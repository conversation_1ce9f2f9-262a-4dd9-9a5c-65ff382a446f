{"name": "tiktok-emoji", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "scrape-unicode": "node scripts/scrape-unicode-full.js", "scrape-unicode-curl": "node scripts/scrape-with-curl.js", "validate-emoji": "node scripts/validate-emoji-data.js"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.303.0", "next": "^14.2.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}