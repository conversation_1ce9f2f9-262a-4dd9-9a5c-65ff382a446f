'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Copy, Check, Info } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface UnicodeEmojiCardProps {
  emoji: string
  name: string
  code: string
  keywords?: string[]
  onCopy?: (emoji: string) => void
  className?: string
}

export function UnicodeEmojiCard({
  emoji,
  name,
  code,
  keywords = [],
  onCopy,
  className
}: UnicodeEmojiCardProps) {
  const [copied, setCopied] = useState(false)
  
  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation()
    navigator.clipboard.writeText(emoji)
    setCopied(true)
    onCopy?.(emoji)
    setTimeout(() => setCopied(false), 2000)
  }
  
  return (
    <Card 
      className={cn(
        "hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer group relative overflow-hidden",
        className
      )}
      onClick={handleCopy}
    >
      <CardContent className="p-4 text-center">
        <div className="text-4xl mb-2 group-hover:scale-110 transition-transform">
          {emoji}
        </div>
        <p className="text-xs font-medium truncate">{name}</p>
        <p className="text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity mt-1">
          {code}
        </p>
        
        {/* Action buttons */}
        <div className="flex gap-1 justify-center mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-7 px-2"
            onClick={handleCopy}
          >
            {copied ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3" />
            )}
          </Button>
          
          {keywords.length > 0 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    className="h-7 px-2"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Info className="w-3 h-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="max-w-xs">
                    <p className="font-semibold mb-1">Keywords:</p>
                    <p className="text-xs">{keywords.join(', ')}</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        
        {/* Hover effect */}
        <div className="absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
      </CardContent>
    </Card>
  )
}