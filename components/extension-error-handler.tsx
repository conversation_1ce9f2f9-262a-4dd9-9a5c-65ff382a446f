"use client"

import { useEffect } from 'react'

export function ExtensionErrorHandler() {
  useEffect(() => {
    // 捕获并忽略来自浏览器扩展的错误
    const handleError = (event: ErrorEvent) => {
      // 检查错误是否来自扩展
      if (
        event.error?.stack?.includes('chrome-extension://') ||
        event.error?.message?.includes('chrome-extension://') ||
        event.filename?.includes('chrome-extension://')
      ) {
        // 阻止错误传播
        event.preventDefault()
        console.warn('Browser extension error caught and ignored:', event.error)
        return true
      }
    }

    // 捕获未处理的Promise rejection
    const handleRejection = (event: PromiseRejectionEvent) => {
      if (
        event.reason?.stack?.includes('chrome-extension://') ||
        event.reason?.message?.includes('chrome-extension://') ||
        String(event.reason).includes('chrome-extension://')
      ) {
        event.preventDefault()
        console.warn('Browser extension promise rejection caught and ignored:', event.reason)
        return true
      }
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleRejection)
    }
  }, [])

  return null
}