"use client"

import Link from 'next/link'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, TrendingUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { trendingEmojis } from '@/lib/data'
import { notFound } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'

interface EmojiDetailClientProps {
  emoji: string
}

export default function EmojiDetailClient({ emoji }: EmojiDetailClientProps) {
  const decodedEmoji = decodeURIComponent(emoji)
  const emojiData = trendingEmojis.find(e => e.char === decodedEmoji)
  
  if (!emojiData) {
    // If not found in trending, create basic data
    const basicEmoji = {
      char: decodedEmoji,
      name: 'Emoji',
      meaning: 'This is an emoji',
      tiktokMeaning: 'Special meaning on TikTok',
      category: 'Emotions',
      usage: '⭐⭐⭐'
    }
    return <EmojiDetailContent emoji={basicEmoji} />
  }
  
  return <EmojiDetailContent emoji={emojiData} />
}

function EmojiDetailContent({ emoji }: { emoji: typeof trendingEmojis[0] }) {
  const { toast } = useToast()
  
  const popularCombos = [
    { combo: `${emoji.char}${emoji.char}${emoji.char}`, meaning: 'Emphasis x3' },
    { combo: `${emoji.char}🤣`, meaning: 'With laughing' },
    { combo: `help${emoji.char}`, meaning: 'Help + emoji' },
    { combo: `not me${emoji.char}`, meaning: 'Not me + emoji' },
  ]
  
  const copyToClipboard = (text: string, type: string = 'Emoji') => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied!",
      description: `${type} copied to clipboard`,
    })
  }
  
  // Mock data for demonstration
  const stats = {
    dailyUsage: '2.3M',
    rank: '#12',
    trend: '+15%',
    emotion: '85% positive'
  }
  
  return (
    <div className="container py-8">
      <Link href="/" className="inline-flex items-center text-muted-foreground hover:text-foreground mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Home
      </Link>
      
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Emoji Display */}
          <Card>
            <CardContent className="pt-8">
              <div className="text-center">
                <div className="text-8xl mb-4">{emoji.char}</div>
                <h1 className="text-3xl font-bold mb-2">{emoji.name}</h1>
                <p className="text-muted-foreground mb-4">{emoji.meaning}</p>
                <Button 
                  size="lg"
                  className="bg-tiktok-pink hover:bg-pink-600"
                  onClick={() => copyToClipboard(emoji.char)}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy Emoji
                </Button>
              </div>
            </CardContent>
          </Card>
          
          {/* TikTok Meaning */}
          {emoji.tiktokMeaning && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span className="text-tiktok-pink">TikTok</span> Special Meaning
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg">{emoji.tiktokMeaning}</p>
              </CardContent>
            </Card>
          )}
          
          {/* Popular Combinations */}
          <Card>
            <CardHeader>
              <CardTitle>Popular Combinations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                {popularCombos.map((combo, index) => (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-3 bg-muted rounded-lg hover:bg-muted/80 cursor-pointer"
                    onClick={() => copyToClipboard(combo.combo, 'Combination')}
                  >
                    <div>
                      <span className="text-lg font-mono">{combo.combo}</span>
                      <p className="text-sm text-muted-foreground">{combo.meaning}</p>
                    </div>
                    <Copy className="w-4 h-4 text-muted-foreground" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Sidebar */}
        <div className="space-y-6">
          {/* Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Usage Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Daily Usage</span>
                  <span className="font-semibold">{stats.dailyUsage}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Popularity Rank</span>
                  <span className="font-semibold">{stats.rank}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Trend</span>
                  <span className="font-semibold text-green-600">{stats.trend}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Emotion</span>
                  <span className="font-semibold">{stats.emotion}</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Category & Tags */}
          <Card>
            <CardHeader>
              <CardTitle>Category & Tags</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Category</p>
                  <Badge variant="secondary">{emoji.category}</Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Usage</p>
                  <div className="flex gap-1">
                    {emoji.usage && emoji.usage.split('').map((star, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Related Emojis */}
          <Card>
            <CardHeader>
              <CardTitle>Related Emojis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-2">
                {trendingEmojis
                  .filter(e => e.category === emoji.category && e.char !== emoji.char)
                  .slice(0, 8)
                  .map((relatedEmoji) => (
                    <Link
                      key={relatedEmoji.char}
                      href={`/emoji/${encodeURIComponent(relatedEmoji.char)}`}
                      className="text-2xl hover:scale-110 transition-transform text-center p-2"
                      title={relatedEmoji.name}
                    >
                      {relatedEmoji.char}
                    </Link>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}