"use client"

import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Copy } from 'lucide-react'
import { cn } from '@/lib/utils'
import { TikTokEmoji } from './tiktok-emoji'

interface EmojiCardProps {
  emoji: string
  code?: string
  name?: string
  usage?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  imageUrl?: string
  description?: string
}

export function EmojiCard({
  emoji,
  code,
  name,
  usage,
  className,
  size = 'md',
  imageUrl,
  description
}: EmojiCardProps) {
  const { toast } = useToast()

  const handleCopy = () => {
    navigator.clipboard.writeText(code || emoji)
    toast({
      title: "Copied to clipboard!",
      description: `${code || emoji} copied successfully`,
    })
  }

  const sizeClasses = {
    sm: 'p-2 sm:p-3',
    md: 'p-3 sm:p-4',
    lg: 'p-4 sm:p-6'
  }

  const emojiSizes = {
    sm: 'text-2xl',
    md: 'text-3xl',
    lg: 'text-4xl'
  }

  return (
    <Card className={cn(
      "relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden",
      "before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100",
      sizeClasses[size],
      className
    )}>
      <div className="relative z-10 text-center">
        <div className="mb-3 flex justify-center">
          <TikTokEmoji
            code={code || ''}
            emoji={emoji}
            imageUrl={imageUrl}
            name={name || ''}
            size={size}
          />
        </div>
        {code && (
          <code className="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">
            {code}
          </code>
        )}
        {name && <p className="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">{name}</p>}
        {usage && <p className="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: {usage}</p>}
        <Button
          size="sm"
          variant="outline"
          className="w-full text-xs sm:text-sm"
          onClick={handleCopy}
        >
          <Copy className="w-3 h-3 mr-1 sm:mr-2" />
          <span className="hidden sm:inline">Copy</span>
          <span className="sm:hidden">Copy</span>
        </Button>
      </div>
    </Card>
  )
}