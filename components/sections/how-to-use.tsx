export function HowToUse() {
  return (
    <section
      className="py-12 md:py-16 bg-gradient-to-b from-muted/40 via-muted/60 to-muted/40"
      aria-labelledby="how-to-use-heading"
    >
      <div className="container">
        <header className="text-center mb-8 md:mb-12">
          <h2 id="how-to-use-heading" className="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-tiktok-pink via-purple-500 to-tiktok-blue bg-clip-text text-transparent">
            How to Use Hidden Emoji Codes
          </h2>
          <p className="text-lg text-muted-foreground max-w-xl mx-auto leading-relaxed">
            Transform your TikTok comments in 3 simple steps and stand out from the crowd
          </p>
        </header>

        <ol className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto" role="list">
          <li className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-tiktok-pink/20 to-purple-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" aria-hidden="true"></div>
            <div className="relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <div className="w-20 h-20 bg-gradient-to-br from-tiktok-pink to-purple-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300" aria-label="Step 1">
                1
              </div>
              <h3 className="text-xl font-semibold mb-4 text-center">Copy the Code</h3>
              <p className="text-muted-foreground text-center leading-relaxed">
                Click the copy button next to any emoji code to copy it to your clipboard instantly
              </p>
            </div>
          </li>

          <li className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-tiktok-blue/20 to-cyan-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" aria-hidden="true"></div>
            <div className="relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <div className="w-20 h-20 bg-gradient-to-br from-tiktok-blue to-cyan-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300" aria-label="Step 2">
                2
              </div>
              <h3 className="text-xl font-semibold mb-4 text-center">Paste in TikTok</h3>
              <p className="text-muted-foreground text-center leading-relaxed">
                Open TikTok and paste the code in any comment or caption field
              </p>
            </div>
          </li>

          <li className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" aria-hidden="true"></div>
            <div className="relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300" aria-label="Step 3">
                3
              </div>
              <h3 className="text-xl font-semibold mb-4 text-center">Watch the Magic</h3>
              <p className="text-muted-foreground text-center leading-relaxed">
                The code automatically transforms into the colorful TikTok emoji
              </p>
            </div>
          </li>
        </ol>

        <div className="mt-8 md:mt-12 text-center">
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200/50 rounded-2xl p-6 max-w-3xl mx-auto">
            <p className="text-sm text-muted-foreground leading-relaxed">
              💡 <strong className="text-yellow-700">Pro Tip:</strong> These emojis only work within the TikTok app. Outside TikTok, they'll appear as regular text codes.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}