import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { trendingEmojis } from '@/lib/data'
import { TrendingUp } from 'lucide-react'

export function TrendingEmojis() {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container">
        <h2 className="text-3xl md:text-4xl font-bold mb-8 flex items-center gap-2">
          <TrendingUp className="w-8 h-8 text-tiktok-blue" />
          Trending on TikTok Today
        </h2>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {trendingEmojis.map((emoji) => (
            <Link key={emoji.char} href={`/emoji/${encodeURIComponent(emoji.char)}`}>
              <Card className="hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full">
                <CardContent className="pt-6 text-center">
                  <div className="text-5xl mb-3">{emoji.char}</div>
                  <h3 className="font-semibold mb-1">{emoji.name}</h3>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {emoji.tiktokMeaning || emoji.meaning}
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}