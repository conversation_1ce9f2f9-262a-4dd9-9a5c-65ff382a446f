"use client"

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { popularCombos } from '@/lib/data'
import { <PERSON>rk<PERSON>, ArrowRight } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

export function PopularCombos() {
  const { toast } = useToast()
  
  const copyEmoji = (emoji: string) => {
    navigator.clipboard.writeText(emoji)
    toast({
      title: "Copied!",
      description: "Emoji combination copied to clipboard",
    })
  }
  
  return (
    <section className="py-20">
      <div className="container">
        <div className="flex flex-col md:flex-row md:items-end justify-between mb-8">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-2 flex items-center gap-2">
              <Sparkles className="w-8 h-8 text-tiktok-pink" />
              Popular Emoji Combinations
            </h2>
            <p className="text-muted-foreground">
              Trending emoji combos that TikT<PERSON><PERSON> love
            </p>
          </div>
          <Link href="/popular-combos" className="hidden md:block">
            <Button variant="ghost" className="gap-2">
              Explore All Combos
              <ArrowRight className="w-4 h-4" />
            </Button>
          </Link>
        </div>
        
        <div className="grid md:grid-cols-2 gap-4">
          {popularCombos.slice(0, 4).map((combo) => (
            <Card key={combo.id} className="hover:shadow-lg transition-all hover:-translate-y-1">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-3xl">{combo.emojis}</span>
                      <CardTitle className="text-lg">{combo.meaning}</CardTitle>
                    </div>
                    <CardDescription>{combo.usage}</CardDescription>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyEmoji(combo.emojis)}
                    className="shrink-0"
                  >
                    Copy
                  </Button>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-8 md:hidden">
          <Link href="/popular-combos">
            <Button variant="outline" className="gap-2">
              Explore All Combinations
              <ArrowRight className="w-4 h-4" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}