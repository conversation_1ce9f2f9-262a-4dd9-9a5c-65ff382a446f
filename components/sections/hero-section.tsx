import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { HeroBackground } from '@/components/hero-background'

export function HeroSection() {
  return (
    <section
      className="relative overflow-hidden bg-gradient-to-br from-pink-50 via-purple-50/30 to-blue-50 py-24 md:py-32"
      aria-label="Hero section introducing TikTok emoji tools"
    >
      <HeroBackground />

      <div className="container relative z-10">
        <header className="text-center max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 bg-gradient-to-r from-tiktok-pink via-purple-500 to-tiktok-blue bg-clip-text text-transparent leading-tight">
            Unlock TikTok's Secret Emojis
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground mb-16 max-w-2xl mx-auto leading-relaxed">
            46 exclusive emoji codes • One-click copy • Used by millions daily • Only work in TikTok app
          </p>

          <nav aria-label="Main navigation to emoji tools" className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/hidden-emojis" aria-describedby="hidden-emojis-desc">
              <Button size="lg" variant="tiktok" className="gap-2 min-w-[200px] shadow-lg hover:shadow-xl transition-all hover:scale-105">
                <span className="text-xl" role="img" aria-label="Target emoji">🎯</span>
                Hidden Emojis
              </Button>
            </Link>
            <span id="hidden-emojis-desc" className="sr-only">Explore TikTok's secret emoji codes</span>

            <Link href="/popular-combos" aria-describedby="popular-combos-desc">
              <Button size="lg" variant="outline" className="gap-2 min-w-[200px] hover:bg-muted/50 transition-all hover:scale-105">
                <span className="text-xl" role="img" aria-label="Fire emoji">🔥</span>
                Popular Combos
              </Button>
            </Link>
            <span id="popular-combos-desc" className="sr-only">Browse trending emoji combinations</span>

            <Link href="/title-generator" aria-describedby="title-generator-desc">
              <Button size="lg" variant="outline" className="gap-2 min-w-[200px] hover:bg-muted/50 transition-all hover:scale-105">
                <span className="text-xl" role="img" aria-label="Sparkles emoji">✨</span>
                Title Generator
              </Button>
            </Link>
            <span id="title-generator-desc" className="sr-only">Generate creative TikTok titles with emojis</span>
          </nav>
        </header>
      </div>
    </section>
  )
}