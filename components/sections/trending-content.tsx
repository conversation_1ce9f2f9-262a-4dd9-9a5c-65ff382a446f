"use client"

import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { popularCombos, trendingEmojis } from '@/lib/data'
import { <PERSON><PERSON><PERSON>, TrendingUp, ArrowRight, Copy, ExternalLink } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

export function TrendingContent() {
  const { toast } = useToast()
  
  const copyEmoji = (emoji: string, type: string) => {
    navigator.clipboard.writeText(emoji)
    toast({
      title: "Copied!",
      description: `${type} copied to clipboard`,
    })
  }
  
  return (
    <section
      className="py-12 md:py-16 bg-gradient-to-b from-muted/20 to-muted/40"
      aria-labelledby="trending-content-heading"
    >
      <div className="container">
        {/* Section Header with Stats */}
        <header className="text-center mb-8 md:mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <TrendingUp className="w-8 h-8 text-tiktok-pink" aria-hidden="true" />
            <h2 id="trending-content-heading" className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">
              What's Trending on TikTok
            </h2>
          </div>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Discover the hottest emoji combinations and trending symbols that TikTokers are using right now
          </p>


        </header>

        {/* Popular Combinations */}
        <div className="mb-8 md:mb-12">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-2">
              <Sparkles className="w-6 h-6 text-tiktok-pink" />
              <h3 className="text-2xl font-bold">Popular Combinations</h3>
              <Badge variant="secondary" className="ml-2">Hot</Badge>
            </div>
            <Link href="/popular-combos" className="hidden md:block">
              <Button variant="ghost" size="sm" className="gap-2">
                View All
                <ArrowRight className="w-4 h-4" />
              </Button>
            </Link>
          </div>
          
          <div className="grid md:grid-cols-2 gap-4 mb-6">
            {popularCombos.slice(0, 8).map((combo) => (
              <Card key={combo.id} className="group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-3xl group-hover:scale-110 transition-transform">{combo.emojis}</span>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <CardTitle className="text-lg">{combo.meaning}</CardTitle>
                            <Badge variant="secondary" className="text-xs">
                              {((combo.popularity || 0) / 1000000).toFixed(1)}M uses
                            </Badge>
                          </div>
                          <CardDescription className="text-sm line-clamp-2">{combo.usage}</CardDescription>
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyEmoji(combo.emojis, 'Combination')}
                      className="shrink-0 gap-2"
                    >
                      <Copy className="w-3 h-3" />
                      Copy
                    </Button>
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>

        {/* Trending Individual Emojis */}
        <div className="mb-8 md:mb-12">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-6 h-6 text-tiktok-blue" />
              <h3 className="text-2xl font-bold">Trending Individual Emojis</h3>
              <Badge variant="outline" className="ml-2">Live</Badge>
            </div>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {trendingEmojis.slice(0, 12).map((emoji) => (
              <Link key={emoji.char} href={`/emoji/${encodeURIComponent(emoji.char)}`}>
                <Card className="group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center">
                  <CardContent className="pt-6 pb-4">
                    <div className="text-4xl mb-3 group-hover:scale-110 transition-transform">{emoji.char}</div>
                    <h4 className="font-semibold text-sm mb-1 line-clamp-1">{emoji.name}</h4>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {emoji.tiktokMeaning || emoji.meaning}
                    </p>
                    <div className="mt-2">
                      <ExternalLink className="w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors" />
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/popular-combos">
              <Button size="lg" variant="default" className="gap-2 min-w-[200px]">
                <Sparkles className="w-4 h-4" />
                Explore All Combinations
              </Button>
            </Link>
            <Link href="/search">
              <Button size="lg" variant="outline" className="gap-2 min-w-[200px]">
                <TrendingUp className="w-4 h-4" />
                Search Trending Emojis
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
