"use client"

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { hiddenEmojis, trendingEmojis } from '@/lib/data'
import { Search, ArrowRight } from 'lucide-react'

export function SearchPreview() {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSearching, setIsSearching] = useState(false)

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setIsSearching(query.length > 0)
    
    if (query.length === 0) {
      setSearchResults([])
      return
    }

    // Search in hidden emojis
    const hiddenResults = hiddenEmojis
      .filter(emoji => 
        emoji.name.toLowerCase().includes(query.toLowerCase()) ||
        emoji.code.toLowerCase().includes(query.toLowerCase()) ||
        (emoji.description && emoji.description.toLowerCase().includes(query.toLowerCase()))
      )
      .slice(0, 3)
      .map(emoji => ({ ...emoji, type: 'hidden' }))

    // Search in trending emojis
    const trendingResults = trendingEmojis
      .filter(emoji => 
        emoji.name.toLowerCase().includes(query.toLowerCase()) ||
        emoji.meaning.toLowerCase().includes(query.toLowerCase()) ||
        (emoji.tiktokMeaning && emoji.tiktokMeaning.toLowerCase().includes(query.toLowerCase()))
      )
      .slice(0, 3)
      .map(emoji => ({ ...emoji, type: 'trending' }))

    setSearchResults([...hiddenResults, ...trendingResults].slice(0, 6))
  }

  return (
    <section 
      className="py-12 bg-gradient-to-b from-background via-muted/10 to-background"
      aria-labelledby="search-preview-heading"
    >
      <div className="container">
        <header className="text-center mb-8">
          <h2 id="search-preview-heading" className="text-3xl md:text-4xl font-bold mb-4 flex items-center justify-center gap-3">
            <Search className="w-8 h-8 text-tiktok-pink" aria-hidden="true" />
            Find Your Perfect Emoji
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Search through our entire collection of hidden codes and trending emojis
          </p>
        </header>

        <div className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" aria-hidden="true" />
            <Input
              type="text"
              placeholder="Search emojis, codes, or meanings..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-12 pr-4 py-6 text-lg border-2 border-muted focus:border-tiktok-pink transition-colors rounded-2xl"
              aria-label="Search for emojis"
            />
          </div>
        </div>

        {/* Search Results */}
        {isSearching && (
          <div className="max-w-4xl mx-auto mb-8">
            {searchResults.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {searchResults.map((result, index) => (
                  <Card 
                    key={`${result.type}-${result.code || result.char}-${index}`}
                    className="hover:shadow-lg transition-all hover:-translate-y-1"
                  >
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-3 mb-3">
                        <span className="text-3xl" role="img" aria-label={result.name}>
                          {result.emoji || result.char}
                        </span>
                        <div className="flex-1">
                          <h3 className="font-semibold text-sm">{result.name}</h3>
                          {result.type === 'hidden' && (
                            <p className="text-xs text-muted-foreground">Code: {result.code}</p>
                          )}
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {result.description || result.meaning || result.tiktokMeaning}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No results found for "{searchQuery}"</p>
                <p className="text-sm text-muted-foreground mt-2">Try searching for emoji names, codes, or meanings</p>
              </div>
            )}
          </div>
        )}

        {/* Popular Searches */}
        {!isSearching && (
          <div className="max-w-3xl mx-auto text-center">
            <h3 className="text-lg font-semibold mb-4 text-muted-foreground">Popular Searches</h3>
            <div className="flex flex-wrap justify-center gap-2">
              {['heart', 'fire', 'star', 'music', 'love', 'party'].map((term) => (
                <Button
                  key={term}
                  variant="outline"
                  size="sm"
                  onClick={() => handleSearch(term)}
                  className="rounded-full hover:bg-tiktok-pink/10 hover:border-tiktok-pink transition-colors"
                >
                  {term}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* CTA */}
        <div className="text-center mt-8">
          <Link href="/search">
            <Button size="lg" className="gap-2 min-w-[200px]">
              Advanced Search
              <ArrowRight className="w-4 h-4" aria-hidden="true" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
