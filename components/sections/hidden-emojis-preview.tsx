"use client"

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { EmojiCard } from '@/components/emoji-card'
import { hiddenEmojis } from '@/lib/data'
import { ArrowRight } from 'lucide-react'

export function HiddenEmojisPreview() {
  return (
    <section
      className="py-12 md:py-16 bg-gradient-to-b from-background to-muted/20"
      aria-labelledby="hidden-emojis-heading"
    >
      <div className="container">
        <header className="flex flex-col md:flex-row md:items-end justify-between mb-8 md:mb-12">
          <div className="max-w-2xl">
            <h2 id="hidden-emojis-heading" className="text-3xl md:text-4xl font-bold mb-4 flex items-center gap-3">
              <span className="text-4xl animate-pulse" role="img" aria-label="Target emoji">🎯</span>
              TikTok Hidden Emoji Codes
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Click copy and paste in TikTok comments to unlock secret emojis that aren't available anywhere else
            </p>
          </div>
          <Link href="/hidden-emojis" className="hidden md:block" aria-label="View all 46 hidden emoji codes">
            <Button variant="ghost" className="gap-2 hover:bg-muted/50 transition-all">
              View All 46 Emojis
              <ArrowRight className="w-4 h-4" aria-hidden="true" />
            </Button>
          </Link>
        </header>

        <div
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4 md:gap-6 mb-8 md:mb-12"
          role="grid"
          aria-label="Preview of hidden emoji codes"
        >
          {hiddenEmojis.slice(0, 16).map((emoji, index) => (
            <div
              key={emoji.code}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 50}ms` }}
              role="gridcell"
            >
              <EmojiCard
                emoji={emoji.emoji}
                code={emoji.code}
                name={emoji.name}
                size="sm"
                imageUrl={emoji.imageUrl}
                description={emoji.description}
              />
            </div>
          ))}
        </div>

        <footer className="text-center md:hidden">
          <Link href="/hidden-emojis" aria-label="View all 46 hidden emoji codes">
            <Button variant="outline" className="gap-2 hover:bg-muted/50 transition-all hover:scale-105">
              View All 46 Hidden Emojis
              <ArrowRight className="w-4 h-4" aria-hidden="true" />
            </Button>
          </Link>
        </footer>
      </div>
    </section>
  )
}