import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { LazyLoad, CategoryCardSkeleton } from '@/components/ui/lazy-load'
import { unicodeCategories } from '@/lib/unicode-data'
import { Grid3X3 } from 'lucide-react'

export function Categories() {
  return (
    <section
      className="py-12 md:py-16 bg-gradient-to-b from-background to-muted/30"
      aria-labelledby="categories-heading"
    >
      <div className="container">
        <header className="text-center mb-8 md:mb-12">
          <h2 id="categories-heading" className="text-3xl md:text-4xl font-bold mb-6 flex items-center justify-center gap-3">
            <Grid3X3 className="w-8 h-8 text-tiktok-pink" aria-hidden="true" />
            Browse by Category
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Explore over 1,300 Unicode emojis organized by category. Find the perfect emoji for every mood and occasion.
          </p>
        </header>

        <div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
          role="grid"
          aria-label="Emoji categories"
        >
          {unicodeCategories.map((category, index) => (
            <LazyLoad
              key={category.id}
              fallback={<CategoryCardSkeleton />}
              rootMargin="100px"
            >
              <Link
                href={`/category/${category.id}`}
                aria-label={`Browse ${category.name} emojis - ${category.count} available`}
              >
                <Card
                  className="hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                  role="gridcell"
                >
                  <CardContent className="pt-8 pb-8">
                    <div className="flex items-start justify-between mb-6">
                      <div
                        className="text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12"
                        role="img"
                        aria-label={`${category.name} category icon`}
                      >
                        {category.icon}
                      </div>
                      <div
                        className="text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors"
                        aria-label={`${category.count} emojis in this category`}
                      >
                        {category.count}
                      </div>
                    </div>
                    <h3 className="font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors">{category.name}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                      {category.description}
                    </p>
                  </CardContent>
                </Card>
              </Link>
            </LazyLoad>
          ))}
        </div>
      </div>
    </section>
  )
}