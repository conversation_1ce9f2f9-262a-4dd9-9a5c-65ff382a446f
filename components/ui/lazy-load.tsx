"use client"

import { useState, useEffect, useRef, ReactNode } from 'react'

interface LazyLoadProps {
  children: ReactNode
  fallback?: ReactNode
  rootMargin?: string
  threshold?: number
  className?: string
}

export function LazyLoad({ 
  children, 
  fallback = null, 
  rootMargin = '50px',
  threshold = 0.1,
  className 
}: LazyLoadProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true)
          setHasLoaded(true)
          // Disconnect observer after first load
          observer.disconnect()
        }
      },
      {
        rootMargin,
        threshold,
      }
    )

    const currentElement = elementRef.current
    if (currentElement) {
      observer.observe(currentElement)
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement)
      }
    }
  }, [rootMargin, threshold, hasLoaded])

  return (
    <div ref={elementRef} className={className}>
      {isVisible ? children : fallback}
    </div>
  )
}

// Skeleton components for fallbacks
export function EmojiCardSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="bg-muted rounded-lg p-4 space-y-3">
        <div className="w-12 h-12 bg-muted-foreground/20 rounded-full mx-auto"></div>
        <div className="space-y-2">
          <div className="h-3 bg-muted-foreground/20 rounded w-3/4 mx-auto"></div>
          <div className="h-2 bg-muted-foreground/20 rounded w-1/2 mx-auto"></div>
        </div>
      </div>
    </div>
  )
}

export function CategoryCardSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="bg-muted rounded-2xl p-8 space-y-4">
        <div className="flex items-start justify-between">
          <div className="w-16 h-16 bg-muted-foreground/20 rounded-full"></div>
          <div className="w-8 h-6 bg-muted-foreground/20 rounded"></div>
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-muted-foreground/20 rounded w-3/4"></div>
          <div className="h-3 bg-muted-foreground/20 rounded w-full"></div>
          <div className="h-3 bg-muted-foreground/20 rounded w-2/3"></div>
        </div>
      </div>
    </div>
  )
}

export function TrendingCardSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="bg-muted rounded-lg p-6 space-y-4">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-muted-foreground/20 rounded-full"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-muted-foreground/20 rounded w-3/4"></div>
            <div className="h-3 bg-muted-foreground/20 rounded w-1/2"></div>
          </div>
          <div className="w-16 h-8 bg-muted-foreground/20 rounded"></div>
        </div>
      </div>
    </div>
  )
}
