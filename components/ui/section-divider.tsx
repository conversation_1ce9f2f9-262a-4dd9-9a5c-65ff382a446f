import { cn } from '@/lib/utils'

interface SectionDividerProps {
  className?: string
  variant?: 'default' | 'gradient' | 'dots'
}

export function SectionDivider({ className, variant = 'default' }: SectionDividerProps) {
  if (variant === 'gradient') {
    return (
      <div className={cn("relative h-px my-8", className)}>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted-foreground/20 to-transparent" />
      </div>
    )
  }
  
  if (variant === 'dots') {
    return (
      <div className={cn("flex items-center justify-center gap-2 my-8", className)}>
        <div className="w-1 h-1 rounded-full bg-muted-foreground/30" />
        <div className="w-1.5 h-1.5 rounded-full bg-muted-foreground/40" />
        <div className="w-2 h-2 rounded-full bg-muted-foreground/50" />
        <div className="w-1.5 h-1.5 rounded-full bg-muted-foreground/40" />
        <div className="w-1 h-1 rounded-full bg-muted-foreground/30" />
      </div>
    )
  }
  
  return <div className={cn("h-px bg-border my-8", className)} />
}