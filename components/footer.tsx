import Link from 'next/link'
import { Button } from '@/components/ui/button'

export function Footer() {
  return (
    <footer className="mt-20 border-t bg-muted/30">
      <div className="container py-12">
        <div className="grid gap-8 md:grid-cols-4">
          <div className="md:col-span-2">
            <div className="flex items-center gap-2 mb-4">
              <span className="text-2xl">🎵</span>
              <span className="font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">
                TikTok Emoji
              </span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Your ultimate resource for TikTok hidden emojis, creative combinations, and emoji trends. 
              Make your TikTok comments more expressive and engaging!
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/hidden-emojis" className="text-muted-foreground hover:text-foreground transition-colors">
                  Hidden Emojis
                </Link>
              </li>
              <li>
                <Link href="/popular-combos" className="text-muted-foreground hover:text-foreground transition-colors">
                  Popular Combos
                </Link>
              </li>
              <li>
                <Link href="/title-generator" className="text-muted-foreground hover:text-foreground transition-colors">
                  Title Generator
                </Link>
              </li>
              <li>
                <Link href="/search" className="text-muted-foreground hover:text-foreground transition-colors">
                  Search Emojis
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Categories</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/category/smileys-emotion" className="text-muted-foreground hover:text-foreground transition-colors">
                  Smileys & Emotion
                </Link>
              </li>
              <li>
                <Link href="/category/people-body" className="text-muted-foreground hover:text-foreground transition-colors">
                  People & Body
                </Link>
              </li>
              <li>
                <Link href="/category/animals-nature" className="text-muted-foreground hover:text-foreground transition-colors">
                  Animals & Nature
                </Link>
              </li>
              <li>
                <Link href="/category/food-drink" className="text-muted-foreground hover:text-foreground transition-colors">
                  Food & Drink
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 pt-8 border-t text-center text-sm text-muted-foreground">
          <p>© 2024 TikTok Emoji. Not affiliated with TikTok or ByteDance.</p>
          <p className="mt-2">
            Made with 💕 for the TikTok community
          </p>
        </div>
      </div>
    </footer>
  )
}