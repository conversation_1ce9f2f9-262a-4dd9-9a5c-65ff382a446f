'use client'

import { useState } from 'react'
import Image from 'next/image'

interface TikTokEmojiProps {
  code: string
  emoji: string
  imageUrl?: string
  name: string
  size?: 'sm' | 'md' | 'lg'
  showFallback?: boolean
}

export function TikTokEmoji({ 
  code, 
  emoji, 
  imageUrl, 
  name, 
  size = 'md',
  showFallback = true 
}: TikTokEmojiProps) {
  const [imageError, setImageError] = useState(false)
  
  const sizeClasses = {
    sm: 'w-8 h-8 text-2xl',
    md: 'w-12 h-12 text-4xl',
    lg: 'w-16 h-16 text-6xl'
  }

  // If there's a TikTok exclusive image URL and the image loads successfully, show TikTok exclusive emoji
  if (imageUrl && !imageError) {
    return (
      <div className={`${sizeClasses[size]} relative flex items-center justify-center`}>
        <Image
          src={imageUrl}
          alt={`TikTok ${name} emoji`}
          width={size === 'sm' ? 32 : size === 'md' ? 48 : 64}
          height={size === 'sm' ? 32 : size === 'md' ? 48 : 64}
          className="rounded-full"
          onError={() => setImageError(true)}
          unoptimized // Because it's an external image URL
        />
      </div>
    )
  }

  // If TikTok image fails to load or doesn't exist, show fallback emoji
  if (showFallback) {
    return (
      <div className={`${sizeClasses[size]} flex items-center justify-center`}>
        <span className="select-none">{emoji}</span>
      </div>
    )
  }

  // If not showing fallback, show the code
  return (
    <div className={`${sizeClasses[size]} flex items-center justify-center bg-gray-100 rounded-full text-xs font-mono text-gray-600`}>
      {code}
    </div>
  )
}
