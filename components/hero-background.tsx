export function HeroBackground() {
  return (
    <>
      {/* Simplified animated background with fewer elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Main gradient orbs - reduced from 5 to 2 */}
        <div className="absolute top-[10%] left-[15%] w-64 h-64 bg-gradient-to-br from-tiktok-pink/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-[10%] right-[15%] w-64 h-64 bg-gradient-to-br from-tiktok-blue/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse animation-delay-2000"></div>
      </div>
      
      {/* Floating emojis - reduced from 8 to 5 key ones */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-[15%] left-[10%] text-6xl opacity-20 animate-float">🎵</div>
        <div className="absolute top-[20%] right-[15%] text-5xl opacity-25 animate-float animation-delay-1000">✨</div>
        <div className="absolute bottom-[25%] left-[20%] text-5xl opacity-20 animate-float animation-delay-2000">💕</div>
        <div className="absolute bottom-[20%] right-[10%] text-6xl opacity-25 animate-float animation-delay-1500">🔥</div>
        <div className="absolute top-[50%] left-[50%] -translate-x-1/2 -translate-y-1/2 text-5xl opacity-20 animate-float animation-delay-2500">😍</div>
      </div>
    </>
  )
}