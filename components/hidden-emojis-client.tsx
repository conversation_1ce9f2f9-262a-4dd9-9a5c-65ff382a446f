"use client"

import { useState } from 'react'
import Link from 'next/link'
import { ArrowLeft, Search } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { EmojiCard } from '@/components/emoji-card'
import { hiddenEmojis } from '@/lib/data'
import { Card, CardContent } from '@/components/ui/card'

export default function HiddenEmojisClient() {
  const [searchQuery, setSearchQuery] = useState('')

  const filteredEmojis = hiddenEmojis.filter(emoji => 
    emoji.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
    emoji.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="container py-8">
      <Link href="/" className="inline-flex items-center text-muted-foreground hover:text-foreground mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Home
      </Link>
      
      <h1 className="text-4xl font-bold mb-8">TikTok Hidden Emoji Codes</h1>
      
      <Card className="mb-8 bg-amber-50 border-amber-200">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <span className="text-2xl">💡</span>
            <div>
              <p className="font-semibold mb-1">How to use:</p>
              <p className="text-sm">Type the code in square brackets in TikTok comments, like [smile], and it will display the corresponding hidden emoji. These emojis are only visible on TikTok!</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="mb-8">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            type="search"
            placeholder="Search hidden emojis..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-12">
        {filteredEmojis.map((emoji) => (
          <EmojiCard
            key={emoji.code}
            emoji={emoji.emoji}
            code={emoji.code}
            name={emoji.name}
            usage={emoji.usage}
            imageUrl={emoji.imageUrl}
            description={emoji.description}
          />
        ))}
      </div>
      
      {filteredEmojis.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No matching emojis found</p>
        </div>
      )}
      
      <Card className="bg-gradient-to-br from-pink-50 to-blue-50 border-pink-200 mb-8">
        <CardContent className="pt-6">
          <h3 className="text-xl font-bold mb-6 text-center">📚 TikTok Emoji Meanings</h3>
          <p className="text-sm text-muted-foreground mb-6 text-center">
            Complete list of TikTok hidden emoji meanings and descriptions
          </p>

          <div className="grid gap-3 max-h-96 overflow-y-auto">
            {hiddenEmojis.map((emoji) => (
              <div key={emoji.code} className="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50">
                <div className="flex-shrink-0">
                  <span className="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">
                    {emoji.code}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-semibold text-sm">{emoji.name}</span>
                    {emoji.usage && (
                      <span className="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">
                        {emoji.usage} uses
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground leading-relaxed">
                    {emoji.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-muted">
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">🎯 Pro Tips</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• These hidden emojis are only visible within TikTok - they'll appear as text codes on other platforms</li>
            <li>• You can combine multiple hidden emojis, like [happy][happy][happy] for super happy</li>
            <li>• Some hidden emojis may display slightly differently in different regions</li>
            <li>• Bookmark this page for easy access whenever you need these codes</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}