"use client"

import { useEffect, useState } from 'react'

// Declare gtag as global variable to avoid TypeScript errors
declare global {
  var gtag: (command: string, action: string, options?: any) => void
}

interface PerformanceMetrics {
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  fcp?: number // First Contentful Paint
  ttfb?: number // Time to First Byte
}

export function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production and if performance API is available
    if (process.env.NODE_ENV !== 'production' || typeof window === 'undefined') {
      return
    }

    const metrics: PerformanceMetrics = {}

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // LCP - Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1] as any
            metrics.lcp = lastEntry.startTime
          })
          lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true })

          // FID - First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              metrics.fid = entry.processingStart - entry.startTime
            })
          })
          fidObserver.observe({ type: 'first-input', buffered: true })

          // CLS - Cumulative Layout Shift
          let clsValue = 0
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value
                metrics.cls = clsValue
              }
            })
          })
          clsObserver.observe({ type: 'layout-shift', buffered: true })

        } catch (error) {
          console.warn('Performance monitoring failed:', error)
        }
      }

      // Navigation Timing API for other metrics
      if ('performance' in window && 'getEntriesByType' in performance) {
        const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[]
        if (navigationEntries.length > 0) {
          const navigation = navigationEntries[0]
          
          // TTFB - Time to First Byte
          metrics.ttfb = navigation.responseStart - navigation.requestStart

          // FCP - First Contentful Paint
          const paintEntries = performance.getEntriesByType('paint')
          const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
          if (fcpEntry) {
            metrics.fcp = fcpEntry.startTime
          }
        }
      }
    }

    // Measure performance after page load
    if (document.readyState === 'complete') {
      measureWebVitals()
    } else {
      window.addEventListener('load', measureWebVitals)
    }

    // Send metrics after a delay to capture all measurements
    const sendMetrics = () => {
      // In a real app, you would send these to your analytics service
      console.log('Performance Metrics:', metrics)
      
      // Example: Send to Google Analytics 4
      if (typeof gtag !== 'undefined') {
        Object.entries(metrics).forEach(([metric, value]) => {
          if (value !== undefined) {
            gtag('event', 'web_vital', {
              name: metric,
              value: Math.round(metric === 'cls' ? value * 1000 : value),
              event_category: 'Web Vitals',
            })
          }
        })
      }
    }

    const timeoutId = setTimeout(sendMetrics, 5000)

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('load', measureWebVitals)
    }
  }, [])

  // This component doesn't render anything
  return null
}

// Utility function to get performance grade
export function getPerformanceGrade(metrics: PerformanceMetrics): string {
  let score = 100

  // LCP scoring (Good: <2.5s, Needs Improvement: 2.5-4s, Poor: >4s)
  if (metrics.lcp) {
    if (metrics.lcp > 4000) score -= 30
    else if (metrics.lcp > 2500) score -= 15
  }

  // FID scoring (Good: <100ms, Needs Improvement: 100-300ms, Poor: >300ms)
  if (metrics.fid) {
    if (metrics.fid > 300) score -= 25
    else if (metrics.fid > 100) score -= 10
  }

  // CLS scoring (Good: <0.1, Needs Improvement: 0.1-0.25, Poor: >0.25)
  if (metrics.cls) {
    if (metrics.cls > 0.25) score -= 25
    else if (metrics.cls > 0.1) score -= 10
  }

  // FCP scoring (Good: <1.8s, Needs Improvement: 1.8-3s, Poor: >3s)
  if (metrics.fcp) {
    if (metrics.fcp > 3000) score -= 20
    else if (metrics.fcp > 1800) score -= 10
  }

  if (score >= 90) return 'A'
  if (score >= 80) return 'B'
  if (score >= 70) return 'C'
  if (score >= 60) return 'D'
  return 'F'
}

// Hook for using performance metrics in components
export function usePerformanceMetrics() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({})

  useEffect(() => {
    // Implementation would be similar to PerformanceMonitor
    // but would update state instead of just logging
  }, [])

  return metrics
}
