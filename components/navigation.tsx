"use client"

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Search, Grid3X3, ChevronDown } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { unicodeCategories } from '@/lib/unicode-data'

export function Navigation() {
  const pathname = usePathname()
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <Link href="/" className="flex items-center space-x-2 flex-shrink-0">
          <span className="text-2xl">🎵</span>
          <span className="font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">
            TikTok Emoji
          </span>
        </Link>

        {/* Centered Search Form */}
        <form onSubmit={handleSearch} className="relative flex-1 max-w-2xl mx-8">
          <Input
            type="search"
            placeholder="Search emojis, codes, or meanings..."
            className="pr-10 w-full"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Button
            type="submit"
            size="icon"
            variant="ghost"
            className="absolute right-0 top-0"
          >
            <Search className="h-4 w-4" />
          </Button>
        </form>

        {/* Categories Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="gap-2 flex-shrink-0">
              <Grid3X3 className="h-4 w-4" />
              Categories
              <ChevronDown className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-64">
            {unicodeCategories.map((category) => (
              <DropdownMenuItem
                key={category.id}
                asChild
                className="cursor-pointer"
              >
                <Link href={`/category/${category.id}`} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{category.icon}</span>
                    <div>
                      <div className="font-medium">{category.name}</div>
                      <div className="text-xs text-muted-foreground">{category.zhName}</div>
                    </div>
                  </div>
                  <span className="text-sm text-muted-foreground">{category.count}</span>
                </Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}