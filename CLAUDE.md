# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Core Development Commands
- `npm run dev` - Start development server on http://localhost:3000
- `npm run build` - Build for production (Next.js optimized build)
- `npm run start` - Run production server
- `npm run lint` - Run ESLint for code quality checks

### Data Scripts
- `npm run scrape-unicode` - Scrape Unicode emoji data
- `npm run scrape-unicode-curl` - Alternative Unicode scraper using curl
- `npm run validate-emoji` - Validate emoji data integrity

### Important Notes
- No test commands are configured - project lacks testing infrastructure
- No type-check script exists despite using TypeScript (tsconfig.json is strict)
- All commands use npm, no yarn/pnpm lockfiles present
- The `.next` directory is improperly tracked in git (should be in .gitignore)

## High-Level Architecture

### Technology Stack
- **Framework**: Next.js 14 with App Router (React 18)
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS + shadcn/ui components
- **State**: React hooks only (no Redux/Zustand)
- **Data**: Static data in `/lib/data.ts` (no API/database)

### Project Structure
```
/app/              # Next.js App Router pages (not /pages)
  ├── layout.tsx   # Root layout with navigation
  ├── page.tsx     # Homepage
  └── [routes]/    # Feature pages (hidden-emojis, emoji/[emoji], etc.)

/components/       # Reusable components
  ├── ui/         # shadcn/ui components (pre-configured)
  └── *.tsx       # Custom components (navigation, emoji-card)

/lib/             # Core utilities and data
  ├── data.ts     # All emoji data (46 hidden codes, combinations, etc.)
  ├── types.ts    # TypeScript interfaces
  └── utils.ts    # cn() helper for className merging
```

### Key Architecture Decisions

1. **Static Data Approach**: All emoji data is hardcoded in `/lib/data.ts` - no external APIs or database. This includes:
   - 46 TikTok hidden emoji codes
   - Popular emoji combinations
   - Emoji metadata and usage statistics

2. **Dynamic Routes**: Uses Next.js dynamic routes for emoji details (`/emoji/[emoji]/page.tsx`)

3. **Tailwind Configuration**: Extensive custom configuration in `tailwind.config.ts`:
   - Custom TikTok brand colors (pink: #fe2c55, blue: #25f4ee)
   - Custom animations: float, pulse, morph, premium effects
   - Dark mode support (class-based, not yet implemented)

4. **Component Library**: Uses shadcn/ui (not installed via npm, copied to `/components/ui/`)

5. **Path Aliases**: `@/*` maps to root directory (configured in tsconfig.json)

### Critical Implementation Details

- **No Environment Variables**: Project doesn't use .env files
- **English-First Content**: Primary language is English for international users
- **Mobile-First Design**: Responsive breakpoints configured for TikTok's mobile audience
- **SEO Optimization**: Leverages Next.js App Router for better SEO
- **No Authentication**: Public-facing website with no user accounts
- **No Backend API**: All functionality is client-side or SSG

### Development Patterns

1. When adding new emojis or data, update `/lib/data.ts`
2. Follow existing component patterns in `/components/ui/`
3. Use `cn()` utility from `/lib/utils.ts` for conditional classes
4. Maintain TikTok brand colors and glassmorphism design
5. Keep pages under `/app/` directory (App Router pattern)

### Routes and Features

- `/` - Homepage with hero section and feature cards
- `/hidden-emojis` - List of 46 hidden TikTok emoji codes
- `/emoji/[emoji]` - Dynamic route for individual emoji details
- `/title-generator` - AI-style title generation with emojis
- `/search` - Global emoji search functionality
- `/popular-combos` - Popular emoji combinations display
- `/category/[category]` - Browse emojis by category

### API Endpoints

- `/pages/api/unicode-emojis.js` - Unicode emoji data API
  - Query parameters: search, category, format (json/csv/simple), page, limit
  - Returns paginated emoji data with metadata

### TikTok Emoji Component

The custom `TikTokEmoji` component (`/components/tiktok-emoji.tsx`) handles:
- Display of TikTok-exclusive emoji images from external URLs
- Automatic fallback to standard emojis when images fail to load
- Three size variants: sm (32px), md (48px), lg (64px)
- Uses Next.js Image component with `unoptimized` flag for external images

### Unicode Emoji Integration

The project includes Unicode emoji support:
- `/lib/unicode-data.ts` - Unicode emoji data structures
- `/data/unicode-emojis-*.json` - Generated Unicode emoji datasets
- `/pages/api/unicode-emojis.js` - API endpoint for Unicode emoji data
- Various scraping scripts in `/scripts/` for data collection

### Internationalization Status

**IMPORTANT**: The codebase contains mixed Chinese/English content:
- 20+ files contain Chinese text (documented in `chinese-text-files.md`)
- No i18n system implemented despite international target audience
- Primary data in `/lib/data.ts` contains Chinese emoji names and descriptions
- UI components have Chinese labels and navigation items
- Consider implementing proper i18n before production deployment

### Critical Issues to Address

1. **Build Directory in Git**: The `.next` directory is tracked in version control (not in .gitignore)
   - This is highly unusual and should be fixed
   - Add `.next/` to .gitignore file

2. **Missing Development Tools**:
   - No ESLint configuration (despite lint script)
   - No type-check script (mentioned in README but missing)
   - No testing framework
   - No pre-commit hooks

3. **Path Structure Note**: Project uses both App Router (`/app`) and Pages Router (`/pages/api`)
   - Keep API routes in `/pages/api/` for now
   - All UI pages should use `/app/` directory

### Data Attribution

- Hidden emoji codes sourced from Emojipedia.org
- All 46 codes are hardcoded in `/lib/data.ts`
- Images loaded from `https://img.alltiktokemojis.com/tiktok-emojis/`

### Unused Assets

- `/ui/` directory contains static HTML prototypes - not used in the Next.js app
- These are design mockups and can be ignored for development

### Scripts Directory

The `/scripts/` directory contains various data collection and validation tools:
- `scrape-unicode-full.js` - Main Unicode emoji scraper
- `scrape-with-curl.js` - Alternative scraper using curl
- `validate-emoji-data.js` - Validates emoji data integrity
- Multiple other scrapers for different emoji sources (Apple, Google, etc.)
- These scripts generate data files in `/data/` directory