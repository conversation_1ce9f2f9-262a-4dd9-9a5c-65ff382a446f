/**
 * 浏览器端 Unicode.org Emoji 数据提取脚本
 * 在浏览器控制台中运行此脚本来提取emoji数据
 */

(function() {
  'use strict';
  
  console.log('开始提取 Unicode.org emoji 数据...');
  
  const emojis = [];
  let currentCategory = '';
  let currentSubcategory = '';
  
  // 查找所有表格行
  const rows = document.querySelectorAll('table tr');
  console.log(`找到 ${rows.length} 行数据`);
  
  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    
    // 检查是否是分类标题行
    const categoryHeader = row.querySelector('th[colspan]');
    if (categoryHeader) {
      const categoryText = categoryHeader.textContent.trim();
      if (categoryText && !categoryText.includes('№') && !categoryText.includes('Code')) {
        currentCategory = cleanCategoryName(categoryText);
        console.log(`发现分类: ${currentCategory}`);
        continue;
      }
    }
    
    // 检查是否是子分类行
    const subcategoryCell = row.querySelector('td[colspan]');
    if (subcategoryCell) {
      currentSubcategory = cleanCategoryName(subcategoryCell.textContent.trim());
      console.log(`  子分类: ${currentSubcategory}`);
      continue;
    }
    
    // 提取emoji数据行
    const cells = row.querySelectorAll('td');
    if (cells.length >= 8) {
      const emojiData = extractEmojiData(cells, currentCategory, currentSubcategory);
      if (emojiData) {
        emojis.push(emojiData);
        if (emojis.length % 100 === 0) {
          console.log(`已提取 ${emojis.length} 个emoji...`);
        }
      }
    }
  }
  
  function extractEmojiData(cells, category, subcategory) {
    try {
      // 第1列: 序号
      const number = cells[0]?.textContent?.trim();
      
      // 第2列: Unicode代码
      const codeCell = cells[1];
      const codeLink = codeCell?.querySelector('a');
      const unicodeCode = codeLink?.textContent?.trim();
      
      // 第3列: 浏览器显示的emoji
      const browserEmoji = cells[2]?.textContent?.trim();
      
      // 第8列: CLDR Short Name (最后一列)
      const cldrName = cells[cells.length - 1]?.textContent?.trim();
      
      // 验证必要字段
      if (!unicodeCode || !browserEmoji || !cldrName) {
        return null;
      }
      
      // 清理和格式化数据
      const emojiData = {
        number: parseInt(number) || 0,
        code: unicodeCode,
        emoji: browserEmoji,
        name: cldrName,
        category: category || 'Unknown',
        subcategory: subcategory || '',
        // 提取Unicode码点
        codepoints: extractCodepoints(unicodeCode),
        // 生成搜索关键词
        keywords: generateKeywords(cldrName, category, subcategory)
      };
      
      return emojiData;
      
    } catch (error) {
      console.warn('提取emoji数据时出错:', error);
      return null;
    }
  }
  
  function extractCodepoints(unicodeCode) {
    // 从 "U+1F600" 或 "U+1F1E6 U+1F1E8" 格式提取码点
    const matches = unicodeCode.match(/U\+([0-9A-F]+)/g);
    if (matches) {
      return matches.map(match => match.replace('U+', '').toLowerCase());
    }
    return [];
  }
  
  function generateKeywords(name, category, subcategory) {
    const keywords = new Set();
    
    // 添加名称中的单词
    if (name) {
      name.toLowerCase().split(/[\s-_]+/).forEach(word => {
        if (word.length > 1) keywords.add(word);
      });
    }
    
    // 添加分类关键词
    if (category) {
      category.toLowerCase().split(/[\s&-_]+/).forEach(word => {
        if (word.length > 1) keywords.add(word);
      });
    }
    
    if (subcategory) {
      subcategory.toLowerCase().split(/[\s-_]+/).forEach(word => {
        if (word.length > 1) keywords.add(word);
      });
    }
    
    return Array.from(keywords);
  }
  
  function cleanCategoryName(text) {
    return text
      .replace(/^\d+\.\s*/, '') // 移除开头的数字
      .replace(/\s+/g, ' ')     // 标准化空格
      .trim();
  }
  
  function getCategoryStats(emojis) {
    const stats = {};
    emojis.forEach(emoji => {
      if (!stats[emoji.category]) {
        stats[emoji.category] = 0;
      }
      stats[emoji.category]++;
    });
    return stats;
  }
  
  // 完成提取
  console.log(`\n=== 提取完成 ===`);
  console.log(`总emoji数量: ${emojis.length}`);
  console.log('分类统计:', getCategoryStats(emojis));
  
  // 创建最终数据结构
  const finalData = {
    metadata: {
      source: 'https://unicode.org/emoji/charts/full-emoji-list.html',
      version: '16.0',
      scraped_at: new Date().toISOString(),
      total_count: emojis.length
    },
    categories: getCategoryStats(emojis),
    emojis: emojis
  };
  
  // 创建简化版本
  const simplifiedData = {
    metadata: finalData.metadata,
    emojis: emojis.map(emoji => ({
      code: emoji.code,
      emoji: emoji.emoji,
      name: emoji.name,
      category: emoji.category,
      keywords: emoji.keywords.slice(0, 5)
    }))
  };
  
  // 输出数据供下载
  console.log('\n=== 数据已准备完成 ===');
  console.log('完整数据可通过 window.unicodeEmojiData 访问');
  console.log('简化数据可通过 window.unicodeEmojiDataSimplified 访问');
  console.log('\n要下载数据，请运行:');
  console.log('downloadJSON(window.unicodeEmojiData, "unicode-emojis.json")');
  console.log('downloadJSON(window.unicodeEmojiDataSimplified, "unicode-emojis-simplified.json")');
  
  // 将数据存储到全局变量
  window.unicodeEmojiData = finalData;
  window.unicodeEmojiDataSimplified = simplifiedData;
  
  // 提供下载函数
  window.downloadJSON = function(data, filename) {
    const jsonStr = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log(`${filename} 下载完成`);
  };
  
  // 显示前几个emoji作为示例
  console.log('\n=== 前10个emoji示例 ===');
  emojis.slice(0, 10).forEach((emoji, index) => {
    console.log(`${index + 1}. ${emoji.emoji} ${emoji.code} - ${emoji.name} (${emoji.category})`);
  });
  
})();
