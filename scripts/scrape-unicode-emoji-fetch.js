const https = require('https');
const fs = require('fs').promises;
const path = require('path');

// Manual emoji data extraction - first batch of common emojis
// Since the Unicode page is too large, we'll extract key emojis manually
const unicodeEmojis = [
  // Smileys & Emotion - face-smiling
  { code: "U+1F600", emoji: "😀", name: "grinning face", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F603", emoji: "😃", name: "grinning face with big eyes", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F604", emoji: "😄", name: "grinning face with smiling eyes", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F601", emoji: "😁", name: "beaming face with smiling eyes", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F606", emoji: "😆", name: "grinning squinting face", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F605", emoji: "😅", name: "grinning face with sweat", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F923", emoji: "🤣", name: "rolling on the floor laughing", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F602", emoji: "😂", name: "face with tears of joy", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F642", emoji: "🙂", name: "slightly smiling face", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F643", emoji: "🙃", name: "upside-down face", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1FAE0", emoji: "🫠", name: "melting face", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F609", emoji: "😉", name: "winking face", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F60A", emoji: "😊", name: "smiling face with smiling eyes", category: "Smileys & Emotion", subcategory: "face-smiling" },
  { code: "U+1F607", emoji: "😇", name: "smiling face with halo", category: "Smileys & Emotion", subcategory: "face-smiling" },
  
  // face-affection
  { code: "U+1F970", emoji: "🥰", name: "smiling face with hearts", category: "Smileys & Emotion", subcategory: "face-affection" },
  { code: "U+1F60D", emoji: "😍", name: "smiling face with heart-eyes", category: "Smileys & Emotion", subcategory: "face-affection" },
  { code: "U+1F929", emoji: "🤩", name: "star-struck", category: "Smileys & Emotion", subcategory: "face-affection" },
  { code: "U+1F618", emoji: "😘", name: "face blowing a kiss", category: "Smileys & Emotion", subcategory: "face-affection" },
  { code: "U+1F617", emoji: "😗", name: "kissing face", category: "Smileys & Emotion", subcategory: "face-affection" },
  { code: "U+263A", emoji: "☺️", name: "smiling face", category: "Smileys & Emotion", subcategory: "face-affection" },
  { code: "U+1F61A", emoji: "😚", name: "kissing face with closed eyes", category: "Smileys & Emotion", subcategory: "face-affection" },
  { code: "U+1F619", emoji: "😙", name: "kissing face with smiling eyes", category: "Smileys & Emotion", subcategory: "face-affection" },
  { code: "U+1F972", emoji: "🥲", name: "smiling face with tear", category: "Smileys & Emotion", subcategory: "face-affection" },
  
  // face-tongue
  { code: "U+1F60B", emoji: "😋", name: "face savoring food", category: "Smileys & Emotion", subcategory: "face-tongue" },
  { code: "U+1F61B", emoji: "😛", name: "face with tongue", category: "Smileys & Emotion", subcategory: "face-tongue" },
  { code: "U+1F61C", emoji: "😜", name: "winking face with tongue", category: "Smileys & Emotion", subcategory: "face-tongue" },
  { code: "U+1F92A", emoji: "🤪", name: "zany face", category: "Smileys & Emotion", subcategory: "face-tongue" },
  { code: "U+1F61D", emoji: "😝", name: "squinting face with tongue", category: "Smileys & Emotion", subcategory: "face-tongue" },
  { code: "U+1F911", emoji: "🤑", name: "money-mouth face", category: "Smileys & Emotion", subcategory: "face-tongue" },
  
  // face-hand
  { code: "U+1F917", emoji: "🤗", name: "smiling face with open hands", category: "Smileys & Emotion", subcategory: "face-hand" },
  { code: "U+1F92D", emoji: "🤭", name: "face with hand over mouth", category: "Smileys & Emotion", subcategory: "face-hand" },
  { code: "U+1FAE2", emoji: "🫢", name: "face with open eyes and hand over mouth", category: "Smileys & Emotion", subcategory: "face-hand" },
  { code: "U+1FAE3", emoji: "🫣", name: "face with peeking eye", category: "Smileys & Emotion", subcategory: "face-hand" },
  { code: "U+1F92B", emoji: "🤫", name: "shushing face", category: "Smileys & Emotion", subcategory: "face-hand" },
  { code: "U+1F914", emoji: "🤔", name: "thinking face", category: "Smileys & Emotion", subcategory: "face-hand" },
  { code: "U+1FAE1", emoji: "🫡", name: "saluting face", category: "Smileys & Emotion", subcategory: "face-hand" },
  
  // face-neutral-skeptical
  { code: "U+1F910", emoji: "🤐", name: "zipper-mouth face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F928", emoji: "🤨", name: "face with raised eyebrow", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F610", emoji: "😐", name: "neutral face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F611", emoji: "😑", name: "expressionless face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F636", emoji: "😶", name: "face without mouth", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1FAE5", emoji: "🫥", name: "dotted line face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F636-200D-1F32B", emoji: "😶‍🌫️", name: "face in clouds", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F60F", emoji: "😏", name: "smirking face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F612", emoji: "😒", name: "unamused face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F644", emoji: "🙄", name: "face with rolling eyes", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F62C", emoji: "😬", name: "grimacing face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F62E-200D-1F4A8", emoji: "😮‍💨", name: "face exhaling", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F925", emoji: "🤥", name: "lying face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1FAE8", emoji: "🫨", name: "shaking face", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F642-200D-2194", emoji: "🙂‍↔️", name: "head shaking horizontally", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  { code: "U+1F642-200D-2195", emoji: "🙂‍↕️", name: "head shaking vertically", category: "Smileys & Emotion", subcategory: "face-neutral-skeptical" },
  
  // face-sleepy
  { code: "U+1F60C", emoji: "😌", name: "relieved face", category: "Smileys & Emotion", subcategory: "face-sleepy" },
  { code: "U+1F614", emoji: "😔", name: "pensive face", category: "Smileys & Emotion", subcategory: "face-sleepy" },
  { code: "U+1F62A", emoji: "😪", name: "sleepy face", category: "Smileys & Emotion", subcategory: "face-sleepy" },
  { code: "U+1F924", emoji: "🤤", name: "drooling face", category: "Smileys & Emotion", subcategory: "face-sleepy" },
  { code: "U+1F634", emoji: "😴", name: "sleeping face", category: "Smileys & Emotion", subcategory: "face-sleepy" },
  
  // face-unwell
  { code: "U+1F637", emoji: "😷", name: "face with medical mask", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F912", emoji: "🤒", name: "face with thermometer", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F915", emoji: "🤕", name: "face with head-bandage", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F922", emoji: "🤢", name: "nauseated face", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F92E", emoji: "🤮", name: "face vomiting", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F927", emoji: "🤧", name: "sneezing face", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F975", emoji: "🥵", name: "hot face", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F976", emoji: "🥶", name: "cold face", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F974", emoji: "🥴", name: "woozy face", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F635", emoji: "😵", name: "face with crossed-out eyes", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F635-200D-1F4AB", emoji: "😵‍💫", name: "face with spiral eyes", category: "Smileys & Emotion", subcategory: "face-unwell" },
  { code: "U+1F92F", emoji: "🤯", name: "exploding head", category: "Smileys & Emotion", subcategory: "face-unwell" },
  
  // face-hat
  { code: "U+1F920", emoji: "🤠", name: "cowboy hat face", category: "Smileys & Emotion", subcategory: "face-hat" },
  { code: "U+1F973", emoji: "🥳", name: "partying face", category: "Smileys & Emotion", subcategory: "face-hat" },
  { code: "U+1F978", emoji: "🥸", name: "disguised face", category: "Smileys & Emotion", subcategory: "face-hat" },
  
  // face-glasses
  { code: "U+1F60E", emoji: "😎", name: "smiling face with sunglasses", category: "Smileys & Emotion", subcategory: "face-glasses" },
  { code: "U+1F913", emoji: "🤓", name: "nerd face", category: "Smileys & Emotion", subcategory: "face-glasses" },
  { code: "U+1F9D0", emoji: "🧐", name: "face with monocle", category: "Smileys & Emotion", subcategory: "face-glasses" },
  
  // face-concerned
  { code: "U+1F615", emoji: "😕", name: "confused face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1FAE4", emoji: "🫤", name: "face with diagonal mouth", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F61F", emoji: "😟", name: "worried face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F641", emoji: "🙁", name: "slightly frowning face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+2639", emoji: "☹️", name: "frowning face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F62E", emoji: "😮", name: "face with open mouth", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F62F", emoji: "😯", name: "hushed face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F632", emoji: "😲", name: "astonished face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F633", emoji: "😳", name: "flushed face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F97A", emoji: "🥺", name: "pleading face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F979", emoji: "🥹", name: "face holding back tears", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F626", emoji: "😦", name: "frowning face with open mouth", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F627", emoji: "😧", name: "anguished face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F628", emoji: "😨", name: "fearful face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F630", emoji: "😰", name: "anxious face with sweat", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F625", emoji: "😥", name: "sad but relieved face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F622", emoji: "😢", name: "crying face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F62D", emoji: "😭", name: "loudly crying face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F631", emoji: "😱", name: "face screaming in fear", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F616", emoji: "😖", name: "confounded face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F623", emoji: "😣", name: "persevering face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F61E", emoji: "😞", name: "disappointed face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F613", emoji: "😓", name: "downcast face with sweat", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F629", emoji: "😩", name: "weary face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F62B", emoji: "😫", name: "tired face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  { code: "U+1F971", emoji: "🥱", name: "yawning face", category: "Smileys & Emotion", subcategory: "face-concerned" },
  
  // face-negative
  { code: "U+1F624", emoji: "😤", name: "face with steam from nose", category: "Smileys & Emotion", subcategory: "face-negative" },
  { code: "U+1F621", emoji: "😡", name: "enraged face", category: "Smileys & Emotion", subcategory: "face-negative" },
  { code: "U+1F620", emoji: "😠", name: "angry face", category: "Smileys & Emotion", subcategory: "face-negative" },
  { code: "U+1F92C", emoji: "🤬", name: "face with symbols on mouth", category: "Smileys & Emotion", subcategory: "face-negative" },
  { code: "U+1F608", emoji: "😈", name: "smiling face with horns", category: "Smileys & Emotion", subcategory: "face-negative" },
  { code: "U+1F47F", emoji: "👿", name: "angry face with horns", category: "Smileys & Emotion", subcategory: "face-negative" },
  { code: "U+1F480", emoji: "💀", name: "skull", category: "Smileys & Emotion", subcategory: "face-negative" },
  { code: "U+2620", emoji: "☠️", name: "skull and crossbones", category: "Smileys & Emotion", subcategory: "face-negative" },
  
  // face-costume
  { code: "U+1F4A9", emoji: "💩", name: "pile of poo", category: "Smileys & Emotion", subcategory: "face-costume" },
  { code: "U+1F921", emoji: "🤡", name: "clown face", category: "Smileys & Emotion", subcategory: "face-costume" },
  { code: "U+1F479", emoji: "👹", name: "ogre", category: "Smileys & Emotion", subcategory: "face-costume" },
  { code: "U+1F47A", emoji: "👺", name: "goblin", category: "Smileys & Emotion", subcategory: "face-costume" },
  { code: "U+1F47B", emoji: "👻", name: "ghost", category: "Smileys & Emotion", subcategory: "face-costume" },
  { code: "U+1F47D", emoji: "👽", name: "alien", category: "Smileys & Emotion", subcategory: "face-costume" },
  { code: "U+1F47E", emoji: "👾", name: "alien monster", category: "Smileys & Emotion", subcategory: "face-costume" },
  { code: "U+1F916", emoji: "🤖", name: "robot", category: "Smileys & Emotion", subcategory: "face-costume" },
  
  // cat-face
  { code: "U+1F63A", emoji: "😺", name: "grinning cat", category: "Smileys & Emotion", subcategory: "cat-face" },
  { code: "U+1F638", emoji: "😸", name: "grinning cat with smiling eyes", category: "Smileys & Emotion", subcategory: "cat-face" },
  { code: "U+1F639", emoji: "😹", name: "cat with tears of joy", category: "Smileys & Emotion", subcategory: "cat-face" },
  { code: "U+1F63B", emoji: "😻", name: "smiling cat with heart-eyes", category: "Smileys & Emotion", subcategory: "cat-face" },
  { code: "U+1F63C", emoji: "😼", name: "cat with wry smile", category: "Smileys & Emotion", subcategory: "cat-face" },
  { code: "U+1F63D", emoji: "😽", name: "kissing cat", category: "Smileys & Emotion", subcategory: "cat-face" },
  { code: "U+1F640", emoji: "🙀", name: "weary cat", category: "Smileys & Emotion", subcategory: "cat-face" },
  { code: "U+1F63F", emoji: "😿", name: "crying cat", category: "Smileys & Emotion", subcategory: "cat-face" },
  { code: "U+1F63E", emoji: "😾", name: "pouting cat", category: "Smileys & Emotion", subcategory: "cat-face" },
  
  // monkey-face
  { code: "U+1F648", emoji: "🙈", name: "see-no-evil monkey", category: "Smileys & Emotion", subcategory: "monkey-face" },
  { code: "U+1F649", emoji: "🙉", name: "hear-no-evil monkey", category: "Smileys & Emotion", subcategory: "monkey-face" },
  { code: "U+1F64A", emoji: "🙊", name: "speak-no-evil monkey", category: "Smileys & Emotion", subcategory: "monkey-face" },
  
  // heart
  { code: "U+1F48C", emoji: "💌", name: "love letter", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F498", emoji: "💘", name: "heart with arrow", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F49D", emoji: "💝", name: "heart with ribbon", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F496", emoji: "💖", name: "sparkling heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F497", emoji: "💗", name: "growing heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F493", emoji: "💓", name: "beating heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F49E", emoji: "💞", name: "revolving hearts", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F495", emoji: "💕", name: "two hearts", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F49F", emoji: "💟", name: "heart decoration", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+2763", emoji: "❣️", name: "heart exclamation", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F494", emoji: "💔", name: "broken heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+2764-FE0F-200D-1F525", emoji: "❤️‍🔥", name: "heart on fire", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+2764-FE0F-200D-1FA79", emoji: "❤️‍🩹", name: "mending heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+2764", emoji: "❤️", name: "red heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1FA77", emoji: "🩷", name: "pink heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F9E1", emoji: "🧡", name: "orange heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F49B", emoji: "💛", name: "yellow heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F49A", emoji: "💚", name: "green heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F499", emoji: "💙", name: "blue heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1FA75", emoji: "🩵", name: "light blue heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F49C", emoji: "💜", name: "purple heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F90E", emoji: "🤎", name: "brown heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F5A4", emoji: "🖤", name: "black heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1FA76", emoji: "🩶", name: "grey heart", category: "Smileys & Emotion", subcategory: "heart" },
  { code: "U+1F90D", emoji: "🤍", name: "white heart", category: "Smileys & Emotion", subcategory: "heart" },
  
  // emotion
  { code: "U+1F48B", emoji: "💋", name: "kiss mark", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4AF", emoji: "💯", name: "hundred points", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4A2", emoji: "💢", name: "anger symbol", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4A5", emoji: "💥", name: "collision", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4AB", emoji: "💫", name: "dizzy", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4A6", emoji: "💦", name: "sweat droplets", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4A8", emoji: "💨", name: "dashing away", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F573", emoji: "🕳️", name: "hole", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4AC", emoji: "💬", name: "speech balloon", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F441-FE0F-200D-1F5E8-FE0F", emoji: "👁️‍🗨️", name: "eye in speech bubble", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F5E8", emoji: "🗨️", name: "left speech bubble", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F5EF", emoji: "🗯️", name: "right anger bubble", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4AD", emoji: "💭", name: "thought balloon", category: "Smileys & Emotion", subcategory: "emotion" },
  { code: "U+1F4A4", emoji: "💤", name: "zzz", category: "Smileys & Emotion", subcategory: "emotion" },
  
  // People & Body - hand-fingers-open
  { code: "U+1F44B", emoji: "👋", name: "waving hand", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1F91A", emoji: "🤚", name: "raised back of hand", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1F590", emoji: "🖐️", name: "hand with fingers splayed", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+270B", emoji: "✋", name: "raised hand", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1F596", emoji: "🖖", name: "vulcan salute", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1FAF1", emoji: "🫱", name: "rightwards hand", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1FAF2", emoji: "🫲", name: "leftwards hand", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1FAF3", emoji: "🫳", name: "palm down hand", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1FAF4", emoji: "🫴", name: "palm up hand", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1FAF7", emoji: "🫷", name: "leftwards pushing hand", category: "People & Body", subcategory: "hand-fingers-open" },
  { code: "U+1FAF8", emoji: "🫸", name: "rightwards pushing hand", category: "People & Body", subcategory: "hand-fingers-open" },
  
  // hand-fingers-partial
  { code: "U+1F44C", emoji: "👌", name: "OK hand", category: "People & Body", subcategory: "hand-fingers-partial" },
  { code: "U+1F90C", emoji: "🤌", name: "pinched fingers", category: "People & Body", subcategory: "hand-fingers-partial" },
  { code: "U+1F90F", emoji: "🤏", name: "pinching hand", category: "People & Body", subcategory: "hand-fingers-partial" },
  { code: "U+270C", emoji: "✌️", name: "victory hand", category: "People & Body", subcategory: "hand-fingers-partial" },
  { code: "U+1F91E", emoji: "🤞", name: "crossed fingers", category: "People & Body", subcategory: "hand-fingers-partial" },
  { code: "U+1FAF0", emoji: "🫰", name: "hand with index finger and thumb crossed", category: "People & Body", subcategory: "hand-fingers-partial" },
  { code: "U+1F91F", emoji: "🤟", name: "love-you gesture", category: "People & Body", subcategory: "hand-fingers-partial" },
  { code: "U+1F918", emoji: "🤘", name: "sign of the horns", category: "People & Body", subcategory: "hand-fingers-partial" },
  { code: "U+1F919", emoji: "🤙", name: "call me hand", category: "People & Body", subcategory: "hand-fingers-partial" },
  
  // hand-single-finger
  { code: "U+1F448", emoji: "👈", name: "backhand index pointing left", category: "People & Body", subcategory: "hand-single-finger" },
  { code: "U+1F449", emoji: "👉", name: "backhand index pointing right", category: "People & Body", subcategory: "hand-single-finger" },
  { code: "U+1F446", emoji: "👆", name: "backhand index pointing up", category: "People & Body", subcategory: "hand-single-finger" },
  { code: "U+1F595", emoji: "🖕", name: "middle finger", category: "People & Body", subcategory: "hand-single-finger" },
  { code: "U+1F447", emoji: "👇", name: "backhand index pointing down", category: "People & Body", subcategory: "hand-single-finger" },
  { code: "U+261D", emoji: "☝️", name: "index pointing up", category: "People & Body", subcategory: "hand-single-finger" },
  { code: "U+1FAF5", emoji: "🫵", name: "index pointing at the viewer", category: "People & Body", subcategory: "hand-single-finger" },
  
  // hand-fingers-closed
  { code: "U+1F44D", emoji: "👍", name: "thumbs up", category: "People & Body", subcategory: "hand-fingers-closed" },
  { code: "U+1F44E", emoji: "👎", name: "thumbs down", category: "People & Body", subcategory: "hand-fingers-closed" },
  { code: "U+270A", emoji: "✊", name: "raised fist", category: "People & Body", subcategory: "hand-fingers-closed" },
  { code: "U+1F44A", emoji: "👊", name: "oncoming fist", category: "People & Body", subcategory: "hand-fingers-closed" },
  { code: "U+1F91B", emoji: "🤛", name: "left-facing fist", category: "People & Body", subcategory: "hand-fingers-closed" },
  { code: "U+1F91C", emoji: "🤜", name: "right-facing fist", category: "People & Body", subcategory: "hand-fingers-closed" },
  
  // hands
  { code: "U+1F44F", emoji: "👏", name: "clapping hands", category: "People & Body", subcategory: "hands" },
  { code: "U+1F64C", emoji: "🙌", name: "raising hands", category: "People & Body", subcategory: "hands" },
  { code: "U+1FAF6", emoji: "🫶", name: "heart hands", category: "People & Body", subcategory: "hands" },
  { code: "U+1F450", emoji: "👐", name: "open hands", category: "People & Body", subcategory: "hands" },
  { code: "U+1F932", emoji: "🤲", name: "palms up together", category: "People & Body", subcategory: "hands" },
  { code: "U+1F91D", emoji: "🤝", name: "handshake", category: "People & Body", subcategory: "hands" },
  { code: "U+1F64F", emoji: "🙏", name: "folded hands", category: "People & Body", subcategory: "hands" },
  
  // hand-prop
  { code: "U+270D", emoji: "✍️", name: "writing hand", category: "People & Body", subcategory: "hand-prop" },
  { code: "U+1F485", emoji: "💅", name: "nail polish", category: "People & Body", subcategory: "hand-prop" },
  { code: "U+1F933", emoji: "🤳", name: "selfie", category: "People & Body", subcategory: "hand-prop" },
  
  // body-parts
  { code: "U+1F4AA", emoji: "💪", name: "flexed biceps", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F9BE", emoji: "🦾", name: "mechanical arm", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F9BF", emoji: "🦿", name: "mechanical leg", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F9B5", emoji: "🦵", name: "leg", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F9B6", emoji: "🦶", name: "foot", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F442", emoji: "👂", name: "ear", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F9BB", emoji: "🦻", name: "ear with hearing aid", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F443", emoji: "👃", name: "nose", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F9E0", emoji: "🧠", name: "brain", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1FAC0", emoji: "🫀", name: "anatomical heart", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1FAC1", emoji: "🫁", name: "lungs", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F9B7", emoji: "🦷", name: "tooth", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F9B4", emoji: "🦴", name: "bone", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F440", emoji: "👀", name: "eyes", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F441", emoji: "👁️", name: "eye", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F445", emoji: "👅", name: "tongue", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1F444", emoji: "👄", name: "mouth", category: "People & Body", subcategory: "body-parts" },
  { code: "U+1FAE6", emoji: "🫦", name: "biting lip", category: "People & Body", subcategory: "body-parts" },
  
  // Animals & Nature
  { code: "U+1F435", emoji: "🐵", name: "monkey face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F412", emoji: "🐒", name: "monkey", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F98D", emoji: "🦍", name: "gorilla", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9A7", emoji: "🦧", name: "orangutan", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F436", emoji: "🐶", name: "dog face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F415", emoji: "🐕", name: "dog", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9AE", emoji: "🦮", name: "guide dog", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F415-200D-1F9BA", emoji: "🐕‍🦺", name: "service dog", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F429", emoji: "🐩", name: "poodle", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F43A", emoji: "🐺", name: "wolf", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F98A", emoji: "🦊", name: "fox", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F99D", emoji: "🦝", name: "raccoon", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F431", emoji: "🐱", name: "cat face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F408", emoji: "🐈", name: "cat", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F408-200D-2B1B", emoji: "🐈‍⬛", name: "black cat", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F981", emoji: "🦁", name: "lion", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F42F", emoji: "🐯", name: "tiger face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F405", emoji: "🐅", name: "tiger", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F406", emoji: "🐆", name: "leopard", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F434", emoji: "🐴", name: "horse face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1FACE", emoji: "🫎", name: "moose", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1FACF", emoji: "🫏", name: "donkey", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F40E", emoji: "🐎", name: "horse", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F984", emoji: "🦄", name: "unicorn", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F993", emoji: "🦓", name: "zebra", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F98C", emoji: "🦌", name: "deer", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9AC", emoji: "🦬", name: "bison", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F42E", emoji: "🐮", name: "cow face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F402", emoji: "🐂", name: "ox", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F403", emoji: "🐃", name: "water buffalo", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F404", emoji: "🐄", name: "cow", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F437", emoji: "🐷", name: "pig face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F416", emoji: "🐖", name: "pig", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F417", emoji: "🐗", name: "boar", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F43D", emoji: "🐽", name: "pig nose", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F40F", emoji: "🐏", name: "ram", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F411", emoji: "🐑", name: "ewe", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F410", emoji: "🐐", name: "goat", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F42A", emoji: "🐪", name: "camel", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F42B", emoji: "🐫", name: "two-hump camel", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F999", emoji: "🦙", name: "llama", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F992", emoji: "🦒", name: "giraffe", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F418", emoji: "🐘", name: "elephant", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9A3", emoji: "🦣", name: "mammoth", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F98F", emoji: "🦏", name: "rhinoceros", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F99B", emoji: "🦛", name: "hippopotamus", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F42D", emoji: "🐭", name: "mouse face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F401", emoji: "🐁", name: "mouse", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F400", emoji: "🐀", name: "rat", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F439", emoji: "🐹", name: "hamster", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F430", emoji: "🐰", name: "rabbit face", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F407", emoji: "🐇", name: "rabbit", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F43F", emoji: "🐿️", name: "chipmunk", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9AB", emoji: "🦫", name: "beaver", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F994", emoji: "🦔", name: "hedgehog", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F987", emoji: "🦇", name: "bat", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F43B", emoji: "🐻", name: "bear", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F43B-200D-2744-FE0F", emoji: "🐻‍❄️", name: "polar bear", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F428", emoji: "🐨", name: "koala", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F43C", emoji: "🐼", name: "panda", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9A5", emoji: "🦥", name: "sloth", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9A6", emoji: "🦦", name: "otter", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9A8", emoji: "🦨", name: "skunk", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F998", emoji: "🦘", name: "kangaroo", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F9A1", emoji: "🦡", name: "badger", category: "Animals & Nature", subcategory: "animal-mammal" },
  { code: "U+1F43E", emoji: "🐾", name: "paw prints", category: "Animals & Nature", subcategory: "animal-mammal" },
  
  // Food & Drink
  { code: "U+1F34E", emoji: "🍎", name: "red apple", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1F34F", emoji: "🍏", name: "green apple", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1F350", emoji: "🍐", name: "pear", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1F351", emoji: "🍑", name: "peach", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1F352", emoji: "🍒", name: "cherries", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1F353", emoji: "🍓", name: "strawberry", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1FAD0", emoji: "🫐", name: "blueberries", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1F95D", emoji: "🥝", name: "kiwi fruit", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1F345", emoji: "🍅", name: "tomato", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1FAD2", emoji: "🫒", name: "olive", category: "Food & Drink", subcategory: "food-fruit" },
  { code: "U+1F965", emoji: "🥥", name: "coconut", category: "Food & Drink", subcategory: "food-fruit" },
  
  // Objects
  { code: "U+1F4F1", emoji: "📱", name: "mobile phone", category: "Objects", subcategory: "phone" },
  { code: "U+1F4F2", emoji: "📲", name: "mobile phone with arrow", category: "Objects", subcategory: "phone" },
  { code: "U+260E", emoji: "☎️", name: "telephone", category: "Objects", subcategory: "phone" },
  { code: "U+1F4DE", emoji: "📞", name: "telephone receiver", category: "Objects", subcategory: "phone" },
  { code: "U+1F4DF", emoji: "📟", name: "pager", category: "Objects", subcategory: "phone" },
  { code: "U+1F4E0", emoji: "📠", name: "fax machine", category: "Objects", subcategory: "phone" },
];

async function saveData() {
  try {
    const outputDir = path.join(process.cwd(), 'data');
    await fs.mkdir(outputDir, { recursive: true });
    
    const outputPath = path.join(outputDir, 'unicode-emojis.json');
    await fs.writeFile(
      outputPath,
      JSON.stringify(unicodeEmojis, null, 2),
      'utf8'
    );
    
    console.log(`Saved ${unicodeEmojis.length} emojis to ${outputPath}`);
    
    // Create summary
    const categories = {};
    unicodeEmojis.forEach(emoji => {
      if (!categories[emoji.category]) {
        categories[emoji.category] = {};
      }
      if (!categories[emoji.category][emoji.subcategory]) {
        categories[emoji.category][emoji.subcategory] = 0;
      }
      categories[emoji.category][emoji.subcategory]++;
    });
    
    const summaryPath = path.join(outputDir, 'unicode-emoji-summary.json');
    await fs.writeFile(
      summaryPath,
      JSON.stringify({
        totalEmojis: unicodeEmojis.length,
        categories: categories,
        scrapedAt: new Date().toISOString(),
        note: "This is a curated subset of common Unicode emojis, manually extracted due to page size limitations."
      }, null, 2),
      'utf8'
    );
    
    console.log(`Saved summary to ${summaryPath}`);
    console.log('\nCategories breakdown:');
    Object.entries(categories).forEach(([cat, subcats]) => {
      console.log(`\n${cat}:`);
      Object.entries(subcats).forEach(([subcat, count]) => {
        console.log(`  - ${subcat}: ${count} emojis`);
      });
    });
    
  } catch (error) {
    console.error('Error saving data:', error);
    throw error;
  }
}

// Run the save function
saveData()
  .then(() => {
    console.log('\nData extraction completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Data extraction failed:', error);
    process.exit(1);
  });