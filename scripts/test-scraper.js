/**
 * 测试版本的Unicode emoji爬取脚本
 * 用于验证解析逻辑
 */

const fs = require('fs');
const path = require('path');

// 模拟的HTML片段用于测试
const testHTML = `
<table>
<tr><th colspan="9">1. Smileys & Emotion</th></tr>
<tr><td colspan="9">face-smiling</td></tr>
<tr>
  <td>1</td>
  <td><a href="U+1F600.html">U+1F600</a></td>
  <td>😀</td>
  <td>😀</td>
  <td>😀</td>
  <td>😀</td>
  <td>😀</td>
  <td>😀</td>
  <td>grinning face</td>
</tr>
<tr>
  <td>2</td>
  <td><a href="U+1F603.html">U+1F603</a></td>
  <td>😃</td>
  <td>😃</td>
  <td>😃</td>
  <td>😃</td>
  <td>😃</td>
  <td>😃</td>
  <td>grinning face with big eyes</td>
</tr>
<tr>
  <td>3</td>
  <td><a href="U+1F604.html">U+1F604</a></td>
  <td>😄</td>
  <td>😄</td>
  <td>😄</td>
  <td>😄</td>
  <td>😄</td>
  <td>😄</td>
  <td>grinning face with smiling eyes</td>
</tr>
<tr><th colspan="9">2. People & Body</th></tr>
<tr><td colspan="9">hand-fingers-open</td></tr>
<tr>
  <td>4</td>
  <td><a href="U+1F44B.html">U+1F44B</a></td>
  <td>👋</td>
  <td>👋</td>
  <td>👋</td>
  <td>👋</td>
  <td>👋</td>
  <td>👋</td>
  <td>waving hand</td>
</tr>
<tr>
  <td>5</td>
  <td><a href="U+1F91A.html">U+1F91A</a></td>
  <td>🤚</td>
  <td>🤚</td>
  <td>🤚</td>
  <td>🤚</td>
  <td>🤚</td>
  <td>🤚</td>
  <td>raised back of hand</td>
</tr>
</table>
`;

class TestEmojiScraper {
  constructor() {
    this.emojis = [];
    this.currentCategory = '';
    this.currentSubcategory = '';
  }

  parseTestHTML(html) {
    console.log('🧪 开始测试HTML解析...');
    
    // 使用正则表达式解析表格行
    const tableRowRegex = /<tr[^>]*>(.*?)<\/tr>/gs;
    const rows = html.match(tableRowRegex) || [];
    
    console.log(`📊 找到 ${rows.length} 个表格行`);
    
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      
      // 检查是否是分类标题行
      if (this.isCategoryRow(row)) {
        this.currentCategory = this.extractCategory(row);
        this.currentSubcategory = '';
        console.log(`📂 发现分类: ${this.currentCategory}`);
        continue;
      }
      
      // 检查是否是子分类行
      if (this.isSubcategoryRow(row)) {
        this.currentSubcategory = this.extractSubcategory(row);
        console.log(`  📁 子分类: ${this.currentSubcategory}`);
        continue;
      }
      
      // 提取emoji数据行
      const emojiData = this.extractEmojiFromRow(row);
      if (emojiData) {
        this.emojis.push(emojiData);
        console.log(`✨ 提取emoji: ${emojiData.emoji} ${emojiData.code} - ${emojiData.name}`);
      }
    }
    
    return this.emojis;
  }

  isCategoryRow(row) {
    return /<th[^>]*colspan[^>]*>/i.test(row) && 
           !row.includes('№') && 
           !row.includes('Code') &&
           !row.includes('Browser');
  }

  extractCategory(row) {
    const match = row.match(/<th[^>]*colspan[^>]*>([^<]+)</i);
    if (match) {
      return this.cleanText(match[1]);
    }
    return '';
  }

  isSubcategoryRow(row) {
    return /<td[^>]*colspan[^>]*>/i.test(row) && 
           !row.includes('<a href=');
  }

  extractSubcategory(row) {
    const match = row.match(/<td[^>]*colspan[^>]*>([^<]+)</i);
    if (match) {
      return this.cleanText(match[1]);
    }
    return '';
  }

  extractEmojiFromRow(row) {
    try {
      // 提取所有td单元格
      const cellRegex = /<td[^>]*>(.*?)<\/td>/gs;
      const cells = [];
      let match;
      
      while ((match = cellRegex.exec(row)) !== null) {
        cells.push(match[1]);
      }
      
      // 需要至少8个单元格才是有效的emoji行
      if (cells.length < 8) {
        return null;
      }
      
      // 第1列: 序号
      const numberText = this.cleanText(cells[0]);
      const number = parseInt(numberText) || 0;
      
      // 第2列: Unicode代码
      const codeCell = cells[1];
      const codeMatch = codeCell.match(/<a[^>]*>([^<]+)<\/a>/);
      const unicodeCode = codeMatch ? this.cleanText(codeMatch[1]) : '';
      
      // 第3列: 浏览器显示的emoji
      const browserEmoji = this.cleanText(cells[2]);
      
      // 最后一列: CLDR Short Name
      const cldrName = this.cleanText(cells[cells.length - 1]);
      
      // 验证必要字段
      if (!unicodeCode || !browserEmoji || !cldrName || number === 0) {
        return null;
      }
      
      // 构建emoji数据对象
      const emojiData = {
        number: number,
        code: unicodeCode,
        emoji: browserEmoji,
        name: cldrName,
        category: this.currentCategory || 'Unknown',
        subcategory: this.currentSubcategory || '',
        codepoints: this.extractCodepoints(unicodeCode),
        keywords: this.generateKeywords(cldrName, this.currentCategory, this.currentSubcategory)
      };
      
      return emojiData;
      
    } catch (error) {
      console.warn('解析错误:', error.message);
      return null;
    }
  }

  extractCodepoints(unicodeCode) {
    const matches = unicodeCode.match(/U\+([0-9A-F]+)/g);
    if (matches) {
      return matches.map(match => match.replace('U+', '').toLowerCase());
    }
    return [];
  }

  generateKeywords(name, category, subcategory) {
    const keywords = new Set();
    
    if (name) {
      name.toLowerCase()
          .replace(/[^\w\s-]/g, ' ')
          .split(/[\s-_]+/)
          .forEach(word => {
            if (word.length > 1) keywords.add(word);
          });
    }
    
    if (category) {
      category.toLowerCase()
             .replace(/[^\w\s-]/g, ' ')
             .split(/[\s&-_]+/)
             .forEach(word => {
               if (word.length > 1) keywords.add(word);
             });
    }
    
    if (subcategory) {
      subcategory.toLowerCase()
                 .replace(/[^\w\s-]/g, ' ')
                 .split(/[\s-_]+/)
                 .forEach(word => {
                   if (word.length > 1) keywords.add(word);
                 });
    }
    
    return Array.from(keywords).slice(0, 10);
  }

  cleanText(text) {
    return text
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#x([0-9A-F]+);/gi, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
      .replace(/&#(\d+);/g, (match, dec) => String.fromCharCode(parseInt(dec, 10)))
      .replace(/\s+/g, ' ')
      .trim();
  }

  saveTestResults() {
    const outputDir = path.join(__dirname, '..', 'data');
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const filePath = path.join(outputDir, 'test-emoji-results.json');
    
    const data = {
      metadata: {
        source: 'Test HTML parsing',
        test_date: new Date().toISOString(),
        total_count: this.emojis.length
      },
      emojis: this.emojis
    };
    
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`💾 测试结果已保存到: ${filePath}`);
    
    return filePath;
  }

  printResults() {
    console.log('\n📊 === 测试结果 ===');
    console.log(`总emoji数量: ${this.emojis.length}`);
    
    console.log('\n提取的emoji:');
    this.emojis.forEach((emoji, index) => {
      console.log(`  ${index + 1}. ${emoji.emoji} ${emoji.code} - ${emoji.name} (${emoji.category})`);
    });
  }
}

// 运行测试
function runTest() {
  console.log('🚀 开始测试emoji解析逻辑...\n');
  
  const scraper = new TestEmojiScraper();
  scraper.parseTestHTML(testHTML);
  scraper.printResults();
  scraper.saveTestResults();
  
  console.log('\n✅ 测试完成！');
  
  if (scraper.emojis.length > 0) {
    console.log('🎉 解析逻辑工作正常，可以用于实际爬取');
  } else {
    console.log('❌ 解析逻辑有问题，需要调试');
  }
}

if (require.main === module) {
  runTest();
}

module.exports = TestEmojiScraper;
