const fs = require('fs').promises;
const path = require('path');

// Unicode Emoji数据来源于官方技术文档
// 由于完整页面过大，我们使用官方提供的emoji数据文件
async function fetchUnicodeEmojis() {
  console.log('Fetching Unicode emoji data from official sources...');
  
  try {
    // Unicode官方提供了多种格式的emoji数据
    // 我们使用emoji-test.txt文件，这是官方推荐的数据源
    const response = await fetch('https://unicode.org/Public/emoji/15.1/emoji-test.txt');
    const text = await response.text();
    
    console.log('Parsing emoji data...');
    const emojis = parseEmojiTestFile(text);
    
    // 保存数据
    const outputDir = path.join(process.cwd(), 'data');
    await fs.mkdir(outputDir, { recursive: true });
    
    const outputPath = path.join(outputDir, 'unicode-emojis-full.json');
    await fs.writeFile(
      outputPath,
      JSON.stringify(emojis, null, 2),
      'utf8'
    );
    
    console.log(`Saved ${emojis.length} emojis to ${outputPath}`);
    
    // 创建分类统计
    const stats = createStats(emojis);
    const statsPath = path.join(outputDir, 'unicode-emoji-stats.json');
    await fs.writeFile(
      statsPath,
      JSON.stringify(stats, null, 2),
      'utf8'
    );
    
    console.log(`Saved statistics to ${statsPath}`);
    
  } catch (error) {
    console.error('Error fetching emoji data:', error);
    
    // 如果网络请求失败，使用备用方案
    console.log('\nUsing alternative approach...');
    await useAlternativeData();
  }
}

function parseEmojiTestFile(text) {
  const lines = text.split('\n');
  const emojis = [];
  let currentGroup = '';
  let currentSubgroup = '';
  
  for (const line of lines) {
    // 跳过空行和注释
    if (!line.trim() || line.startsWith('#')) {
      // 检查是否是组/子组标记
      if (line.includes('# group:')) {
        currentGroup = line.split('# group:')[1].trim();
      } else if (line.includes('# subgroup:')) {
        currentSubgroup = line.split('# subgroup:')[1].trim();
      }
      continue;
    }
    
    // 解析emoji行
    // 格式: 1F600 ; fully-qualified # 😀 E1.0 grinning face
    const match = line.match(/^([0-9A-F\s]+)\s*;\s*[\w-]+\s*#\s*(.+?)\s+E[\d.]+\s+(.+)$/);
    if (match) {
      const [, codePoints, emoji, name] = match;
      
      emojis.push({
        code: 'U+' + codePoints.trim().replace(/\s+/g, '-'),
        emoji: emoji.trim(),
        name: name.trim(),
        category: currentGroup,
        subcategory: currentSubgroup
      });
    }
  }
  
  return emojis;
}

function createStats(emojis) {
  const categories = {};
  
  emojis.forEach(emoji => {
    if (!categories[emoji.category]) {
      categories[emoji.category] = {};
    }
    if (!categories[emoji.category][emoji.subcategory]) {
      categories[emoji.category][emoji.subcategory] = 0;
    }
    categories[emoji.category][emoji.subcategory]++;
  });
  
  return {
    totalEmojis: emojis.length,
    categories: categories,
    fetchedAt: new Date().toISOString(),
    source: 'Unicode official emoji-test.txt file'
  };
}

// 备用方案：使用更完整的静态数据
async function useAlternativeData() {
  console.log('Creating comprehensive emoji dataset...');
  
  // 这里我们将创建一个包含所有主要类别的完整数据集
  // 数据基于Unicode 15.1标准
  const allEmojis = [];
  
  // 使用API获取emoji数据
  try {
    // 尝试使用emoji-api或其他公开API
    const response = await fetch('https://emoji-api.com/emojis?access_key=demo');
    const data = await response.json();
    
    if (Array.isArray(data)) {
      data.forEach(item => {
        allEmojis.push({
          code: item.codePoint || 'U+' + item.unicode,
          emoji: item.character,
          name: item.unicodeName || item.slug,
          category: item.group || 'Uncategorized',
          subcategory: item.subGroup || 'other'
        });
      });
    }
  } catch (error) {
    console.log('API request failed, using local comprehensive data...');
    
    // 使用本地完整数据集
    const localData = require('./emoji-full-list.js');
    allEmojis.push(...localData);
  }
  
  // 保存数据
  const outputDir = path.join(process.cwd(), 'data');
  await fs.mkdir(outputDir, { recursive: true });
  
  const outputPath = path.join(outputDir, 'unicode-emojis-full.json');
  await fs.writeFile(
    outputPath,
    JSON.stringify(allEmojis, null, 2),
    'utf8'
  );
  
  console.log(`Saved ${allEmojis.length} emojis to ${outputPath}`);
}

// 运行爬虫
fetchUnicodeEmojis()
  .then(() => {
    console.log('Emoji data fetching completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Failed to fetch emoji data:', error);
    process.exit(1);
  });