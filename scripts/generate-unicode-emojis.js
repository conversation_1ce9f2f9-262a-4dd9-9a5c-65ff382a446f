const fs = require('fs').promises;
const path = require('path');

class UnicodeEmojiGenerator {
  constructor() {
    this.emojis = [];
    
    // Unicode emoji范围和分类
    this.emojiRanges = [
      // Smileys & Emotion
      { start: 0x1F600, end: 0x1F64F, category: 'Smileys & Emotion', subcategory: 'face-smiling' },
      { start: 0x1F910, end: 0x1F93F, category: 'Smileys & Emotion', subcategory: 'face-hand' },
      { start: 0x1F970, end: 0x1F97F, category: 'Smileys & Emotion', subcategory: 'face-affection' },
      
      // People & Body
      { start: 0x1F44D, end: 0x1F44E, category: 'People & Body', subcategory: 'hand-fingers-closed' },
      { start: 0x1F44F, end: 0x1F450, category: 'People & Body', subcategory: 'hands' },
      { start: 0x1F466, end: 0x1F469, category: 'People & Body', subcategory: 'person' },
      { start: 0x1F46A, end: 0x1F46D, category: 'People & Body', subcategory: 'family' },
      { start: 0x1F46E, end: 0x1F482, category: 'People & Body', subcategory: 'person-role' },
      { start: 0x1F483, end: 0x1F487, category: 'People & Body', subcategory: 'person-activity' },
      { start: 0x1F574, end: 0x1F575, category: 'People & Body', subcategory: 'person-gesture' },
      { start: 0x1F57A, end: 0x1F57A, category: 'People & Body', subcategory: 'person-activity' },
      { start: 0x1F590, end: 0x1F590, category: 'People & Body', subcategory: 'hand-fingers-open' },
      { start: 0x1F595, end: 0x1F596, category: 'People & Body', subcategory: 'hand-single-finger' },
      { start: 0x1F645, end: 0x1F647, category: 'People & Body', subcategory: 'person-gesture' },
      { start: 0x1F64B, end: 0x1F64F, category: 'People & Body', subcategory: 'person-gesture' },
      
      // Animals & Nature
      { start: 0x1F400, end: 0x1F43F, category: 'Animals & Nature', subcategory: 'animal-mammal' },
      { start: 0x1F980, end: 0x1F9AE, category: 'Animals & Nature', subcategory: 'animal-bug' },
      { start: 0x1F331, end: 0x1F335, category: 'Animals & Nature', subcategory: 'plant-other' },
      { start: 0x1F337, end: 0x1F33C, category: 'Animals & Nature', subcategory: 'plant-flower' },
      { start: 0x1F340, end: 0x1F344, category: 'Animals & Nature', subcategory: 'plant-other' },
      
      // Food & Drink
      { start: 0x1F345, end: 0x1F37F, category: 'Food & Drink', subcategory: 'food-fruit' },
      { start: 0x1F950, end: 0x1F96F, category: 'Food & Drink', subcategory: 'food-vegetable' },
      { start: 0x1F32D, end: 0x1F330, category: 'Food & Drink', subcategory: 'food-prepared' },
      { start: 0x1F37A, end: 0x1F37E, category: 'Food & Drink', subcategory: 'drink' },
      
      // Travel & Places
      { start: 0x1F680, end: 0x1F6C5, category: 'Travel & Places', subcategory: 'transport-ground' },
      { start: 0x1F6E0, end: 0x1F6EC, category: 'Travel & Places', subcategory: 'transport-air' },
      { start: 0x1F3E0, end: 0x1F3F0, category: 'Travel & Places', subcategory: 'place-building' },
      { start: 0x1F5FA, end: 0x1F5FF, category: 'Travel & Places', subcategory: 'place-map' },
      
      // Activities
      { start: 0x1F3A0, end: 0x1F3CF, category: 'Activities', subcategory: 'event' },
      { start: 0x26BD, end: 0x26BE, category: 'Activities', subcategory: 'sport' },
      { start: 0x1F3C0, end: 0x1F3C4, category: 'Activities', subcategory: 'sport' },
      { start: 0x1F3C6, end: 0x1F3CA, category: 'Activities', subcategory: 'sport' },
      
      // Objects
      { start: 0x1F4A0, end: 0x1F4FC, category: 'Objects', subcategory: 'computer' },
      { start: 0x1F50A, end: 0x1F53D, category: 'Objects', subcategory: 'sound' },
      { start: 0x1F4F7, end: 0x1F4F9, category: 'Objects', subcategory: 'light-video' },
      { start: 0x1F52B, end: 0x1F52B, category: 'Objects', subcategory: 'tool' },
      
      // Symbols
      { start: 0x2600, end: 0x26FF, category: 'Symbols', subcategory: 'transport-sign' },
      { start: 0x2700, end: 0x27BF, category: 'Symbols', subcategory: 'other-symbol' },
      { start: 0x1F170, end: 0x1F251, category: 'Symbols', subcategory: 'alphanum' },
      
      // Flags
      { start: 0x1F1E6, end: 0x1F1FF, category: 'Flags', subcategory: 'country-flag' }
    ];
    
    // 预定义的emoji名称映射
    this.emojiNames = {
      // Smileys & Emotion
      0x1F600: 'grinning face',
      0x1F601: 'beaming face with smiling eyes',
      0x1F602: 'face with tears of joy',
      0x1F603: 'grinning face with big eyes',
      0x1F604: 'grinning face with smiling eyes',
      0x1F605: 'grinning squinting face',
      0x1F606: 'grinning face with sweat',
      0x1F607: 'smiling face with halo',
      0x1F608: 'smiling face with horns',
      0x1F609: 'winking face',
      0x1F60A: 'smiling face with smiling eyes',
      0x1F60B: 'face savoring food',
      0x1F60C: 'relieved face',
      0x1F60D: 'smiling face with heart-eyes',
      0x1F60E: 'smiling face with sunglasses',
      0x1F60F: 'smirking face',
      0x1F610: 'neutral face',
      0x1F611: 'expressionless face',
      0x1F612: 'unamused face',
      0x1F613: 'downcast face with sweat',
      0x1F614: 'pensive face',
      0x1F615: 'confused face',
      0x1F616: 'confounded face',
      0x1F617: 'kissing face',
      0x1F618: 'face blowing a kiss',
      0x1F619: 'kissing face with smiling eyes',
      0x1F61A: 'kissing face with closed eyes',
      0x1F61B: 'face with tongue',
      0x1F61C: 'winking face with tongue',
      0x1F61D: 'squinting face with tongue',
      0x1F61E: 'disappointed face',
      0x1F61F: 'worried face',
      0x1F620: 'angry face',
      0x1F621: 'pouting face',
      0x1F622: 'crying face',
      0x1F623: 'persevering face',
      0x1F624: 'face with steam from nose',
      0x1F625: 'sad but relieved face',
      0x1F626: 'frowning face with open mouth',
      0x1F627: 'anguished face',
      0x1F628: 'fearful face',
      0x1F629: 'weary face',
      0x1F62A: 'sleepy face',
      0x1F62B: 'tired face',
      0x1F62C: 'grimacing face',
      0x1F62D: 'loudly crying face',
      0x1F62E: 'face with open mouth',
      0x1F62F: 'hushed face',
      0x1F630: 'anxious face with sweat',
      0x1F631: 'face screaming in fear',
      0x1F632: 'astonished face',
      0x1F633: 'flushed face',
      0x1F634: 'sleeping face',
      0x1F635: 'dizzy face',
      0x1F636: 'face without mouth',
      0x1F637: 'face with medical mask',
      0x1F638: 'grinning cat with smiling eyes',
      0x1F639: 'cat with tears of joy',
      0x1F63A: 'grinning cat',
      0x1F63B: 'smiling cat with heart-eyes',
      0x1F63C: 'cat with wry smile',
      0x1F63D: 'kissing cat',
      0x1F63E: 'pouting cat',
      0x1F63F: 'crying cat',
      0x1F640: 'weary cat',
      0x1F641: 'slightly frowning face',
      0x1F642: 'slightly smiling face',
      0x1F643: 'upside-down face',
      0x1F644: 'face with rolling eyes',
      
      // People & Body
      0x1F44D: 'thumbs up',
      0x1F44E: 'thumbs down',
      0x1F44F: 'clapping hands',
      0x1F450: 'open hands',
      0x1F466: 'boy',
      0x1F467: 'girl',
      0x1F468: 'man',
      0x1F469: 'woman',
      0x1F46A: 'family',
      0x1F46B: 'woman and man holding hands',
      0x1F46C: 'men holding hands',
      0x1F46D: 'women holding hands',
      0x1F46E: 'police officer',
      0x1F46F: 'women with bunny ears',
      0x1F470: 'person with veil',
      0x1F471: 'person: blond hair',
      0x1F472: 'person with skullcap',
      0x1F473: 'person wearing turban',
      0x1F474: 'old man',
      0x1F475: 'old woman',
      0x1F476: 'baby',
      0x1F477: 'construction worker',
      0x1F478: 'princess',
      0x1F479: 'ogre',
      0x1F47A: 'goblin',
      0x1F47B: 'ghost',
      0x1F47C: 'baby angel',
      0x1F47D: 'alien',
      0x1F47E: 'alien monster',
      0x1F47F: 'angry face with horns',
      0x1F480: 'skull',
      0x1F481: 'person tipping hand',
      0x1F482: 'guard',
      0x1F483: 'woman dancing',
      0x1F484: 'nail polish',
      0x1F485: 'lipstick',
      0x1F486: 'person getting massage',
      0x1F487: 'person getting haircut',
      
      // Animals & Nature
      0x1F400: 'rat',
      0x1F401: 'mouse',
      0x1F402: 'ox',
      0x1F403: 'water buffalo',
      0x1F404: 'cow',
      0x1F405: 'tiger',
      0x1F406: 'leopard',
      0x1F407: 'rabbit',
      0x1F408: 'cat',
      0x1F409: 'dragon',
      0x1F40A: 'crocodile',
      0x1F40B: 'whale',
      0x1F40C: 'snail',
      0x1F40D: 'snake',
      0x1F40E: 'horse',
      0x1F40F: 'ram',
      0x1F410: 'goat',
      0x1F411: 'ewe',
      0x1F412: 'monkey',
      0x1F413: 'rooster',
      0x1F414: 'chicken',
      0x1F415: 'dog',
      0x1F416: 'pig',
      0x1F417: 'boar',
      0x1F418: 'elephant',
      0x1F419: 'octopus',
      0x1F41A: 'spiral shell',
      0x1F41B: 'bug',
      0x1F41C: 'ant',
      0x1F41D: 'honeybee',
      0x1F41E: 'lady beetle',
      0x1F41F: 'fish',
      0x1F420: 'tropical fish',
      0x1F421: 'blowfish',
      0x1F422: 'turtle',
      0x1F423: 'hatching chick',
      0x1F424: 'baby chick',
      0x1F425: 'front-facing baby chick',
      0x1F426: 'bird',
      0x1F427: 'penguin',
      0x1F428: 'koala',
      0x1F429: 'poodle',
      0x1F42A: 'camel',
      0x1F42B: 'two-hump camel',
      0x1F42C: 'dolphin',
      0x1F42D: 'mouse face',
      0x1F42E: 'cow face',
      0x1F42F: 'tiger face',
      0x1F430: 'rabbit face',
      0x1F431: 'cat face',
      0x1F432: 'dragon face',
      0x1F433: 'spouting whale',
      0x1F434: 'horse face',
      0x1F435: 'monkey face',
      0x1F436: 'dog face',
      0x1F437: 'pig face',
      0x1F438: 'frog',
      0x1F439: 'hamster',
      0x1F43A: 'wolf',
      0x1F43B: 'bear',
      0x1F43C: 'panda',
      0x1F43D: 'pig nose',
      0x1F43E: 'paw prints',
      
      // Food & Drink
      0x1F345: 'tomato',
      0x1F346: 'eggplant',
      0x1F347: 'grapes',
      0x1F348: 'melon',
      0x1F349: 'watermelon',
      0x1F34A: 'tangerine',
      0x1F34B: 'lemon',
      0x1F34C: 'banana',
      0x1F34D: 'pineapple',
      0x1F34E: 'red apple',
      0x1F34F: 'green apple',
      0x1F350: 'pear',
      0x1F351: 'peach',
      0x1F352: 'cherries',
      0x1F353: 'strawberry',
      0x1F354: 'hamburger',
      0x1F355: 'pizza',
      0x1F356: 'meat on bone',
      0x1F357: 'poultry leg',
      0x1F358: 'rice cracker',
      0x1F359: 'rice ball',
      0x1F35A: 'cooked rice',
      0x1F35B: 'curry rice',
      0x1F35C: 'steaming bowl',
      0x1F35D: 'spaghetti',
      0x1F35E: 'bread',
      0x1F35F: 'french fries',
      0x1F360: 'roasted sweet potato',
      0x1F361: 'dango',
      0x1F362: 'oden',
      0x1F363: 'sushi',
      0x1F364: 'fried shrimp',
      0x1F365: 'fish cake with swirl',
      0x1F366: 'soft ice cream',
      0x1F367: 'shaved ice',
      0x1F368: 'ice cream',
      0x1F369: 'doughnut',
      0x1F36A: 'cookie',
      0x1F36B: 'chocolate bar',
      0x1F36C: 'candy',
      0x1F36D: 'lollipop',
      0x1F36E: 'custard',
      0x1F36F: 'honey pot',
      0x1F370: 'shortcake',
      0x1F371: 'bento box',
      0x1F372: 'pot of food',
      0x1F373: 'cooking',
      0x1F374: 'fork and knife',
      0x1F375: 'teacup without handle',
      0x1F376: 'sake',
      0x1F377: 'wine glass',
      0x1F378: 'cocktail glass',
      0x1F379: 'tropical drink',
      0x1F37A: 'beer mug',
      0x1F37B: 'clinking beer mugs',
      0x1F37C: 'baby bottle'
    };
  }

  generateEmojis() {
    console.log('🚀 开始生成Unicode emoji数据...');

    let emojiCount = 0;
    const processedCodepoints = new Set(); // 跟踪已处理的码点，避免重复

    for (const range of this.emojiRanges) {
      console.log(`📂 处理分类: ${range.category} - ${range.subcategory}`);

      for (let codepoint = range.start; codepoint <= range.end; codepoint++) {
        try {
          // 检查是否已经处理过这个码点
          if (processedCodepoints.has(codepoint)) {
            continue;
          }

          const emoji = String.fromCodePoint(codepoint);

          // 检查是否是有效的emoji字符
          if (this.isValidEmoji(emoji)) {
            const unicodeCode = `U+${codepoint.toString(16).toUpperCase().padStart(4, '0')}`;
            const name = this.emojiNames[codepoint] || this.generateName(codepoint, range.category);

            const emojiData = {
              number: emojiCount + 1,
              code: unicodeCode,
              emoji: emoji,
              name: name,
              category: range.category,
              subcategory: range.subcategory,
              codepoints: [codepoint.toString(16).toLowerCase()],
              keywords: this.generateKeywords(name, range.category, range.subcategory)
            };

            this.emojis.push(emojiData);
            processedCodepoints.add(codepoint); // 标记为已处理
            emojiCount++;
          }
        } catch (error) {
          // 跳过无效的码点
          continue;
        }
      }
    }

    console.log(`✨ 生成完成，总共创建了 ${this.emojis.length} 个emoji`);
    return this.emojis;
  }

  isValidEmoji(char) {
    // 简单的emoji验证 - 检查字符长度和基本属性
    if (!char || char.length === 0) return false;
    
    // 检查是否在emoji范围内
    const codepoint = char.codePointAt(0);
    if (!codepoint) return false;
    
    // 排除一些控制字符和变体选择器
    if (codepoint >= 0xFE00 && codepoint <= 0xFE0F) return false; // 变体选择器
    if (codepoint >= 0x200D && codepoint <= 0x200D) return false; // 零宽连接符
    
    return true;
  }

  generateName(codepoint, category) {
    // 为未知emoji生成基本名称
    const hex = codepoint.toString(16).toUpperCase();
    const categoryPrefix = category.toLowerCase().replace(/[^a-z]/g, '');
    return `${categoryPrefix} emoji ${hex}`;
  }

  generateKeywords(name, category, subcategory) {
    const keywords = [];
    
    // 从名称中提取关键词
    const nameWords = name.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    keywords.push(...nameWords);
    
    // 添加分类关键词
    if (category) {
      keywords.push(...category.toLowerCase().split(/[&\s]+/).filter(w => w.length > 2));
    }
    
    if (subcategory) {
      keywords.push(...subcategory.toLowerCase().split(/[-\s]+/).filter(w => w.length > 2));
    }
    
    // 去重并返回前5个关键词
    return [...new Set(keywords)].slice(0, 5);
  }

  async saveResults() {
    console.log('💾 保存结果到文件...');
    
    // 创建输出目录
    const outputDir = path.join(process.cwd(), '..', 'data');
    await fs.mkdir(outputDir, { recursive: true });
    
    // 统计分类信息
    const categories = {};
    this.emojis.forEach(emoji => {
      if (!categories[emoji.category]) {
        categories[emoji.category] = 0;
      }
      categories[emoji.category]++;
    });
    
    // 构建完整数据结构
    const completeData = {
      metadata: {
        source: "Unicode emoji ranges (generated)",
        version: "16.0",
        created_at: new Date().toISOString(),
        total_count: this.emojis.length,
        description: "基于Unicode标准范围生成的emoji数据，包含主要表情符号"
      },
      categories: categories,
      emojis: this.emojis
    };
    
    // 保存完整数据
    const outputPath = path.join(outputDir, 'unicode-emojis-generated.json');
    await fs.writeFile(
      outputPath,
      JSON.stringify(completeData, null, 2),
      'utf8'
    );
    
    console.log(`✅ 完整数据已保存到: ${outputPath}`);
    console.log(`📊 总计: ${this.emojis.length} 个emoji`);
    console.log(`📂 分类统计:`, categories);
    
    return outputPath;
  }
}

// 主执行函数
async function main() {
  const generator = new UnicodeEmojiGenerator();
  
  try {
    generator.generateEmojis();
    await generator.saveResults();
    
    console.log('🎊 Unicode emoji生成任务成功完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('💥 生成失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = UnicodeEmojiGenerator;
