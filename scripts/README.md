# Unicode Emoji Scraper

This directory contains scripts to scrape emoji data from the Unicode.org website.

## Setup

```bash
cd scripts
npm install
```

## Usage

### Option 1: Puppeteer Version (More Reliable)
```bash
npm run scrape
```

This uses <PERSON><PERSON><PERSON><PERSON> to render the page in a real browser, which is more reliable for complex pages.

### Option 2: Simple Version (Faster)
```bash
npm run scrape:simple
```

This uses axios and cheerio for faster scraping, but may not work if the page requires JavaScript rendering.

## Output

The scripts will create a `data` directory with two files:

1. `unicode-emojis.json` - Contains all emoji data:
   - `code`: Unicode code point (e.g., "U+1F600")
   - `emoji`: The actual emoji character
   - `name`: CLDR short name
   - `category`: Main category (e.g., "Smileys & Emotion")
   - `subcategory`: Subcategory (e.g., "face-smiling")

2. `unicode-emoji-summary.json` - Summary statistics:
   - Total number of emojis
   - Breakdown by category and subcategory
   - Timestamp of when the data was scraped

## Notes

- The Unicode emoji list is quite large (1800+ emojis)
- The page may take some time to load
- Make sure you have a stable internet connection
- The Puppeteer version requires Chrome/Chromium to be installed