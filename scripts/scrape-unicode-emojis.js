/**
 * Unicode.org Emoji 数据爬取脚本
 * 提取 Code、Browser、CLDR Short Name 等信息
 */

const fs = require('fs');
const https = require('https');
const { JSDOM } = require('jsdom');

class UnicodeEmojiScraper {
  constructor() {
    this.emojis = [];
    this.categories = new Map();
  }

  async scrapeEmojis() {
    console.log('开始爬取 Unicode.org emoji 数据...');
    
    try {
      const html = await this.fetchPage('https://unicode.org/emoji/charts/full-emoji-list.html');
      const dom = new JSDOM(html);
      const document = dom.window.document;
      
      // 查找所有emoji表格行
      const rows = document.querySelectorAll('table tr');
      let currentCategory = '';
      let currentSubcategory = '';
      
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        
        // 检查是否是分类标题行
        const categoryHeader = row.querySelector('th[colspan]');
        if (categoryHeader) {
          const categoryText = categoryHeader.textContent.trim();
          if (categoryText && !categoryText.includes('№') && !categoryText.includes('Code')) {
            currentCategory = this.cleanCategoryName(categoryText);
            console.log(`发现分类: ${currentCategory}`);
            continue;
          }
        }
        
        // 检查是否是子分类行
        const subcategoryCell = row.querySelector('td[colspan]');
        if (subcategoryCell) {
          currentSubcategory = this.cleanCategoryName(subcategoryCell.textContent.trim());
          console.log(`  子分类: ${currentSubcategory}`);
          continue;
        }
        
        // 提取emoji数据行
        const cells = row.querySelectorAll('td');
        if (cells.length >= 8) {
          const emojiData = this.extractEmojiData(cells, currentCategory, currentSubcategory);
          if (emojiData) {
            this.emojis.push(emojiData);
            if (this.emojis.length % 100 === 0) {
              console.log(`已提取 ${this.emojis.length} 个emoji...`);
            }
          }
        }
      }
      
      console.log(`爬取完成！总共提取了 ${this.emojis.length} 个emoji`);
      return this.emojis;
      
    } catch (error) {
      console.error('爬取过程中出现错误:', error);
      throw error;
    }
  }

  extractEmojiData(cells, category, subcategory) {
    try {
      // 第1列: 序号
      const number = cells[0]?.textContent?.trim();
      
      // 第2列: Unicode代码
      const codeCell = cells[1];
      const codeLink = codeCell?.querySelector('a');
      const unicodeCode = codeLink?.textContent?.trim();
      
      // 第3列: 浏览器显示的emoji
      const browserEmoji = cells[2]?.textContent?.trim();
      
      // 第8列: CLDR Short Name (最后一列)
      const cldrName = cells[cells.length - 1]?.textContent?.trim();
      
      // 验证必要字段
      if (!unicodeCode || !browserEmoji || !cldrName) {
        return null;
      }
      
      // 清理和格式化数据
      const emojiData = {
        number: parseInt(number) || 0,
        code: unicodeCode,
        emoji: browserEmoji,
        name: cldrName,
        category: category || 'Unknown',
        subcategory: subcategory || '',
        // 提取Unicode码点
        codepoints: this.extractCodepoints(unicodeCode),
        // 生成搜索关键词
        keywords: this.generateKeywords(cldrName, category, subcategory)
      };
      
      return emojiData;
      
    } catch (error) {
      console.warn('提取emoji数据时出错:', error);
      return null;
    }
  }

  extractCodepoints(unicodeCode) {
    // 从 "U+1F600" 或 "U+1F1E6 U+1F1E8" 格式提取码点
    const matches = unicodeCode.match(/U\+([0-9A-F]+)/g);
    if (matches) {
      return matches.map(match => match.replace('U+', '').toLowerCase());
    }
    return [];
  }

  generateKeywords(name, category, subcategory) {
    const keywords = new Set();
    
    // 添加名称中的单词
    if (name) {
      name.toLowerCase().split(/[\s-_]+/).forEach(word => {
        if (word.length > 1) keywords.add(word);
      });
    }
    
    // 添加分类关键词
    if (category) {
      category.toLowerCase().split(/[\s&-_]+/).forEach(word => {
        if (word.length > 1) keywords.add(word);
      });
    }
    
    if (subcategory) {
      subcategory.toLowerCase().split(/[\s-_]+/).forEach(word => {
        if (word.length > 1) keywords.add(word);
      });
    }
    
    return Array.from(keywords);
  }

  cleanCategoryName(text) {
    return text
      .replace(/^\d+\.\s*/, '') // 移除开头的数字
      .replace(/\s+/g, ' ')     // 标准化空格
      .trim();
  }

  async fetchPage(url) {
    return new Promise((resolve, reject) => {
      https.get(url, (response) => {
        let data = '';
        
        response.on('data', (chunk) => {
          data += chunk;
        });
        
        response.on('end', () => {
          resolve(data);
        });
        
      }).on('error', (error) => {
        reject(error);
      });
    });
  }

  async saveToFile(filename = 'unicode-emojis.json') {
    const data = {
      metadata: {
        source: 'https://unicode.org/emoji/charts/full-emoji-list.html',
        version: '16.0',
        scraped_at: new Date().toISOString(),
        total_count: this.emojis.length
      },
      categories: this.getCategoryStats(),
      emojis: this.emojis
    };
    
    fs.writeFileSync(filename, JSON.stringify(data, null, 2), 'utf8');
    console.log(`数据已保存到 ${filename}`);
    
    // 同时保存简化版本（仅包含基本信息）
    const simplifiedData = {
      metadata: data.metadata,
      emojis: this.emojis.map(emoji => ({
        code: emoji.code,
        emoji: emoji.emoji,
        name: emoji.name,
        category: emoji.category,
        keywords: emoji.keywords.slice(0, 5) // 只保留前5个关键词
      }))
    };
    
    const simplifiedFilename = filename.replace('.json', '-simplified.json');
    fs.writeFileSync(simplifiedFilename, JSON.stringify(simplifiedData, null, 2), 'utf8');
    console.log(`简化数据已保存到 ${simplifiedFilename}`);
  }

  getCategoryStats() {
    const stats = {};
    this.emojis.forEach(emoji => {
      if (!stats[emoji.category]) {
        stats[emoji.category] = 0;
      }
      stats[emoji.category]++;
    });
    return stats;
  }
}

// 运行爬虫
async function main() {
  const scraper = new UnicodeEmojiScraper();
  
  try {
    await scraper.scrapeEmojis();
    await scraper.saveToFile('data/unicode-emojis.json');
    
    console.log('\n=== 爬取统计 ===');
    console.log(`总emoji数量: ${scraper.emojis.length}`);
    console.log('分类统计:', scraper.getCategoryStats());
    
  } catch (error) {
    console.error('爬取失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = UnicodeEmojiScraper;
