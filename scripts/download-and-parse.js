const https = require('https');
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 下载并解析Unicode emoji页面
async function downloadAndParseEmojis() {
  console.log('Downloading Unicode emoji page...');
  
  const url = 'https://unicode.org/emoji/charts/full-emoji-list.html';
  const htmlPath = path.join(__dirname, 'data', 'unicode-emoji-page.html');
  
  // 确保data目录存在
  if (!fs.existsSync(path.join(__dirname, 'data'))) {
    fs.mkdirSync(path.join(__dirname, 'data'), { recursive: true });
  }
  
  // Step 1: 下载HTML文件
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(htmlPath);
    
    https.get(url, (response) => {
      let downloadedBytes = 0;
      const totalBytes = parseInt(response.headers['content-length'], 10);
      
      response.on('data', (chunk) => {
        downloadedBytes += chunk.length;
        const progress = totalBytes ? (downloadedBytes / totalBytes * 100).toFixed(1) : '?';
        process.stdout.write(`\rDownloading: ${progress}% (${(downloadedBytes / 1024 / 1024).toFixed(1)} MB)`);
      });
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log('\nDownload completed!');
        
        // Step 2: 解析HTML文件
        parseHTMLFile(htmlPath).then(resolve).catch(reject);
      });
    }).on('error', (err) => {
      fs.unlink(htmlPath, () => {}); // 删除未完成的文件
      reject(err);
    });
  });
}

async function parseHTMLFile(htmlPath) {
  console.log('Parsing HTML file...');
  
  const html = fs.readFileSync(htmlPath, 'utf8');
  const dom = new JSDOM(html);
  const document = dom.window.document;
  
  const emojis = [];
  let currentCategory = '';
  let currentSubcategory = '';
  
  // 查找所有表格行
  const rows = document.querySelectorAll('table tr');
  
  rows.forEach((row, index) => {
    if (index % 100 === 0) {
      process.stdout.write(`\rParsing: ${index}/${rows.length} rows`);
    }
    
    // 检查是否是类别标题
    const bighead = row.querySelector('th.bighead');
    if (bighead) {
      currentCategory = bighead.textContent.trim();
      return;
    }
    
    // 检查是否是子类别标题
    const mediumhead = row.querySelector('th.mediumhead');
    if (mediumhead) {
      currentSubcategory = mediumhead.textContent.trim();
      return;
    }
    
    // 提取emoji数据
    const codeCell = row.querySelector('td.code');
    const charsCell = row.querySelector('td.chars');
    const nameCell = row.querySelector('td.name');
    
    if (codeCell && charsCell && nameCell) {
      const code = codeCell.textContent.trim();
      const emoji = charsCell.textContent.trim();
      const name = nameCell.textContent.trim();
      
      if (code && emoji && name) {
        emojis.push({
          code: code,
          emoji: emoji,
          name: name,
          category: currentCategory,
          subcategory: currentSubcategory
        });
      }
    }
  });
  
  console.log(`\nParsed ${emojis.length} emojis`);
  
  // 保存JSON数据
  const outputPath = path.join(__dirname, 'data', 'unicode-emojis-complete.json');
  fs.writeFileSync(outputPath, JSON.stringify(emojis, null, 2), 'utf8');
  console.log(`Saved to ${outputPath}`);
  
  // 创建统计信息
  const stats = createStatistics(emojis);
  const statsPath = path.join(__dirname, 'data', 'unicode-emoji-complete-stats.json');
  fs.writeFileSync(statsPath, JSON.stringify(stats, null, 2), 'utf8');
  console.log(`Statistics saved to ${statsPath}`);
  
  // 可选：删除HTML文件以节省空间
  // fs.unlinkSync(htmlPath);
  
  return emojis;
}

function createStatistics(emojis) {
  const categories = {};
  const uniqueEmojis = new Set();
  
  emojis.forEach(emoji => {
    uniqueEmojis.add(emoji.emoji);
    
    if (!categories[emoji.category]) {
      categories[emoji.category] = {
        total: 0,
        subcategories: {}
      };
    }
    
    categories[emoji.category].total++;
    
    if (!categories[emoji.category].subcategories[emoji.subcategory]) {
      categories[emoji.category].subcategories[emoji.subcategory] = 0;
    }
    categories[emoji.category].subcategories[emoji.subcategory]++;
  });
  
  return {
    totalEntries: emojis.length,
    uniqueEmojis: uniqueEmojis.size,
    categories: categories,
    parsedAt: new Date().toISOString()
  };
}

// 运行下载和解析
downloadAndParseEmojis()
  .then(() => {
    console.log('\nAll done! Check the data directory for results.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\nError:', error);
    process.exit(1);
  });