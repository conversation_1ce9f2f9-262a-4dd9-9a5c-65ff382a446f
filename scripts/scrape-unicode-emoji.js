const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

async function scrapeUnicodeEmojis() {
  console.log('Starting Unicode emoji scraper...');
  
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    console.log('Navigating to Unicode emoji page...');
    await page.goto('https://unicode.org/emoji/charts/full-emoji-list.html', {
      waitUntil: 'networkidle2',
      timeout: 60000
    });
    
    console.log('Page loaded, extracting emoji data...');
    
    const emojis = await page.evaluate(() => {
      const emojiData = [];
      const rows = document.querySelectorAll('table tr');
      
      let currentCategory = '';
      let currentSubcategory = '';
      
      rows.forEach((row) => {
        // Check if this is a category header
        const categoryHeader = row.querySelector('th.bighead');
        if (categoryHeader) {
          currentCategory = categoryHeader.textContent.trim();
          return;
        }
        
        // Check if this is a subcategory header
        const subcategoryHeader = row.querySelector('th.mediumhead');
        if (subcategoryHeader) {
          currentSubcategory = subcategoryHeader.textContent.trim();
          return;
        }
        
        // Extract emoji data from regular rows
        const codeCell = row.querySelector('td.code');
        const browserCell = row.querySelector('td.chars');
        const nameCell = row.querySelector('td.name');
        
        if (codeCell && browserCell && nameCell) {
          const code = codeCell.textContent.trim();
          const emoji = browserCell.textContent.trim();
          const name = nameCell.textContent.trim();
          
          // Skip if it's not a valid emoji row
          if (code && emoji && name && code.startsWith('U+')) {
            emojiData.push({
              code: code,
              emoji: emoji,
              name: name,
              category: currentCategory,
              subcategory: currentSubcategory
            });
          }
        }
      });
      
      return emojiData;
    });
    
    console.log(`Extracted ${emojis.length} emojis`);
    
    // Create output directory if it doesn't exist
    const outputDir = path.join(process.cwd(), 'data');
    await fs.mkdir(outputDir, { recursive: true });
    
    // Save to JSON file
    const outputPath = path.join(outputDir, 'unicode-emojis.json');
    await fs.writeFile(
      outputPath,
      JSON.stringify(emojis, null, 2),
      'utf8'
    );
    
    console.log(`Saved emoji data to ${outputPath}`);
    
    // Also create a summary file with categories
    const categories = {};
    emojis.forEach(emoji => {
      if (!categories[emoji.category]) {
        categories[emoji.category] = {};
      }
      if (!categories[emoji.category][emoji.subcategory]) {
        categories[emoji.category][emoji.subcategory] = 0;
      }
      categories[emoji.category][emoji.subcategory]++;
    });
    
    const summaryPath = path.join(outputDir, 'unicode-emoji-summary.json');
    await fs.writeFile(
      summaryPath,
      JSON.stringify({
        totalEmojis: emojis.length,
        categories: categories,
        scrapedAt: new Date().toISOString()
      }, null, 2),
      'utf8'
    );
    
    console.log(`Saved summary to ${summaryPath}`);
    
  } catch (error) {
    console.error('Error during scraping:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the scraper
scrapeUnicodeEmojis()
  .then(() => {
    console.log('Scraping completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Scraping failed:', error);
    process.exit(1);
  });