/**
 * 验证和展示emoji数据的脚本
 */

const fs = require('fs');
const path = require('path');

class EmojiDataValidator {
  constructor() {
    this.data = null;
    this.errors = [];
    this.warnings = [];
  }

  loadData(filename = 'unicode-emojis-generated.json') {
    const filePath = path.join(__dirname, '..', 'data', filename);
    
    try {
      const rawData = fs.readFileSync(filePath, 'utf8');
      this.data = JSON.parse(rawData);
      console.log(`✅ 成功加载数据文件: ${filename}`);
      return true;
    } catch (error) {
      console.error(`❌ 加载数据文件失败: ${error.message}`);
      return false;
    }
  }

  validateData() {
    if (!this.data) {
      this.errors.push('数据未加载');
      return false;
    }

    console.log('\n🔍 开始验证数据...');

    // 验证元数据
    this.validateMetadata();
    
    // 验证emoji数组
    this.validateEmojis();
    
    // 验证分类统计
    this.validateCategories();
    
    // 输出验证结果
    this.printValidationResults();
    
    return this.errors.length === 0;
  }

  validateMetadata() {
    const metadata = this.data.metadata;
    
    if (!metadata) {
      this.errors.push('缺少metadata字段');
      return;
    }

    const requiredFields = ['source', 'version', 'total_count', 'description'];
    requiredFields.forEach(field => {
      if (!metadata[field]) {
        this.errors.push(`metadata缺少${field}字段`);
      }
    });

    if (metadata.total_count !== this.data.emojis.length) {
      this.errors.push(`metadata中的total_count (${metadata.total_count}) 与实际emoji数量 (${this.data.emojis.length}) 不匹配`);
    }
  }

  validateEmojis() {
    const emojis = this.data.emojis;
    
    if (!Array.isArray(emojis)) {
      this.errors.push('emojis字段不是数组');
      return;
    }

    const seenCodes = new Set();
    const seenNumbers = new Set();
    
    emojis.forEach((emoji, index) => {
      this.validateSingleEmoji(emoji, index);
      
      // 检查重复的code
      if (seenCodes.has(emoji.code)) {
        this.errors.push(`重复的Unicode代码: ${emoji.code} (索引 ${index})`);
      } else {
        seenCodes.add(emoji.code);
      }
      
      // 检查重复的number
      if (seenNumbers.has(emoji.number)) {
        this.errors.push(`重复的序号: ${emoji.number} (索引 ${index})`);
      } else {
        seenNumbers.add(emoji.number);
      }
    });
  }

  validateSingleEmoji(emoji, index) {
    const requiredFields = ['number', 'code', 'emoji', 'name', 'category', 'codepoints', 'keywords'];
    
    requiredFields.forEach(field => {
      if (emoji[field] === undefined || emoji[field] === null) {
        this.errors.push(`emoji[${index}]缺少${field}字段`);
      }
    });

    // 验证number是数字
    if (typeof emoji.number !== 'number' || emoji.number <= 0) {
      this.errors.push(`emoji[${index}]的number字段无效: ${emoji.number}`);
    }

    // 验证code格式
    if (emoji.code && !emoji.code.match(/^U\+[0-9A-F]+(\s+U\+[0-9A-F]+)*$/)) {
      this.errors.push(`emoji[${index}]的code格式无效: ${emoji.code}`);
    }

    // 验证emoji字符存在
    if (!emoji.emoji || emoji.emoji.length === 0) {
      this.errors.push(`emoji[${index}]缺少emoji字符`);
    }

    // 验证name不为空
    if (!emoji.name || emoji.name.trim().length === 0) {
      this.errors.push(`emoji[${index}]的name为空`);
    }

    // 验证codepoints是数组
    if (!Array.isArray(emoji.codepoints)) {
      this.errors.push(`emoji[${index}]的codepoints不是数组`);
    }

    // 验证keywords是数组
    if (!Array.isArray(emoji.keywords)) {
      this.errors.push(`emoji[${index}]的keywords不是数组`);
    }
  }

  validateCategories() {
    const categories = this.data.categories;
    const emojis = this.data.emojis;
    
    if (!categories) {
      this.errors.push('缺少categories字段');
      return;
    }

    // 统计实际分类数量
    const actualCounts = {};
    emojis.forEach(emoji => {
      actualCounts[emoji.category] = (actualCounts[emoji.category] || 0) + 1;
    });

    // 比较声明的分类数量与实际数量
    Object.entries(categories).forEach(([category, declaredCount]) => {
      const actualCount = actualCounts[category] || 0;
      if (actualCount !== declaredCount) {
        this.errors.push(`分类"${category}"声明数量(${declaredCount})与实际数量(${actualCount})不匹配`);
      }
    });

    // 检查是否有未声明的分类
    Object.keys(actualCounts).forEach(category => {
      if (!categories[category]) {
        this.warnings.push(`分类"${category}"在categories中未声明`);
      }
    });
  }

  printValidationResults() {
    console.log('\n📊 === 验证结果 ===');
    
    if (this.errors.length === 0) {
      console.log('✅ 数据验证通过！');
    } else {
      console.log(`❌ 发现 ${this.errors.length} 个错误:`);
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    if (this.warnings.length > 0) {
      console.log(`\n⚠️  发现 ${this.warnings.length} 个警告:`);
      this.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning}`);
      });
    }
  }

  printDataSummary() {
    if (!this.data) return;

    console.log('\n📈 === 数据摘要 ===');
    console.log(`总emoji数量: ${this.data.emojis.length}`);
    console.log(`数据版本: ${this.data.metadata.version}`);
    console.log(`数据源: ${this.data.metadata.source}`);
    
    console.log('\n分类统计:');
    Object.entries(this.data.categories)
          .sort(([,a], [,b]) => b - a)
          .forEach(([category, count]) => {
            console.log(`  ${category}: ${count} 个`);
          });

    console.log('\n前10个emoji示例:');
    this.data.emojis.slice(0, 10).forEach((emoji, index) => {
      console.log(`  ${index + 1}. ${emoji.emoji} ${emoji.code} - ${emoji.name}`);
    });
  }

  exportToCSV(filename = 'emoji-data.csv') {
    if (!this.data) return;

    const outputPath = path.join(__dirname, '..', 'data', filename);
    
    // CSV头部
    const headers = ['Number', 'Code', 'Emoji', 'Name', 'Category', 'Subcategory', 'Keywords'];
    const csvLines = [headers.join(',')];
    
    // 数据行
    this.data.emojis.forEach(emoji => {
      const row = [
        emoji.number,
        `"${emoji.code}"`,
        `"${emoji.emoji}"`,
        `"${emoji.name}"`,
        `"${emoji.category}"`,
        `"${emoji.subcategory || ''}"`,
        `"${emoji.keywords.join('; ')}"`
      ];
      csvLines.push(row.join(','));
    });
    
    fs.writeFileSync(outputPath, csvLines.join('\n'), 'utf8');
    console.log(`\n💾 CSV文件已导出到: ${outputPath}`);
  }

  searchEmojis(query) {
    if (!this.data) return [];

    const lowerQuery = query.toLowerCase();
    
    return this.data.emojis.filter(emoji => {
      return emoji.name.toLowerCase().includes(lowerQuery) ||
             emoji.category.toLowerCase().includes(lowerQuery) ||
             emoji.keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery));
    });
  }

  printSearchResults(query) {
    const results = this.searchEmojis(query);
    
    console.log(`\n🔍 搜索 "${query}" 的结果 (${results.length} 个):`);
    results.forEach((emoji, index) => {
      console.log(`  ${index + 1}. ${emoji.emoji} ${emoji.code} - ${emoji.name} (${emoji.category})`);
    });
  }
}

// 主函数
function main() {
  const validator = new EmojiDataValidator();
  
  console.log('🎯 开始验证emoji数据文件...\n');
  
  // 加载数据
  if (!validator.loadData()) {
    process.exit(1);
  }
  
  // 验证数据
  const isValid = validator.validateData();
  
  // 显示数据摘要
  validator.printDataSummary();
  
  // 导出CSV
  validator.exportToCSV();
  
  // 示例搜索
  validator.printSearchResults('smile');
  validator.printSearchResults('hand');
  
  console.log('\n🎉 === 验证完成 ===');
  
  if (!isValid) {
    console.log('❌ 数据验证失败，请修复错误后重试');
    process.exit(1);
  } else {
    console.log('✅ 数据验证成功！');
  }
}

if (require.main === module) {
  main();
}

module.exports = EmojiDataValidator;
