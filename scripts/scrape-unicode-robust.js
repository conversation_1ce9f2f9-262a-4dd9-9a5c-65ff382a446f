const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs').promises;
const path = require('path');

class RobustUnicodeEmojiScraper {
  constructor() {
    this.emojis = [];
    this.currentCategory = '';
    this.currentSubcategory = '';
    this.processedCount = 0;
    
    // 分类映射
    this.categoryMap = {
      'Smileys & Emotion': 'Smileys & Emotion',
      'People & Body': 'People & Body', 
      'Animals & Nature': 'Animals & Nature',
      'Food & Drink': 'Food & Drink',
      'Travel & Places': 'Travel & Places',
      'Activities': 'Activities',
      'Objects': 'Objects',
      'Symbols': 'Symbols',
      'Flags': 'Flags'
    };
  }

  async downloadPage() {
    console.log('🌐 正在下载Unicode emoji页面...');
    
    const maxRetries = 3;
    let attempt = 0;
    
    while (attempt < maxRetries) {
      try {
        attempt++;
        console.log(`📥 尝试下载 (${attempt}/${maxRetries})...`);
        
        const response = await axios.get('https://unicode.org/emoji/charts/full-emoji-list.html', {
          timeout: 180000, // 3分钟超时
          maxContentLength: 50 * 1024 * 1024, // 50MB限制
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
          }
        });
        
        console.log(`✅ 页面下载成功，大小: ${(response.data.length / 1024 / 1024).toFixed(2)}MB`);
        
        // 保存页面到本地以备后用
        const cacheDir = path.join(__dirname, 'cache');
        await fs.mkdir(cacheDir, { recursive: true });
        const cachePath = path.join(cacheDir, 'unicode-emoji-page.html');
        await fs.writeFile(cachePath, response.data, 'utf8');
        console.log(`💾 页面已缓存到: ${cachePath}`);
        
        return response.data;
        
      } catch (error) {
        console.warn(`⚠️ 下载尝试 ${attempt} 失败:`, error.message);
        
        if (attempt === maxRetries) {
          // 最后一次尝试失败，检查是否有缓存
          const cachePath = path.join(__dirname, 'cache', 'unicode-emoji-page.html');
          try {
            const cachedData = await fs.readFile(cachePath, 'utf8');
            console.log('📂 使用缓存的页面数据');
            return cachedData;
          } catch (cacheError) {
            throw new Error(`下载失败且无可用缓存: ${error.message}`);
          }
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  }

  async parseHTML(html) {
    console.log('🔍 开始解析HTML...');
    const $ = cheerio.load(html);
    
    // 找到主表格
    const table = $('table').first();
    if (!table.length) {
      throw new Error('未找到emoji表格');
    }
    
    const rows = table.find('tr');
    console.log(`📊 找到 ${rows.length} 行数据`);
    
    // 处理每一行
    rows.each((index, row) => {
      if (index % 500 === 0) {
        console.log(`⏳ 处理进度: ${index}/${rows.length} (${((index/rows.length)*100).toFixed(1)}%)`);
      }
      
      this.processRow($, $(row));
    });
    
    console.log(`✨ 解析完成，提取了 ${this.emojis.length} 个emoji`);
  }

  processRow($, $row) {
    try {
      // 检查是否是分类标题行
      const categoryHeader = $row.find('th.bighead').text().trim();
      if (categoryHeader) {
        this.currentCategory = this.normalizeCategory(categoryHeader);
        this.currentSubcategory = '';
        console.log(`📂 发现分类: ${this.currentCategory}`);
        return;
      }
      
      // 检查是否是子分类行
      const subcategoryHeader = $row.find('th.mediumhead').text().trim();
      if (subcategoryHeader) {
        this.currentSubcategory = this.normalizeSubcategory(subcategoryHeader);
        console.log(`  📁 子分类: ${this.currentSubcategory}`);
        return;
      }
      
      // 提取emoji数据
      const cells = $row.find('td');
      if (cells.length >= 15) {
        const emojiData = this.extractEmojiFromCells($, cells);
        if (emojiData) {
          this.emojis.push(emojiData);
          this.processedCount++;
          
          if (this.processedCount % 100 === 0) {
            console.log(`✨ 已提取 ${this.processedCount} 个emoji...`);
          }
        }
      }
      
    } catch (error) {
      // 静默处理单行错误
    }
  }

  extractEmojiFromCells($, cells) {
    try {
      // 提取各个字段 (根据Unicode.org表格结构)
      const number = $(cells[0]).find('a').text().trim() || $(cells[0]).text().trim();
      const unicodeCode = $(cells[1]).text().trim();
      const browserEmoji = $(cells[2]).text().trim();
      
      // CLDR名称通常在最后几列
      let cldrName = '';
      for (let i = cells.length - 1; i >= 10; i--) {
        const cellText = $(cells[i]).text().trim();
        if (cellText && !cellText.match(/^[A-Z]{2}$/)) { // 不是国家代码
          cldrName = cellText;
          break;
        }
      }
      
      // 验证必要字段
      if (!unicodeCode || !browserEmoji || !cldrName) {
        return null;
      }
      
      // 过滤无效数据
      if (!unicodeCode.startsWith('U+') || browserEmoji.length === 0) {
        return null;
      }
      
      // 跳过一些特殊字符
      if (cldrName.includes('VARIATION SELECTOR') || cldrName.includes('COMBINING')) {
        return null;
      }
      
      // 构建emoji数据对象
      const emojiData = {
        number: parseInt(number) || this.processedCount + 1,
        code: unicodeCode,
        emoji: browserEmoji,
        name: cldrName.toLowerCase(),
        category: this.currentCategory || 'Unknown',
        subcategory: this.currentSubcategory || '',
        codepoints: this.extractCodepoints(unicodeCode),
        keywords: this.generateKeywords(cldrName, this.currentCategory, this.currentSubcategory)
      };
      
      return emojiData;
      
    } catch (error) {
      return null;
    }
  }

  normalizeCategory(category) {
    // 清理和标准化分类名称
    const cleaned = category.replace(/^\d+\.\s*/, '').trim();
    return this.categoryMap[cleaned] || cleaned;
  }

  normalizeSubcategory(subcategory) {
    // 清理子分类名称
    return subcategory.replace(/^\d+\.\d+\s*/, '').trim();
  }

  extractCodepoints(unicodeCode) {
    // 从 "U+1F600" 或 "U+1F1E6 U+1F1E8" 格式提取码点
    const matches = unicodeCode.match(/U\+([0-9A-F]+)/g);
    if (matches) {
      return matches.map(match => match.replace('U+', '').toLowerCase());
    }
    return [];
  }

  generateKeywords(name, category, subcategory) {
    // 生成搜索关键词
    const keywords = [];
    
    // 从名称中提取关键词
    const nameWords = name.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    keywords.push(...nameWords);
    
    // 添加分类相关关键词
    if (category) {
      keywords.push(...category.toLowerCase().split(/[&\s]+/).filter(w => w.length > 2));
    }
    
    // 添加子分类关键词
    if (subcategory) {
      keywords.push(...subcategory.toLowerCase().split(/[-\s]+/).filter(w => w.length > 2));
    }
    
    // 去重并返回前5个关键词
    return [...new Set(keywords)].slice(0, 5);
  }

  async scrapeCompleteEmojiList() {
    console.log('🚀 开始爬取完整的Unicode emoji列表...');
    
    try {
      // 下载页面
      const html = await this.downloadPage();
      
      // 解析HTML
      await this.parseHTML(html);
      
      console.log(`🎉 爬取完成！总共提取了 ${this.emojis.length} 个emoji`);
      return this.emojis;
      
    } catch (error) {
      console.error('❌ 爬取过程中出现错误:', error.message);
      throw error;
    }
  }

  async saveResults() {
    console.log('💾 保存结果到文件...');
    
    // 创建输出目录
    const outputDir = path.join(process.cwd(), '..', 'data');
    await fs.mkdir(outputDir, { recursive: true });
    
    // 统计分类信息
    const categories = {};
    this.emojis.forEach(emoji => {
      if (!categories[emoji.category]) {
        categories[emoji.category] = 0;
      }
      categories[emoji.category]++;
    });
    
    // 构建完整数据结构
    const completeData = {
      metadata: {
        source: "Unicode.org emoji charts (complete list)",
        version: "16.0",
        created_at: new Date().toISOString(),
        total_count: this.emojis.length,
        description: "完整的Unicode标准emoji数据，包含所有官方表情符号"
      },
      categories: categories,
      emojis: this.emojis
    };
    
    // 保存完整数据
    const outputPath = path.join(outputDir, 'unicode-emojis-complete.json');
    await fs.writeFile(
      outputPath,
      JSON.stringify(completeData, null, 2),
      'utf8'
    );
    
    console.log(`✅ 完整数据已保存到: ${outputPath}`);
    console.log(`📊 总计: ${this.emojis.length} 个emoji`);
    console.log(`📂 分类统计:`, categories);
    
    return outputPath;
  }
}

// 主执行函数
async function main() {
  const scraper = new RobustUnicodeEmojiScraper();
  
  try {
    await scraper.scrapeCompleteEmojiList();
    await scraper.saveResults();
    
    console.log('🎊 完整emoji爬取任务成功完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('💥 爬取失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = RobustUnicodeEmojiScraper;
