const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs').promises;
const path = require('path');

async function scrapeUnicodeEmojis() {
  console.log('Starting Unicode emoji scraper (simple version)...');
  
  try {
    console.log('Fetching Unicode emoji page...');
    const response = await axios.get('https://unicode.org/emoji/charts/full-emoji-list.html', {
      timeout: 60000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    console.log('Page fetched, parsing HTML...');
    const $ = cheerio.load(response.data);
    
    const emojis = [];
    let currentCategory = '';
    let currentSubcategory = '';
    
    $('table tr').each((index, row) => {
      const $row = $(row);
      
      // Check for category header
      const categoryHeader = $row.find('th.bighead').text().trim();
      if (categoryHeader) {
        currentCategory = categoryHeader;
        return;
      }
      
      // Check for subcategory header
      const subcategoryHeader = $row.find('th.mediumhead').text().trim();
      if (subcategoryHeader) {
        currentSubcategory = subcategoryHeader;
        return;
      }
      
      // Extract emoji data
      const code = $row.find('td.code').text().trim();
      const emoji = $row.find('td.chars').text().trim();
      const name = $row.find('td.name').text().trim();
      
      if (code && emoji && name && code.startsWith('U+')) {
        emojis.push({
          code: code,
          emoji: emoji,
          name: name,
          category: currentCategory,
          subcategory: currentSubcategory
        });
      }
    });
    
    console.log(`Extracted ${emojis.length} emojis`);
    
    // Create output directory
    const outputDir = path.join(process.cwd(), 'data');
    await fs.mkdir(outputDir, { recursive: true });
    
    // Save emoji data
    const outputPath = path.join(outputDir, 'unicode-emojis.json');
    await fs.writeFile(
      outputPath,
      JSON.stringify(emojis, null, 2),
      'utf8'
    );
    
    console.log(`Saved emoji data to ${outputPath}`);
    
    // Create summary
    const categories = {};
    emojis.forEach(emoji => {
      if (!categories[emoji.category]) {
        categories[emoji.category] = {};
      }
      if (!categories[emoji.category][emoji.subcategory]) {
        categories[emoji.category][emoji.subcategory] = 0;
      }
      categories[emoji.category][emoji.subcategory]++;
    });
    
    const summaryPath = path.join(outputDir, 'unicode-emoji-summary.json');
    await fs.writeFile(
      summaryPath,
      JSON.stringify({
        totalEmojis: emojis.length,
        categories: categories,
        scrapedAt: new Date().toISOString()
      }, null, 2),
      'utf8'
    );
    
    console.log(`Saved summary to ${summaryPath}`);
    
  } catch (error) {
    console.error('Error during scraping:', error);
    throw error;
  }
}

// Run the scraper
scrapeUnicodeEmojis()
  .then(() => {
    console.log('Scraping completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Scraping failed:', error);
    process.exit(1);
  });