/**
 * 使用curl获取Unicode.org emoji数据的爬取脚本
 * 更可靠的网络请求方式
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class CurlEmojiScraper {
  constructor() {
    this.emojis = [];
    this.currentCategory = '';
    this.currentSubcategory = '';
    this.processedCount = 0;
  }

  async scrapeWithCurl() {
    console.log('🚀 使用curl开始爬取Unicode.org emoji数据...');
    
    try {
      // 使用curl获取页面
      console.log('🌐 正在使用curl获取页面...');
      const { stdout, stderr } = await execAsync(
        'curl -L -A "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36" ' +
        '--connect-timeout 30 --max-time 300 ' +
        '"https://unicode.org/emoji/charts/full-emoji-list.html"',
        { maxBuffer: 50 * 1024 * 1024 } // 50MB buffer
      );
      
      if (stderr) {
        console.warn('⚠️ curl警告:', stderr);
      }
      
      if (!stdout || stdout.length < 1000) {
        throw new Error('获取的页面内容太少，可能下载失败');
      }
      
      console.log(`✅ 页面获取成功，大小: ${(stdout.length / 1024 / 1024).toFixed(2)}MB`);
      
      // 保存原始HTML用于调试
      const htmlPath = path.join(__dirname, '..', 'data', 'unicode-full-emoji-list.html');
      fs.writeFileSync(htmlPath, stdout, 'utf8');
      console.log(`💾 原始HTML已保存到: ${htmlPath}`);
      
      // 解析HTML
      await this.parseHTML(stdout);
      
      console.log(`🎉 爬取完成！总共提取了 ${this.emojis.length} 个emoji`);
      return this.emojis;
      
    } catch (error) {
      console.error('❌ 爬取过程中出现错误:', error.message);
      throw error;
    }
  }

  async parseHTML(html) {
    console.log('📝 开始解析HTML内容...');
    
    // 使用正则表达式解析表格行
    const tableRowRegex = /<tr[^>]*>(.*?)<\/tr>/gs;
    const rows = html.match(tableRowRegex) || [];
    
    console.log(`📊 找到 ${rows.length} 个表格行`);
    
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      
      // 检查进度
      if (i % 1000 === 0) {
        console.log(`⏳ 处理进度: ${i}/${rows.length} (${((i/rows.length)*100).toFixed(1)}%)`);
      }
      
      // 检查是否是分类标题行
      if (this.isCategoryRow(row)) {
        this.currentCategory = this.extractCategory(row);
        this.currentSubcategory = '';
        console.log(`📂 发现分类: ${this.currentCategory}`);
        continue;
      }
      
      // 检查是否是子分类行
      if (this.isSubcategoryRow(row)) {
        this.currentSubcategory = this.extractSubcategory(row);
        console.log(`  📁 子分类: ${this.currentSubcategory}`);
        continue;
      }
      
      // 提取emoji数据行
      const emojiData = this.extractEmojiFromRow(row);
      if (emojiData) {
        this.emojis.push(emojiData);
        this.processedCount++;
        
        if (this.processedCount % 100 === 0) {
          console.log(`✨ 已提取 ${this.processedCount} 个emoji...`);
        }
      }
    }
  }

  isCategoryRow(row) {
    return /<th[^>]*colspan[^>]*>/i.test(row) && 
           !row.includes('№') && 
           !row.includes('Code') &&
           !row.includes('Browser');
  }

  extractCategory(row) {
    const match = row.match(/<th[^>]*colspan[^>]*>([^<]+)</i);
    if (match) {
      return this.cleanText(match[1]);
    }
    return '';
  }

  isSubcategoryRow(row) {
    return /<td[^>]*colspan[^>]*>/i.test(row) && 
           !row.includes('<a href=');
  }

  extractSubcategory(row) {
    const match = row.match(/<td[^>]*colspan[^>]*>([^<]+)</i);
    if (match) {
      return this.cleanText(match[1]);
    }
    return '';
  }

  extractEmojiFromRow(row) {
    try {
      // 提取所有td单元格
      const cellRegex = /<td[^>]*>(.*?)<\/td>/gs;
      const cells = [];
      let match;
      
      while ((match = cellRegex.exec(row)) !== null) {
        cells.push(match[1]);
      }
      
      // 需要至少8个单元格才是有效的emoji行
      if (cells.length < 8) {
        return null;
      }
      
      // 第1列: 序号
      const numberText = this.cleanText(cells[0]);
      const number = parseInt(numberText) || 0;
      
      // 第2列: Unicode代码
      const codeCell = cells[1];
      const codeMatch = codeCell.match(/<a[^>]*>([^<]+)<\/a>/);
      const unicodeCode = codeMatch ? this.cleanText(codeMatch[1]) : '';
      
      // 第3列: 浏览器显示的emoji
      const browserEmoji = this.cleanText(cells[2]);
      
      // 最后一列: CLDR Short Name
      const cldrName = this.cleanText(cells[cells.length - 1]);
      
      // 验证必要字段
      if (!unicodeCode || !browserEmoji || !cldrName || number === 0) {
        return null;
      }
      
      // 构建emoji数据对象
      const emojiData = {
        number: number,
        code: unicodeCode,
        emoji: browserEmoji,
        name: cldrName,
        category: this.currentCategory || 'Unknown',
        subcategory: this.currentSubcategory || '',
        codepoints: this.extractCodepoints(unicodeCode),
        keywords: this.generateKeywords(cldrName, this.currentCategory, this.currentSubcategory)
      };
      
      return emojiData;
      
    } catch (error) {
      return null;
    }
  }

  extractCodepoints(unicodeCode) {
    const matches = unicodeCode.match(/U\+([0-9A-F]+)/g);
    if (matches) {
      return matches.map(match => match.replace('U+', '').toLowerCase());
    }
    return [];
  }

  generateKeywords(name, category, subcategory) {
    const keywords = new Set();
    
    if (name) {
      name.toLowerCase()
          .replace(/[^\w\s-]/g, ' ')
          .split(/[\s-_]+/)
          .forEach(word => {
            if (word.length > 1) keywords.add(word);
          });
    }
    
    if (category) {
      category.toLowerCase()
             .replace(/[^\w\s-]/g, ' ')
             .split(/[\s&-_]+/)
             .forEach(word => {
               if (word.length > 1) keywords.add(word);
             });
    }
    
    if (subcategory) {
      subcategory.toLowerCase()
                 .replace(/[^\w\s-]/g, ' ')
                 .split(/[\s-_]+/)
                 .forEach(word => {
                   if (word.length > 1) keywords.add(word);
                 });
    }
    
    return Array.from(keywords).slice(0, 10);
  }

  cleanText(text) {
    return text
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#x([0-9A-F]+);/gi, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
      .replace(/&#(\d+);/g, (match, dec) => String.fromCharCode(parseInt(dec, 10)))
      .replace(/\s+/g, ' ')
      .trim();
  }

  async saveToFile(filename = 'unicode-emojis-full.json') {
    const outputDir = path.join(__dirname, '..', 'data');
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const filePath = path.join(outputDir, filename);
    
    const data = {
      metadata: {
        source: 'https://unicode.org/emoji/charts/full-emoji-list.html',
        version: '16.0',
        scraped_at: new Date().toISOString(),
        total_count: this.emojis.length,
        categories: this.getCategoryStats()
      },
      emojis: this.emojis
    };
    
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`💾 完整数据已保存到: ${filePath}`);
    
    // 保存简化版本
    const simplifiedData = {
      metadata: data.metadata,
      emojis: this.emojis.map(emoji => ({
        code: emoji.code,
        emoji: emoji.emoji,
        name: emoji.name,
        category: emoji.category,
        keywords: emoji.keywords.slice(0, 5)
      }))
    };
    
    const simplifiedPath = filePath.replace('.json', '-simplified.json');
    fs.writeFileSync(simplifiedPath, JSON.stringify(simplifiedData, null, 2), 'utf8');
    console.log(`💾 简化数据已保存到: ${simplifiedPath}`);
    
    return { fullPath: filePath, simplifiedPath };
  }

  getCategoryStats() {
    const stats = {};
    this.emojis.forEach(emoji => {
      if (!stats[emoji.category]) {
        stats[emoji.category] = 0;
      }
      stats[emoji.category]++;
    });
    return stats;
  }

  printSummary() {
    console.log('\n📊 === 爬取统计摘要 ===');
    console.log(`总emoji数量: ${this.emojis.length}`);
    console.log('\n分类统计:');
    
    const stats = this.getCategoryStats();
    Object.entries(stats)
          .sort(([,a], [,b]) => b - a)
          .forEach(([category, count]) => {
            console.log(`  ${category}: ${count} 个`);
          });
    
    console.log('\n前10个emoji示例:');
    this.emojis.slice(0, 10).forEach((emoji, index) => {
      console.log(`  ${index + 1}. ${emoji.emoji} ${emoji.code} - ${emoji.name}`);
    });
  }
}

// 主函数
async function main() {
  const scraper = new CurlEmojiScraper();
  
  try {
    console.log('🎯 开始使用curl爬取Unicode.org emoji数据');
    console.log('⏰ 预计需要 2-5 分钟，请耐心等待...\n');
    
    await scraper.scrapeWithCurl();
    const { fullPath, simplifiedPath } = await scraper.saveToFile();
    
    scraper.printSummary();
    
    console.log('\n🎉 === 任务完成 ===');
    console.log(`✅ 完整数据文件: ${fullPath}`);
    console.log(`✅ 简化数据文件: ${simplifiedPath}`);
    
  } catch (error) {
    console.error('\n❌ === 任务失败 ===');
    console.error('错误详情:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = CurlEmojiScraper;
