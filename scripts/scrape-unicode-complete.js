const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

class CompleteUnicodeEmojiScraper {
  constructor() {
    this.emojis = [];
    this.currentCategory = '';
    this.currentSubcategory = '';
    this.processedCount = 0;
    this.browser = null;
    this.page = null;
    
    // 分类映射
    this.categoryMap = {
      'Smileys & Emotion': 'Smileys & Emotion',
      'People & Body': 'People & Body', 
      'Animals & Nature': 'Animals & Nature',
      'Food & Drink': 'Food & Drink',
      'Travel & Places': 'Travel & Places',
      'Activities': 'Activities',
      'Objects': 'Objects',
      'Symbols': 'Symbols',
      'Flags': 'Flags'
    };
  }

  async initialize() {
    console.log('🚀 初始化浏览器...');
    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
    
    this.page = await this.browser.newPage();
    
    // 设置用户代理和视口
    await this.page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    await this.page.setViewport({ width: 1920, height: 1080 });
    
    // 设置超时时间
    this.page.setDefaultTimeout(60000);
    this.page.setDefaultNavigationTimeout(60000);
  }

  async scrapeCompleteEmojiList() {
    console.log('🌐 开始爬取完整的Unicode emoji列表...');
    
    try {
      await this.initialize();
      
      console.log('📥 正在加载Unicode emoji页面...');
      await this.page.goto('https://unicode.org/emoji/charts/full-emoji-list.html', {
        waitUntil: 'networkidle2',
        timeout: 120000
      });
      
      console.log('✅ 页面加载成功，开始解析...');
      
      // 等待表格加载完成
      await this.page.waitForSelector('table', { timeout: 30000 });
      
      // 获取所有表格行
      const rows = await this.page.$$('table tr');
      console.log(`📊 找到 ${rows.length} 行数据`);
      
      // 分批处理行数据，避免内存溢出
      const batchSize = 100;
      for (let i = 0; i < rows.length; i += batchSize) {
        const batch = rows.slice(i, i + batchSize);
        console.log(`⏳ 处理批次 ${Math.floor(i/batchSize) + 1}/${Math.ceil(rows.length/batchSize)} (${i}-${Math.min(i + batchSize, rows.length)})`);
        
        await this.processBatch(batch);
        
        // 每处理一批后稍作休息，避免过载
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log(`🎉 爬取完成！总共提取了 ${this.emojis.length} 个emoji`);
      return this.emojis;
      
    } catch (error) {
      console.error('❌ 爬取过程中出现错误:', error.message);
      throw error;
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async processBatch(rows) {
    for (const row of rows) {
      try {
        // 检查是否是分类标题行
        const categoryHeader = await row.$eval('th.bighead', el => el.textContent.trim()).catch(() => null);
        if (categoryHeader) {
          this.currentCategory = this.normalizeCategory(categoryHeader);
          this.currentSubcategory = '';
          console.log(`📂 发现分类: ${this.currentCategory}`);
          continue;
        }
        
        // 检查是否是子分类行
        const subcategoryHeader = await row.$eval('th.mediumhead', el => el.textContent.trim()).catch(() => null);
        if (subcategoryHeader) {
          this.currentSubcategory = this.normalizeSubcategory(subcategoryHeader);
          console.log(`  📁 子分类: ${this.currentSubcategory}`);
          continue;
        }
        
        // 提取emoji数据
        const emojiData = await this.extractEmojiFromRow(row);
        if (emojiData) {
          this.emojis.push(emojiData);
          this.processedCount++;
          
          if (this.processedCount % 50 === 0) {
            console.log(`✨ 已提取 ${this.processedCount} 个emoji...`);
          }
        }
        
      } catch (error) {
        // 静默处理单行错误，继续处理下一行
        continue;
      }
    }
  }

  async extractEmojiFromRow(row) {
    try {
      // 检查是否有足够的单元格
      const cells = await row.$$('td');
      if (cells.length < 8) {
        return null;
      }
      
      // 提取各个字段
      const number = await cells[0].$eval('a', el => el.textContent.trim()).catch(() => '');
      const unicodeCode = await cells[1].evaluate(el => el.textContent.trim()).catch(() => '');
      const browserEmoji = await cells[2].evaluate(el => el.textContent.trim()).catch(() => '');
      const cldrName = await cells[15].evaluate(el => el.textContent.trim()).catch(() => '');
      
      // 验证必要字段
      if (!number || !unicodeCode || !browserEmoji || !cldrName) {
        return null;
      }
      
      // 过滤无效数据
      if (!unicodeCode.startsWith('U+') || browserEmoji.length === 0) {
        return null;
      }
      
      // 构建emoji数据对象
      const emojiData = {
        number: parseInt(number) || this.processedCount + 1,
        code: unicodeCode,
        emoji: browserEmoji,
        name: cldrName,
        category: this.currentCategory || 'Unknown',
        subcategory: this.currentSubcategory || '',
        codepoints: this.extractCodepoints(unicodeCode),
        keywords: this.generateKeywords(cldrName, this.currentCategory, this.currentSubcategory)
      };
      
      return emojiData;
      
    } catch (error) {
      return null;
    }
  }

  normalizeCategory(category) {
    // 清理和标准化分类名称
    const cleaned = category.replace(/^\d+\.\s*/, '').trim();
    return this.categoryMap[cleaned] || cleaned;
  }

  normalizeSubcategory(subcategory) {
    // 清理子分类名称
    return subcategory.replace(/^\d+\.\d+\s*/, '').trim();
  }

  extractCodepoints(unicodeCode) {
    // 从 "U+1F600" 或 "U+1F1E6 U+1F1E8" 格式提取码点
    const matches = unicodeCode.match(/U\+([0-9A-F]+)/g);
    if (matches) {
      return matches.map(match => match.replace('U+', '').toLowerCase());
    }
    return [];
  }

  generateKeywords(name, category, subcategory) {
    // 生成搜索关键词
    const keywords = [];
    
    // 从名称中提取关键词
    const nameWords = name.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    keywords.push(...nameWords);
    
    // 添加分类相关关键词
    if (category) {
      keywords.push(...category.toLowerCase().split(/[&\s]+/).filter(w => w.length > 2));
    }
    
    // 添加子分类关键词
    if (subcategory) {
      keywords.push(...subcategory.toLowerCase().split(/[-\s]+/).filter(w => w.length > 2));
    }
    
    // 去重并返回前5个关键词
    return [...new Set(keywords)].slice(0, 5);
  }

  async saveResults() {
    console.log('💾 保存结果到文件...');
    
    // 创建输出目录
    const outputDir = path.join(process.cwd(), 'data');
    await fs.mkdir(outputDir, { recursive: true });
    
    // 统计分类信息
    const categories = {};
    this.emojis.forEach(emoji => {
      if (!categories[emoji.category]) {
        categories[emoji.category] = 0;
      }
      categories[emoji.category]++;
    });
    
    // 构建完整数据结构
    const completeData = {
      metadata: {
        source: "Unicode.org emoji charts (complete list)",
        version: "16.0",
        created_at: new Date().toISOString(),
        total_count: this.emojis.length,
        description: "完整的Unicode标准emoji数据，包含所有官方表情符号"
      },
      categories: categories,
      emojis: this.emojis
    };
    
    // 保存完整数据
    const outputPath = path.join(outputDir, 'unicode-emojis-complete.json');
    await fs.writeFile(
      outputPath,
      JSON.stringify(completeData, null, 2),
      'utf8'
    );
    
    console.log(`✅ 完整数据已保存到: ${outputPath}`);
    console.log(`📊 总计: ${this.emojis.length} 个emoji`);
    console.log(`📂 分类统计:`, categories);
    
    return outputPath;
  }
}

// 主执行函数
async function main() {
  const scraper = new CompleteUnicodeEmojiScraper();
  
  try {
    await scraper.scrapeCompleteEmojiList();
    await scraper.saveResults();
    
    console.log('🎊 完整emoji爬取任务成功完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('💥 爬取失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = CompleteUnicodeEmojiScraper;
