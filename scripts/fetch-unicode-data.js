const https = require('https');
const fs = require('fs').promises;
const path = require('path');

// 使用Unicode官方数据文件
async function fetchUnicodeData() {
  console.log('Fetching Unicode emoji data from official data files...');
  
  try {
    // Unicode官方emoji数据文件URL
    const url = 'https://unicode.org/Public/emoji/15.1/emoji-test.txt';
    
    const response = await new Promise((resolve, reject) => {
      https.get(url, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve(data));
        res.on('error', reject);
      });
    });
    
    console.log('Parsing emoji data...');
    const emojis = parseEmojiTestFile(response);
    
    console.log(`Parsed ${emojis.length} emojis`);
    
    // 保存完整数据
    const outputDir = path.join(process.cwd(), 'data');
    await fs.mkdir(outputDir, { recursive: true });
    
    const outputPath = path.join(outputDir, 'unicode-emojis-all.json');
    await fs.writeFile(outputPath, JSON.stringify(emojis, null, 2), 'utf8');
    console.log(`Saved to ${outputPath}`);
    
    // 创建统计
    const stats = createStatistics(emojis);
    const statsPath = path.join(outputDir, 'unicode-emoji-all-stats.json');
    await fs.writeFile(statsPath, JSON.stringify(stats, null, 2), 'utf8');
    console.log(`Statistics saved to ${statsPath}`);
    
    // 打印统计信息
    console.log('\n=== Unicode Emoji Statistics ===');
    console.log(`Total emojis: ${stats.totalEmojis}`);
    console.log(`\nCategories:`);
    Object.entries(stats.categories).forEach(([category, data]) => {
      console.log(`\n${category}: ${data.total} emojis`);
      Object.entries(data.subcategories).forEach(([subcategory, count]) => {
        console.log(`  - ${subcategory}: ${count}`);
      });
    });
    
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
}

function parseEmojiTestFile(text) {
  const lines = text.split('\n');
  const emojis = [];
  let currentGroup = '';
  let currentSubgroup = '';
  
  for (const line of lines) {
    // 跳过空行
    if (!line.trim()) continue;
    
    // 解析组信息
    if (line.includes('# group:')) {
      currentGroup = line.split('# group:')[1].trim();
      continue;
    }
    
    // 解析子组信息
    if (line.includes('# subgroup:')) {
      currentSubgroup = line.split('# subgroup:')[1].trim();
      continue;
    }
    
    // 跳过其他注释
    if (line.startsWith('#')) continue;
    
    // 解析emoji行
    // 格式: 1F600  ; fully-qualified  # 😀 E1.0 grinning face
    // 或: 1F600 1F3FB ; fully-qualified # 😀🏻 E1.0 grinning face: light skin tone
    const match = line.match(/^([0-9A-F\s]+)\s*;\s*([\w-]+)\s*#\s*(.+?)\s+E[\d.]+\s+(.+)$/);
    if (match) {
      const [, codePoints, status, emoji, name] = match;
      
      // 只包含fully-qualified的emoji
      if (status === 'fully-qualified') {
        const codes = codePoints.trim().split(/\s+/);
        const codeString = codes.map(c => 'U+' + c).join(' ');
        
        emojis.push({
          code: codeString,
          emoji: emoji.trim(),
          name: name.trim(),
          category: currentGroup,
          subcategory: currentSubgroup,
          codePoints: codes
        });
      }
    }
  }
  
  return emojis;
}

function createStatistics(emojis) {
  const categories = {};
  const uniqueBaseEmojis = new Set();
  
  emojis.forEach(emoji => {
    // 计算基础emoji（不包含修饰符）
    const baseEmoji = emoji.codePoints[0];
    uniqueBaseEmojis.add(baseEmoji);
    
    if (!categories[emoji.category]) {
      categories[emoji.category] = {
        total: 0,
        subcategories: {}
      };
    }
    
    categories[emoji.category].total++;
    
    if (!categories[emoji.category].subcategories[emoji.subcategory]) {
      categories[emoji.category].subcategories[emoji.subcategory] = 0;
    }
    categories[emoji.category].subcategories[emoji.subcategory]++;
  });
  
  return {
    totalEmojis: emojis.length,
    uniqueBaseEmojis: uniqueBaseEmojis.size,
    categories: categories,
    dataSource: 'Unicode 15.1 emoji-test.txt',
    fetchedAt: new Date().toISOString()
  };
}

// 运行获取
fetchUnicodeData()
  .then(() => {
    console.log('\nSuccessfully fetched all Unicode emojis!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\nFailed:', error);
    process.exit(1);
  });