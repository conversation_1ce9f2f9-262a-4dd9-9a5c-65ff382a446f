# Files Containing Chinese Text - Translation Needed

## Summary
Found 20 files containing Chinese text across the codebase that need translation.

## Core Data Files

### 1. `/lib/data.ts`
- **Chinese Content**: All emoji names and descriptions are in Chinese
- **Examples**:
  - name: '微笑' → 'Smile'
  - name: '快乐' → 'Happy'
  - description: 'TikTok专属粉色微笑表情' → 'TikTok exclusive pink smile emoji'
- **Total Items**: 40 emoji entries with Chinese names and descriptions

### 2. `/lib/types.ts`
- **Chinese Content**: Comments explaining type fields
- **Examples**:
  - `// 用于显示的替代emoji（用于不支持TikTok表情的地方）`
  - `// TikTok专属表情的图片URL`
  - `// 详细描述`

## React Components

### 3. `/components/emoji-card.tsx`
- **Chinese Content**: UI labels and text
- **Key Translations Needed**:
  - "使用量" → "Usage"
  - "次" → "times"
  - "点击复制" → "Click to copy"
  - "已复制!" → "Copied!"

### 4. `/components/tiktok-emoji.tsx`
- **Chinese Content**: Component title and description
- **Key Translations Needed**:
  - "如何使用TikTok隐藏表情符号" → "How to Use TikTok Hidden Emojis"
  - Various instructional text

### 5. `/components/navigation.tsx`
- **Chinese Content**: Navigation menu items
- **Key Translations Needed**:
  - "隐藏表情" → "Hidden Emojis"
  - "热门组合" → "Popular Combos"
  - "标题生成器" → "Title Generator"
  - "搜索" → "Search"

## Page Components

### 6. `/app/page.tsx`
- **Chinese Content**: Homepage hero section and features
- **Key Translations Needed**:
  - "解锁TikTok秘密表情符号" → "Unlock TikTok Secret Emojis"
  - Feature titles and descriptions

### 7. `/app/hidden-emojis/page.tsx`
- **Chinese Content**: Page title and descriptions
- **Key Translations Needed**:
  - "TikTok隐藏表情大全" → "Complete TikTok Hidden Emojis"

### 8. `/app/popular-combos/page.tsx`
- **Chinese Content**: Combo names and descriptions
- **Examples**:
  - "爱心组合" → "Love Combo"
  - "快乐组合" → "Happy Combo"

### 9. `/app/search/page.tsx`
- **Chinese Content**: Search UI elements
- **Key Translations Needed**:
  - "搜索TikTok表情符号" → "Search TikTok Emojis"
  - "输入表情名称或关键词..." → "Enter emoji name or keyword..."

### 10. `/app/title-generator/page.tsx`
- **Chinese Content**: Title generator UI
- **Key Translations Needed**:
  - "TikTok标题生成器" → "TikTok Title Generator"
  - "输入您的视频主题..." → "Enter your video topic..."

### 11. `/app/emoji/[emoji]/page.tsx`
- **Chinese Content**: Detail page elements
- **Key Translations Needed**:
  - "使用频率" → "Usage Frequency"
  - "表情描述" → "Emoji Description"

### 12. `/app/layout.tsx`
- **Chinese Content**: Page metadata
- **Key Translations Needed**:
  - title and description meta tags

## Static HTML Files (UI folder)
All HTML files in the `/ui` folder contain Chinese text:

### 13-20. UI HTML Files:
- `/ui/index.html`
- `/ui/emoji-detail.html`
- `/ui/hidden-emojis.html`
- `/ui/popular-combos.html`
- `/ui/search.html`
- `/ui/title-generator.html`
- `/ui/styles.css`

## Other Files

### README.md
Contains Chinese project documentation that needs translation.

## Translation Priority

1. **High Priority**: 
   - `/lib/data.ts` (core data)
   - Navigation and main page components
   - Page titles and metadata

2. **Medium Priority**:
   - UI components and labels
   - Feature descriptions

3. **Low Priority**:
   - Comments in code
   - Static HTML files in `/ui` folder

## Recommended Approach

1. Create a translations file or i18n system
2. Extract all Chinese strings to a central location
3. Implement language switching functionality
4. Update all components to use translated strings