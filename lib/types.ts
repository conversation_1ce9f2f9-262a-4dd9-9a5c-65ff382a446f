export interface HiddenEmoji {
  code: string
  emoji: string // Alternative emoji for display (for places that don't support TikTok emojis)
  name: string
  usage?: string
  imageUrl?: string // Image URL for TikTok exclusive emoji
  description?: string // Detailed description
}

export interface EmojiCombo {
  id: string
  emojis: string
  meaning: string
  usage: string
  popularity?: number
}

export interface Emoji {
  char: string
  name: string
  meaning: string
  tiktokMeaning?: string
  category: string
  usage?: string
}

export interface TitleStyle {
  id: string
  name: string
  label: string
}