// Unicode emoji data types and utilities
import unicodeData from '@/data/unicode-emojis-generated.json'

export interface UnicodeEmoji {
  number: number
  code: string
  emoji: string
  name: string
  category: string
  subcategory: string
  codepoints: string[]
  keywords: string[]
}

export interface CategoryInfo {
  id: string
  name: string
  zhName: string
  icon: string
  count: number
  description: string
}

// Map category names to URL-friendly IDs and add Chinese names
export const categoryMapping: Record<string, CategoryInfo> = {
  'Smileys & Emotion': {
    id: 'smileys-emotion',
    name: 'Smileys & Emotion',
    zhName: '情绪与感受',
    icon: '😊',
    count: unicodeData.categories['Smileys & Emotion'] || 0,
    description: 'Express your emotions with faces, hearts, and feelings'
  },
  'People & Body': {
    id: 'people-body',
    name: 'People & Body',
    zhName: '人物与身体',
    icon: '👋',
    count: unicodeData.categories['People & Body'] || 0,
    description: 'Hand gestures, body parts, and people'
  },
  'Animals & Nature': {
    id: 'animals-nature',
    name: 'Animals & Nature',
    zhName: '动物与自然',
    icon: '🐱',
    count: unicodeData.categories['Animals & Nature'] || 0,
    description: 'Animals, plants, and nature'
  },
  'Food & Drink': {
    id: 'food-drink',
    name: 'Food & Drink',
    zhName: '餐饮',
    icon: '🍔',
    count: unicodeData.categories['Food & Drink'] || 0,
    description: 'Delicious food and refreshing drinks'
  },
  'Travel & Places': {
    id: 'travel-places',
    name: 'Travel & Places',
    zhName: '旅行与地点',
    icon: '✈️',
    count: unicodeData.categories['Travel & Places'] || 0,
    description: 'Transportation, landmarks, and places'
  },
  'Activities': {
    id: 'activities',
    name: 'Activities',
    zhName: '活动与体育',
    icon: '⚽',
    count: unicodeData.categories['Activities'] || 0,
    description: 'Sports, games, and activities'
  },
  'Objects': {
    id: 'objects',
    name: 'Objects',
    zhName: '物体',
    icon: '💎',
    count: unicodeData.categories['Objects'] || 0,
    description: 'Everyday objects and tools'
  },
  'Symbols': {
    id: 'symbols',
    name: 'Symbols',
    zhName: '符号',
    icon: '❤️',
    count: unicodeData.categories['Symbols'] || 0,
    description: 'Hearts, arrows, and various symbols'
  }
}

// Get all categories as array
export const unicodeCategories = Object.values(categoryMapping)

// Get all emojis
export const unicodeEmojis: UnicodeEmoji[] = unicodeData.emojis

// Get emojis by category
export function getEmojisByCategory(categoryName: string): UnicodeEmoji[] {
  return unicodeEmojis.filter(emoji => emoji.category === categoryName)
}

// Get category info by ID
export function getCategoryByIdOrName(idOrName: string): CategoryInfo | undefined {
  // First try to find by ID
  const categoryByID = Object.values(categoryMapping).find(cat => cat.id === idOrName)
  if (categoryByID) return categoryByID
  
  // Then try to find by name
  return categoryMapping[idOrName]
}

// Search emojis
export function searchUnicodeEmojis(query: string): UnicodeEmoji[] {
  const lowerQuery = query.toLowerCase()
  return unicodeEmojis.filter(emoji => 
    emoji.name.toLowerCase().includes(lowerQuery) ||
    emoji.keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery)) ||
    emoji.emoji === query
  )
}

// Get random emojis from a category
export function getRandomEmojis(category?: string, count: number = 8): UnicodeEmoji[] {
  const source = category ? getEmojisByCategory(category) : unicodeEmojis
  const shuffled = [...source].sort(() => Math.random() - 0.5)
  return shuffled.slice(0, count)
}