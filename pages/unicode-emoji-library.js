import { useState, useEffect } from 'react';
import Head from 'next/head';
import emojiData from '../data/unicode-emojis-generated.json';

export default function UnicodeEmojiLibrary() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredEmojis, setFilteredEmojis] = useState(emojiData.emojis);

  useEffect(() => {
    let filtered = emojiData.emojis;

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(emoji => emoji.category === selectedCategory);
    }

    // 按搜索词过滤
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(emoji => 
        emoji.name.toLowerCase().includes(lowerSearchTerm) ||
        emoji.keywords.some(keyword => keyword.toLowerCase().includes(lowerSearchTerm)) ||
        emoji.code.toLowerCase().includes(lowerSearchTerm)
      );
    }

    setFilteredEmojis(filtered);
  }, [searchTerm, selectedCategory]);

  const copyToClipboard = async (text, type) => {
    try {
      await navigator.clipboard.writeText(text);
      // 可以添加一个临时的成功提示
      console.log(`已复制${type}: ${text}`);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const categories = Object.keys(emojiData.categories);

  return (
    <>
      <Head>
        <title>Unicode Emoji 库 - TikTok Emoji</title>
        <meta name="description" content="完整的Unicode标准emoji表情符号库，包含最新的emoji 16.0版本" />
        <meta name="keywords" content="unicode emoji, 表情符号, emoji库, TikTok emoji" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 头部 */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Unicode Emoji 库
              </h1>
              <p className="text-gray-600 mb-4">
                精选的 {emojiData.metadata.total_count} 个Unicode标准表情符号
              </p>
              <div className="text-sm text-gray-500">
                数据版本: {emojiData.metadata.version} | 数据源: {emojiData.metadata.source}
              </div>
            </div>
          </div>
        </div>

        {/* 搜索和过滤 */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* 搜索框 */}
              <div className="flex-1">
                <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                  搜索表情符号
                </label>
                <input
                  type="text"
                  id="search"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="输入名称、关键词或Unicode代码..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* 分类过滤 */}
              <div className="md:w-64">
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  选择分类
                </label>
                <select
                  id="category"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="all">所有分类 ({emojiData.metadata.total_count})</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category} ({emojiData.categories[category]})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* 搜索结果统计 */}
            <div className="mt-4 text-sm text-gray-600">
              显示 {filteredEmojis.length} 个表情符号
              {searchTerm && ` (搜索: "${searchTerm}")`}
              {selectedCategory !== 'all' && ` (分类: ${selectedCategory})`}
            </div>
          </div>

          {/* Emoji 网格 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredEmojis.map((emoji) => (
              <div
                key={emoji.number}
                className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 p-4"
              >
                {/* Emoji 显示 */}
                <div className="text-center mb-3">
                  <div 
                    className="text-4xl mb-2 cursor-pointer hover:scale-110 transition-transform duration-200"
                    onClick={() => copyToClipboard(emoji.emoji, 'emoji')}
                    title="点击复制emoji"
                  >
                    {emoji.emoji}
                  </div>
                  <div className="text-sm font-medium text-gray-900 mb-1">
                    {emoji.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {emoji.category}
                  </div>
                </div>

                {/* Unicode 代码 */}
                <div className="mb-3">
                  <div 
                    className="text-xs font-mono bg-gray-100 px-2 py-1 rounded cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                    onClick={() => copyToClipboard(emoji.code, 'Unicode代码')}
                    title="点击复制Unicode代码"
                  >
                    {emoji.code}
                  </div>
                </div>

                {/* 关键词 */}
                <div className="flex flex-wrap gap-1">
                  {emoji.keywords.slice(0, 3).map((keyword, index) => (
                    <span
                      key={index}
                      className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full"
                    >
                      {keyword}
                    </span>
                  ))}
                  {emoji.keywords.length > 3 && (
                    <span className="text-xs text-gray-500">
                      +{emoji.keywords.length - 3}
                    </span>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="mt-3 flex gap-2">
                  <button
                    onClick={() => copyToClipboard(emoji.emoji, 'emoji')}
                    className="flex-1 text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors duration-200"
                  >
                    复制
                  </button>
                  <button
                    onClick={() => copyToClipboard(emoji.code, 'Unicode代码')}
                    className="flex-1 text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600 transition-colors duration-200"
                  >
                    代码
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* 无结果提示 */}
          {filteredEmojis.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg mb-2">😔</div>
              <div className="text-gray-600">
                没有找到匹配的表情符号
              </div>
              <div className="text-sm text-gray-500 mt-2">
                尝试使用不同的搜索词或选择其他分类
              </div>
            </div>
          )}
        </div>

        {/* 页脚信息 */}
        <div className="bg-white border-t mt-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="text-center text-sm text-gray-500">
              <p className="mb-2">
                数据来源: Unicode.org | 版本: {emojiData.metadata.version}
              </p>
              <p>
                包含 {emojiData.metadata.total_count} 个精选的Unicode标准表情符号
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
