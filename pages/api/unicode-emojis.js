import emojiData from '../../data/unicode-emojis-generated.json';

export default function handler(req, res) {
  const { method, query } = req;

  // 只允许GET请求
  if (method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    let result = { ...emojiData };
    let emojis = [...emojiData.emojis];

    // 处理查询参数
    const {
      search,
      category,
      limit,
      offset,
      format
    } = query;

    // 按分类过滤
    if (category && category !== 'all') {
      emojis = emojis.filter(emoji => 
        emoji.category.toLowerCase() === category.toLowerCase()
      );
    }

    // 按搜索词过滤
    if (search) {
      const searchTerm = search.toLowerCase();
      emojis = emojis.filter(emoji => 
        emoji.name.toLowerCase().includes(searchTerm) ||
        emoji.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm)) ||
        emoji.code.toLowerCase().includes(searchTerm) ||
        emoji.category.toLowerCase().includes(searchTerm)
      );
    }

    // 分页处理
    const totalCount = emojis.length;
    const limitNum = parseInt(limit) || totalCount;
    const offsetNum = parseInt(offset) || 0;
    
    if (limitNum > 0) {
      emojis = emojis.slice(offsetNum, offsetNum + limitNum);
    }

    // 更新结果
    result.emojis = emojis;
    result.pagination = {
      total: totalCount,
      limit: limitNum,
      offset: offsetNum,
      returned: emojis.length
    };

    // 处理不同的输出格式
    if (format === 'csv') {
      // 返回CSV格式
      const headers = ['Number', 'Code', 'Emoji', 'Name', 'Category', 'Subcategory', 'Keywords'];
      const csvLines = [headers.join(',')];
      
      emojis.forEach(emoji => {
        const row = [
          emoji.number,
          `"${emoji.code}"`,
          `"${emoji.emoji}"`,
          `"${emoji.name}"`,
          `"${emoji.category}"`,
          `"${emoji.subcategory || ''}"`,
          `"${emoji.keywords.join('; ')}"`
        ];
        csvLines.push(row.join(','));
      });
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="unicode-emojis.csv"');
      return res.status(200).send(csvLines.join('\n'));
    }

    if (format === 'simple') {
      // 返回简化格式，只包含基本信息
      result.emojis = emojis.map(emoji => ({
        code: emoji.code,
        emoji: emoji.emoji,
        name: emoji.name,
        category: emoji.category
      }));
    }

    // 默认返回JSON格式
    res.status(200).json(result);

  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

// API使用说明
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}

/*
API使用示例:

1. 获取所有emoji:
   GET /api/unicode-emojis

2. 按分类获取:
   GET /api/unicode-emojis?category=Smileys%20%26%20Emotion

3. 搜索emoji:
   GET /api/unicode-emojis?search=smile

4. 分页获取:
   GET /api/unicode-emojis?limit=10&offset=0

5. 获取CSV格式:
   GET /api/unicode-emojis?format=csv

6. 获取简化格式:
   GET /api/unicode-emojis?format=simple

7. 组合查询:
   GET /api/unicode-emojis?category=People%20%26%20Body&search=hand&limit=5

响应格式:
{
  "metadata": {
    "source": "Unicode.org emoji charts (curated selection)",
    "version": "16.0",
    "total_count": 50,
    "description": "精选的Unicode标准emoji数据，包含最常用的表情符号"
  },
  "categories": {
    "Smileys & Emotion": 30,
    "People & Body": 20
  },
  "emojis": [...],
  "pagination": {
    "total": 50,
    "limit": 50,
    "offset": 0,
    "returned": 50
  }
}
*/
