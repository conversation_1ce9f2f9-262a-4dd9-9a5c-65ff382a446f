"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[775],{2660:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2135:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(9763).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8867:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(9763).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},7168:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(9763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},2023:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(9763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},6394:function(e,t,r){r.d(t,{f:function(){return i}});var n=r(2265),l=r(6840),o=r(7437),a=n.forwardRef((e,t)=>(0,o.jsx)(l.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},7265:function(e,t,r){r.d(t,{VY:function(){return eN},ZA:function(){return e_},JO:function(){return eD},ck:function(){return eH},wU:function(){return eB},eT:function(){return eA},__:function(){return eW},h_:function(){return eL},fC:function(){return eE},$G:function(){return eK},u_:function(){return eZ},Z0:function(){return eO},xz:function(){return eI},B4:function(){return eP},l_:function(){return eV}});var n=r(2265),l=r(4887);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(6741),i=r(8068),u=r(8575),s=r(3966),d=r(9114),c=r(5278),p=r(6097),f=r(9103),v=r(9255),h=r(5345),m=r(3832),w=r(6840),g=r(7495),x=r(6606),y=r(886),b=r(1188),S=r(5098),C=r(5478),M=r(9157),k=r(7437),j=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],R="Select",[E,I,P]=(0,i.B)(R),[D,L]=(0,s.b)(R,[P,h.D7]),N=(0,h.D7)(),[V,_]=D(R),[W,H]=D(R),A=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:u,onValueChange:s,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=N(t),[b,S]=n.useState(null),[C,M]=n.useState(null),[j,T]=n.useState(!1),I=(0,d.gm)(c),[P,D]=(0,y.T)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:R}),[L,_]=(0,y.T)({prop:i,defaultProp:u,onChange:s,caller:R}),H=n.useRef(null),A=!b||g||!!b.closest("form"),[B,Z]=n.useState(new Set),K=Array.from(B).map(e=>e.props.value).join(";");return(0,k.jsx)(h.fC,{...x,children:(0,k.jsxs)(V,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:C,onValueNodeChange:M,valueNodeHasChildren:j,onValueNodeHasChildrenChange:T,contentId:(0,v.M)(),value:L,onValueChange:_,open:P,onOpenChange:D,dir:I,triggerPointerDownPosRef:H,disabled:m,children:[(0,k.jsx)(E.Provider,{scope:t,children:(0,k.jsx)(W,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{Z(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{Z(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),A?(0,k.jsxs)(ek,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:L,onChange:e=>_(e.target.value),disabled:m,form:g,children:[void 0===L?(0,k.jsx)("option",{value:""}):null,Array.from(B)]},K):null]})})};A.displayName=R;var B="SelectTrigger",Z=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=N(r),s=_(B,r),d=s.disabled||l,c=(0,u.e)(t,s.onTriggerChange),p=I(r),f=n.useRef("touch"),[v,m,g]=eT(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=eR(t,e,r);void 0!==n&&s.onValueChange(n.value)}),x=e=>{d||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,k.jsx)(h.ee,{asChild:!0,...i,children:(0,k.jsx)(w.WV.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ej(s.value)?"":void 0,...o,ref:c,onClick:(0,a.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.M)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.M)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&j.includes(e.key)&&(x(),e.preventDefault())})})})});Z.displayName=B;var K="SelectValue",O=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,s=_(K,r),{onValueNodeHasChildrenChange:d}=s,c=void 0!==o,p=(0,u.e)(t,s.onValueNodeChange);return(0,b.b)(()=>{d(c)},[d,c]),(0,k.jsx)(w.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:ej(s.value)?(0,k.jsx)(k.Fragment,{children:a}):o})});O.displayName=K;var F=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,k.jsx)(w.WV.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});F.displayName="SelectIcon";var z=e=>(0,k.jsx)(m.h,{asChild:!0,...e});z.displayName="SelectPortal";var U="SelectContent",q=n.forwardRef((e,t)=>{let r=_(U,e.__scopeSelect),[o,a]=n.useState();return((0,b.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,k.jsx)(J,{...e,ref:t}):o?l.createPortal((0,k.jsx)(Y,{scope:e.__scopeSelect,children:(0,k.jsx)(E.Slot,{scope:e.__scopeSelect,children:(0,k.jsx)("div",{children:e.children})})}),o):null});q.displayName=U;var[Y,X]=D(U),G=(0,g.Z8)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S,...j}=e,T=_(U,r),[R,E]=n.useState(null),[P,D]=n.useState(null),L=(0,u.e)(t,e=>E(e)),[N,V]=n.useState(null),[W,H]=n.useState(null),A=I(r),[B,Z]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(R)return(0,C.Ry)(R)},[R]),(0,p.EW)();let O=n.useCallback(e=>{let[t,...r]=A().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[A,P]),F=n.useCallback(()=>O([N,R]),[O,N,R]);n.useEffect(()=>{B&&F()},[B,F]);let{onOpenChange:z,triggerPointerDownPosRef:q}=T;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=q.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=q.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||z(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,z,q]),n.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[X,J]=eT(e=>{let t=A().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eR(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),ee=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==T.value&&T.value===t||n)&&(V(e),n&&(K.current=!0))},[T.value]),et=n.useCallback(()=>null==R?void 0:R.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==T.value&&T.value===t||n)&&H(e)},[T.value]),en="popper"===l?Q:$,el=en===Q?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S}:{};return(0,k.jsx)(Y,{scope:r,content:R,viewport:P,onViewportChange:D,itemRefCallback:ee,selectedItem:N,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:F,selectedItemText:W,position:l,isPositioned:B,searchRef:X,children:(0,k.jsx)(M.Z,{as:G,allowPinchZoom:!0,children:(0,k.jsx)(f.M,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(o,e=>{var t;null===(t=T.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,k.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,k.jsx)(en,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>Z(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,a.M)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>O(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=_(U,r),s=X(U,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,u.e)(t,e=>f(e)),h=I(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:S,focusSelectedItem:C}=s,M=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&x&&y&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,u=e.width+i,s=Math.max(u,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-s)]);d.style.minWidth=u+"px",d.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,u=e.width+i,s=Math.max(u,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-s)]);d.style.minWidth=u+"px",d.style.right=c+"px"}let a=h(),u=window.innerHeight-20,s=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+s+parseInt(c.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),C=window.getComputedStyle(x),M=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),j=e.top+e.height/2-10,T=y.offsetHeight/2,R=f+v+(y.offsetTop+T);if(R<=j){let e=a.length>0&&y===a[a.length-1].ref.current;d.style.bottom="0px";let t=p.clientHeight-x.offsetTop-x.offsetHeight;d.style.height=R+Math.max(u-j,T+(e?k:0)+t+w)+"px"}else{let e=a.length>0&&y===a[0].ref.current;d.style.top="0px";let t=Math.max(j,f+x.offsetTop+(e?M:0)+T);d.style.height=t+(g-R)+"px",x.scrollTop=R-j+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=b+"px",d.style.maxHeight=u+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,p,x,y,S,i.dir,l]);(0,b.b)(()=>M(),[M]);let[j,T]=n.useState();(0,b.b)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let R=n.useCallback(e=>{e&&!0===g.current&&(M(),null==C||C(),g.current=!1)},[M,C]);return(0,k.jsx)(ee,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,k.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,k.jsx)(w.WV.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});$.displayName="SelectItemAlignedPosition";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=N(r);return(0,k.jsx)(h.VY,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Q.displayName="SelectPopperPosition";var[ee,et]=D(U,{}),er="SelectViewport",en=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=X(er,r),s=et(er,r),d=(0,u.e)(t,i.onViewportChange),c=n.useRef(0);return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,k.jsx)(E.Slot,{scope:r,children:(0,k.jsx)(w.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});en.displayName=er;var el="SelectGroup",[eo,ea]=D(el),ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.M)();return(0,k.jsx)(eo,{scope:r,id:l,children:(0,k.jsx)(w.WV.div,{role:"group","aria-labelledby":l,...n,ref:t})})});ei.displayName=el;var eu="SelectLabel",es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ea(eu,r);return(0,k.jsx)(w.WV.div,{id:l.id,...n,ref:t})});es.displayName=eu;var ed="SelectItem",[ec,ep]=D(ed),ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...s}=e,d=_(ed,r),c=X(ed,r),p=d.value===l,[f,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),x=(0,u.e)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,l,o)}),y=(0,v.M)(),b=n.useRef("touch"),S=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,k.jsx)(ec,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,k.jsx)(E.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,k.jsx)(w.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:x,onFocus:(0,a.M)(s.onFocus,()=>g(!0)),onBlur:(0,a.M)(s.onBlur,()=>g(!1)),onClick:(0,a.M)(s.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.M)(s.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.M)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.M)(s.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,a.M)(s.onKeyDown,e=>{var t;(null===(t=c.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(T.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ef.displayName=ed;var ev="SelectItemText",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,s=_(ev,r),d=X(ev,r),c=ep(ev,r),p=H(ev,r),[f,v]=n.useState(null),h=(0,u.e)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null===(t=d.itemTextRefCallback)||void 0===t?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,k.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.b)(()=>(x(g),()=>y(g)),[x,y,g]),(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(w.WV.span,{id:c.textId,...i,ref:h}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(i.children,s.valueNode):null]})});eh.displayName=ev;var em="SelectItemIndicator",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(em,r).isSelected?(0,k.jsx)(w.WV.span,{"aria-hidden":!0,...n,ref:t}):null});ew.displayName=em;var eg="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=X(eg,e.__scopeSelect),l=et(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,u.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,k.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=eg;var ey="SelectScrollDownButton",eb=n.forwardRef((e,t)=>{let r=X(ey,e.__scopeSelect),l=et(ey,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,u.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,k.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ey;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=X("SelectScrollButton",r),u=n.useRef(null),s=I(r),d=n.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,b.b)(()=>{var e;let t=s().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[s]),(0,k.jsx)(w.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.M)(o.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(l,50))}),onPointerMove:(0,a.M)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===u.current&&(u.current=window.setInterval(l,50))}),onPointerLeave:(0,a.M)(o.onPointerLeave,()=>{d()})})}),eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,k.jsx)(w.WV.div,{"aria-hidden":!0,...n,ref:t})});eC.displayName="SelectSeparator";var eM="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=N(r),o=_(eM,r),a=X(eM,r);return o.open&&"popper"===a.position?(0,k.jsx)(h.Eh,{...l,...n,ref:t}):null}).displayName=eM;var ek=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,u.e)(t,a),s=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[s,l]),(0,k.jsx)(w.WV.select,{...o,style:{...S.C2,...o.style},ref:i,defaultValue:l})});function ej(e){return""===e||void 0===e}function eT(e){let t=(0,x.W)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eR(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(o=o.filter(e=>e!==r));let a=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return a!==r?a:void 0}ek.displayName="SelectBubbleInput";var eE=A,eI=Z,eP=O,eD=F,eL=z,eN=q,eV=en,e_=ei,eW=es,eH=ef,eA=eh,eB=ew,eZ=ex,eK=eb,eO=eC}}]);