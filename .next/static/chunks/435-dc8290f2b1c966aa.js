(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[435],{407:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},519:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},1473:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},3247:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},2489:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9376:function(e,t,n){"use strict";var r=n(5475);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},4742:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},85:function(e,t,n){"use strict";n.d(t,{oC:function(){return e9},VY:function(){return e6},ZA:function(){return e5},ck:function(){return e7},wU:function(){return te},__:function(){return e3},Uv:function(){return e8},Ee:function(){return e2},Rk:function(){return e4},fC:function(){return e0},Z0:function(){return tt},Tr:function(){return tn},tu:function(){return to},fF:function(){return tr},xz:function(){return e1}});var r=n(2265),o=n(6741),a=n(8575),i=n(3966),u=n(886),l=n(6840),s=n(8068),c=n(9114),d=n(5278),f=n(6097),p=n(9103),v=n(9255),m=n(5345),w=n(3832),h=n(1599),g=n(1353),y=n(7495),x=n(6606),M=n(5478),b=n(9157),C=n(7437),E=["Enter"," "],R=["ArrowUp","PageDown","End"],T=["ArrowDown","PageUp","Home",...R],P={ltr:[...E,"ArrowRight"],rtl:[...E,"ArrowLeft"]},j={ltr:["ArrowLeft"],rtl:["ArrowRight"]},D="Menu",[k,N,I]=(0,s.B)(D),[_,S]=(0,i.b)(D,[I,m.D7,g.Pc]),F=(0,m.D7)(),L=(0,g.Pc)(),[A,O]=_(D),[K,W]=_(D),V=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:a,onOpenChange:i,modal:u=!0}=e,l=F(t),[s,d]=r.useState(null),f=r.useRef(!1),p=(0,x.W)(i),v=(0,c.gm)(a);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,C.jsx)(m.fC,{...l,children:(0,C.jsx)(A,{scope:t,open:n,onOpenChange:p,content:s,onContentChange:d,children:(0,C.jsx)(K,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:u,children:o})})})};V.displayName=D;var U=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=F(n);return(0,C.jsx)(m.ee,{...o,...r,ref:t})});U.displayName="MenuAnchor";var G="MenuPortal",[Z,B]=_(G,{forceMount:void 0}),X=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=O(G,t);return(0,C.jsx)(Z,{scope:t,forceMount:n,children:(0,C.jsx)(h.z,{present:n||a.open,children:(0,C.jsx)(w.h,{asChild:!0,container:o,children:r})})})};X.displayName=G;var z="MenuContent",[q,H]=_(z),Y=r.forwardRef((e,t)=>{let n=B(z,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=O(z,e.__scopeMenu),i=W(z,e.__scopeMenu);return(0,C.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(h.z,{present:r||a.open,children:(0,C.jsx)(k.Slot,{scope:e.__scopeMenu,children:i.modal?(0,C.jsx)(J,{...o,ref:t}):(0,C.jsx)(Q,{...o,ref:t})})})})}),J=r.forwardRef((e,t)=>{let n=O(z,e.__scopeMenu),i=r.useRef(null),u=(0,a.e)(t,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,M.Ry)(e)},[]),(0,C.jsx)(ee,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let n=O(z,e.__scopeMenu);return(0,C.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=(0,y.Z8)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:i=!1,trapFocus:u,onOpenAutoFocus:l,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:v,onEscapeKeyDown:w,onPointerDownOutside:h,onFocusOutside:y,onInteractOutside:x,onDismiss:M,disableOutsideScroll:E,...P}=e,j=O(z,n),D=W(z,n),k=F(n),I=L(n),_=N(n),[S,A]=r.useState(null),K=r.useRef(null),V=(0,a.e)(t,K,j.onContentChange),U=r.useRef(0),G=r.useRef(""),Z=r.useRef(0),B=r.useRef(null),X=r.useRef("right"),H=r.useRef(0),Y=E?b.Z:r.Fragment,J=e=>{var t,n;let r=G.current+e,o=_().filter(e=>!e.disabled),a=document.activeElement,i=null===(t=o.find(e=>e.ref.current===a))||void 0===t?void 0:t.textValue,u=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let i=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}(o.map(e=>e.textValue),r,i),l=null===(n=o.find(e=>e.textValue===u))||void 0===n?void 0:n.ref.current;!function e(t){G.current=t,window.clearTimeout(U.current),""!==t&&(U.current=window.setTimeout(()=>e(""),1e3))}(r),l&&setTimeout(()=>l.focus())};r.useEffect(()=>()=>window.clearTimeout(U.current),[]),(0,f.EW)();let Q=r.useCallback(e=>{var t,n,r;return X.current===(null===(t=B.current)||void 0===t?void 0:t.side)&&!!(r=null===(n=B.current)||void 0===n?void 0:n.area)&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],u=t[a],l=i.x,s=i.y,c=u.x,d=u.y;s>r!=d>r&&n<(c-l)*(r-s)/(d-s)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)},[]);return(0,C.jsx)(q,{scope:n,searchRef:G,onItemEnter:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:r.useCallback(e=>{var t;Q(e)||(null===(t=K.current)||void 0===t||t.focus(),A(null))},[Q]),onTriggerLeave:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:Z,onPointerGraceIntentChange:r.useCallback(e=>{B.current=e},[]),children:(0,C.jsx)(Y,{...E?{as:$,allowPinchZoom:!0}:void 0,children:(0,C.jsx)(p.M,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.M)(l,e=>{var t;e.preventDefault(),null===(t=K.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,C.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:w,onPointerDownOutside:h,onFocusOutside:y,onInteractOutside:x,onDismiss:M,children:(0,C.jsx)(g.fC,{asChild:!0,...I,dir:D.dir,orientation:"vertical",loop:i,currentTabStopId:S,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.M)(v,e=>{D.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":ej(j.open),"data-radix-menu-content":"",dir:D.dir,...k,...P,ref:V,style:{outline:"none",...P.style},onKeyDown:(0,o.M)(P.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&J(e.key));let o=K.current;if(e.target!==o||!T.includes(e.key))return;e.preventDefault();let a=_().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(U.current),G.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eN(e=>{let t=e.target,n=H.current!==e.clientX;if(e.currentTarget.contains(t)&&n){let t=e.clientX>H.current?"right":"left";X.current=t,H.current=e.clientX}}))})})})})})})});Y.displayName=z;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(l.WV.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(l.WV.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ea=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:i,...u}=e,s=r.useRef(null),c=W(er,e.__scopeMenu),d=H(er,e.__scopeMenu),f=(0,a.e)(t,s),p=r.useRef(!1);return(0,C.jsx)(ei,{...u,ref:f,disabled:n,onClick:(0,o.M)(e.onClick,()=>{let e=s.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==i?void 0:i(e),{once:!0}),(0,l.jH)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{var t;p.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&E.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=er;var ei=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:i=!1,textValue:u,...s}=e,c=H(er,n),d=L(n),f=r.useRef(null),p=(0,a.e)(t,f),[v,m]=r.useState(!1),[w,h]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var t;h((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[s.children]),(0,C.jsx)(k.ItemSlot,{scope:n,disabled:i,textValue:null!=u?u:w,children:(0,C.jsx)(g.ck,{asChild:!0,...d,focusable:!i,children:(0,C.jsx)(l.WV.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...s,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eN(e=>{i?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eN(e=>c.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),eu=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...a}=e;return(0,C.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,C.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eD(n)?"mixed":n,...a,ref:t,"data-state":ek(n),onSelect:(0,o.M)(a.onSelect,()=>null==r?void 0:r(!!eD(n)||!n),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[es,ec]=_(el,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,a=(0,x.W)(r);return(0,C.jsx)(es,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,C.jsx)(et,{...o,ref:t})})});ed.displayName=el;var ef="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,a=ec(ef,e.__scopeMenu),i=n===a.value;return(0,C.jsx)(em,{scope:e.__scopeMenu,checked:i,children:(0,C.jsx)(ea,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":ek(i),onSelect:(0,o.M)(r.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var ev="MenuItemIndicator",[em,ew]=_(ev,{checked:!1}),eh=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,a=ew(ev,n);return(0,C.jsx)(h.z,{present:r||eD(a.checked)||!0===a.checked,children:(0,C.jsx)(l.WV.span,{...o,ref:t,"data-state":ek(a.checked)})})});eh.displayName=ev;var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(l.WV.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eg.displayName="MenuSeparator";var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=F(n);return(0,C.jsx)(m.Eh,{...o,...r,ref:t})});ey.displayName="MenuArrow";var ex="MenuSub",[eM,eb]=_(ex),eC=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:a}=e,i=O(ex,t),u=F(t),[l,s]=r.useState(null),[c,d]=r.useState(null),f=(0,x.W)(a);return r.useEffect(()=>(!1===i.open&&f(!1),()=>f(!1)),[i.open,f]),(0,C.jsx)(m.fC,{...u,children:(0,C.jsx)(A,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,C.jsx)(eM,{scope:t,contentId:(0,v.M)(),triggerId:(0,v.M)(),trigger:l,onTriggerChange:s,children:n})})})};eC.displayName=ex;var eE="MenuSubTrigger",eR=r.forwardRef((e,t)=>{let n=O(eE,e.__scopeMenu),i=W(eE,e.__scopeMenu),u=eb(eE,e.__scopeMenu),l=H(eE,e.__scopeMenu),s=r.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=l,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,C.jsx)(U,{asChild:!0,...f,children:(0,C.jsx)(ei,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":ej(n.open),...e,ref:(0,a.F)(t,u.onTriggerChange),onClick:t=>{var r;null===(r=e.onClick)||void 0===r||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eN(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||s.current||(l.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eN(e=>{var t,r;p();let o=null===(t=n.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(r=n.content)||void 0===r?void 0:r.dataset.side,a="right"===t,i=o[a?"left":"right"],u=o[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:i,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:i,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let r=""!==l.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&P[i.dir].includes(t.key)){var o;n.onOpenChange(!0),null===(o=n.content)||void 0===o||o.focus(),t.preventDefault()}})})})});eR.displayName=eE;var eT="MenuSubContent",eP=r.forwardRef((e,t)=>{let n=B(z,e.__scopeMenu),{forceMount:i=n.forceMount,...u}=e,l=O(z,e.__scopeMenu),s=W(z,e.__scopeMenu),c=eb(eT,e.__scopeMenu),d=r.useRef(null),f=(0,a.e)(t,d);return(0,C.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(h.z,{present:i||l.open,children:(0,C.jsx)(k.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...u,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null===(t=d.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=j[s.dir].includes(e.key);if(t&&n){var r;l.onOpenChange(!1),null===(r=c.trigger)||void 0===r||r.focus(),e.preventDefault()}})})})})})});function ej(e){return e?"open":"closed"}function eD(e){return"indeterminate"===e}function ek(e){return eD(e)?"indeterminate":e?"checked":"unchecked"}function eN(e){return t=>"mouse"===t.pointerType?e(t):void 0}eP.displayName=eT;var eI="DropdownMenu",[e_,eS]=(0,i.b)(eI,[S]),eF=S(),[eL,eA]=e_(eI),eO=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:a,defaultOpen:i,onOpenChange:l,modal:s=!0}=e,c=eF(t),d=r.useRef(null),[f,p]=(0,u.T)({prop:a,defaultProp:null!=i&&i,onChange:l,caller:eI});return(0,C.jsx)(eL,{scope:t,triggerId:(0,v.M)(),triggerRef:d,contentId:(0,v.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,C.jsx)(V,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:n})})};eO.displayName=eI;var eK="DropdownMenuTrigger",eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...i}=e,u=eA(eK,n),s=eF(n);return(0,C.jsx)(U,{asChild:!0,...s,children:(0,C.jsx)(l.WV.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...i,ref:(0,a.F)(t,u.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eW.displayName=eK;var eV=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eF(t);return(0,C.jsx)(X,{...r,...n})};eV.displayName="DropdownMenuPortal";var eU="DropdownMenuContent",eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eA(eU,n),u=eF(n),l=r.useRef(!1);return(0,C.jsx)(Y,{id:i.contentId,"aria-labelledby":i.triggerId,...u,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;l.current||null===(t=i.triggerRef.current)||void 0===t||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!i.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=eU;var eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(et,{...o,...r,ref:t})});eZ.displayName="DropdownMenuGroup";var eB=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(en,{...o,...r,ref:t})});eB.displayName="DropdownMenuLabel";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(ea,{...o,...r,ref:t})});eX.displayName="DropdownMenuItem";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(eu,{...o,...r,ref:t})});ez.displayName="DropdownMenuCheckboxItem";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(ed,{...o,...r,ref:t})});eq.displayName="DropdownMenuRadioGroup";var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(ep,{...o,...r,ref:t})});eH.displayName="DropdownMenuRadioItem";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(eh,{...o,...r,ref:t})});eY.displayName="DropdownMenuItemIndicator";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(eg,{...o,...r,ref:t})});eJ.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(ey,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(eR,{...o,...r,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eF(n);return(0,C.jsx)(eP,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eO,e1=eW,e8=eV,e6=eG,e5=eZ,e3=eB,e7=eX,e9=ez,e2=eq,e4=eH,te=eY,tt=eJ,tn=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:a}=e,i=eF(t),[l,s]=(0,u.T)({prop:r,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,C.jsx)(eC,{...i,open:l,onOpenChange:s,children:n})},tr=eQ,to=e$},1599:function(e,t,n){"use strict";n.d(t,{z:function(){return i}});var r=n(2265),o=n(8575),a=n(1188),i=e=>{var t,n;let i,l;let{present:s,children:c}=e,d=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(l.current);c.current="mounted"===d?e:"none"},[d]),(0,a.b)(()=>{let t=l.current,n=s.current;if(n!==e){let r=c.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=u(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=u(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(s),f="function"==typeof c?c({present:d.isPresent}):r.Children.only(c),p=(0,o.e)(d.ref,(i=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in i&&i.isReactWarning?f.ref:(i=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in i&&i.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof c||d.isPresent?r.cloneElement(f,{ref:p}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},1353:function(e,t,n){"use strict";n.d(t,{Pc:function(){return M},ck:function(){return N},fC:function(){return k}});var r=n(2265),o=n(6741),a=n(8068),i=n(8575),u=n(3966),l=n(9255),s=n(6840),c=n(6606),d=n(886),f=n(9114),p=n(7437),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[h,g,y]=(0,a.B)(w),[x,M]=(0,u.b)(w,[y]),[b,C]=x(w),E=r.forwardRef((e,t)=>(0,p.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));E.displayName=w;var R=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:l,currentTabStopId:h,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:M,preventScrollOnEntryFocus:C=!1,...E}=e,R=r.useRef(null),T=(0,i.e)(t,R),P=(0,f.gm)(l),[j,k]=(0,d.T)({prop:h,defaultProp:null!=y?y:null,onChange:x,caller:w}),[N,I]=r.useState(!1),_=(0,c.W)(M),S=g(n),F=r.useRef(!1),[L,A]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,_),()=>e.removeEventListener(v,_)},[_]),(0,p.jsx)(b,{scope:n,orientation:a,dir:P,loop:u,currentTabStopId:j,onItemFocus:r.useCallback(e=>k(e),[k]),onItemShiftTab:r.useCallback(()=>I(!0),[]),onFocusableItemAdd:r.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>A(e=>e-1),[]),children:(0,p.jsx)(s.WV.div,{tabIndex:N||0===L?-1:0,"data-orientation":a,...E,ref:T,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!N){let t=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),C)}}F.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>I(!1))})})}),T="RovingFocusGroupItem",P=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:u,children:c,...d}=e,f=(0,l.M)(),v=u||f,m=C(T,n),w=m.currentTabStopId===v,y=g(n),{onFocusableItemAdd:x,onFocusableItemRemove:M,currentTabStopId:b}=m;return r.useEffect(()=>{if(a)return x(),()=>M()},[a,x,M]),(0,p.jsx)(h.ItemSlot,{scope:n,id:v,focusable:a,active:i,children:(0,p.jsx)(s.WV.span,{tabIndex:w?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return j[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=m.loop?(n=o,r=a+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(a+1)}setTimeout(()=>D(o))}}),children:"function"==typeof c?c({isCurrentTabStop:w,hasTabStop:null!=b}):c})})});P.displayName=T;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var k=E,N=P},1915:function(e,t,n){"use strict";n.d(t,{Dx:function(){return $},aU:function(){return et},dk:function(){return ee},fC:function(){return Q},l_:function(){return J},x8:function(){return en},zt:function(){return Y}});var r=n(2265),o=n(4887),a=n(6741),i=n(8575),u=n(8068),l=n(3966),s=n(5278),c=n(3832),d=n(1599),f=n(6840),p=n(6606),v=n(886),m=n(1188),w=n(5098),h=n(7437),g="ToastProvider",[y,x,M]=(0,u.B)("Toast"),[b,C]=(0,l.b)("Toast",[M]),[E,R]=b(g),T=e=>{let{__scopeToast:t,label:n="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:u}=e,[l,s]=r.useState(null),[c,d]=r.useState(0),f=r.useRef(!1),p=r.useRef(!1);return n.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,h.jsx)(y.Provider,{scope:t,children:(0,h.jsx)(E,{scope:t,label:n,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:c,viewport:l,onViewportChange:s,onToastAdd:r.useCallback(()=>d(e=>e+1),[]),onToastRemove:r.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:u})})};T.displayName=g;var P="ToastViewport",j=["F8"],D="toast.viewportPause",k="toast.viewportResume",N=r.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:o=j,label:a="Notifications ({hotkey})",...u}=e,l=R(P,n),c=x(n),d=r.useRef(null),p=r.useRef(null),v=r.useRef(null),m=r.useRef(null),w=(0,i.e)(t,m,l.onViewportChange),g=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),M=l.toastCount>0;r.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=m.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),r.useEffect(()=>{let e=d.current,t=m.current;if(M&&e&&t){let n=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},r=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(k);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||r()},a=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",a),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",a),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[M,l.isClosePausedRef]);let b=r.useCallback(e=>{let{tabbingDirection:t}=e,n=c().map(e=>{let n=e.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===t?r:r.reverse()});return("forwards"===t?n.reverse():n).flat()},[c]);return r.useEffect(()=>{let e=m.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){var r,o,a;let n=document.activeElement,i=t.shiftKey;if(t.target===e&&i){null===(r=p.current)||void 0===r||r.focus();return}let u=b({tabbingDirection:i?"backwards":"forwards"}),l=u.findIndex(e=>e===n);H(u.slice(l+1))?t.preventDefault():i?null===(o=p.current)||void 0===o||o.focus():null===(a=v.current)||void 0===a||a.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,b]),(0,h.jsxs)(s.I0,{ref:d,role:"region","aria-label":a.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:M?void 0:"none"},children:[M&&(0,h.jsx)(_,{ref:p,onFocusFromOutsideViewport:()=>{H(b({tabbingDirection:"forwards"}))}}),(0,h.jsx)(y.Slot,{scope:n,children:(0,h.jsx)(f.WV.ol,{tabIndex:-1,...u,ref:w})}),M&&(0,h.jsx)(_,{ref:v,onFocusFromOutsideViewport:()=>{H(b({tabbingDirection:"backwards"}))}})]})});N.displayName=P;var I="ToastFocusProxy",_=r.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,a=R(I,n);return(0,h.jsx)(w.TX,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let n=e.relatedTarget;(null===(t=a.viewport)||void 0===t?void 0:t.contains(n))||r()}})});_.displayName=I;var S="Toast",F=r.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...u}=e,[l,s]=(0,v.T)({prop:r,defaultProp:null==o||o,onChange:i,caller:S});return(0,h.jsx)(d.z,{present:n||l,children:(0,h.jsx)(O,{open:l,...u,ref:t,onClose:()=>s(!1),onPause:(0,p.W)(e.onPause),onResume:(0,p.W)(e.onResume),onSwipeStart:(0,a.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.M)(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(n,"px"))}),onSwipeCancel:(0,a.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.M)(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(n,"px")),s(!1)})})})});F.displayName=S;var[L,A]=b(S,{onClose(){}}),O=r.forwardRef((e,t)=>{let{__scopeToast:n,type:u="foreground",duration:l,open:c,onClose:d,onEscapeKeyDown:v,onPause:m,onResume:w,onSwipeStart:g,onSwipeMove:x,onSwipeCancel:M,onSwipeEnd:b,...C}=e,E=R(S,n),[T,P]=r.useState(null),j=(0,i.e)(t,e=>P(e)),N=r.useRef(null),I=r.useRef(null),_=l||E.duration,F=r.useRef(0),A=r.useRef(_),O=r.useRef(0),{onToastAdd:W,onToastRemove:V}=E,U=(0,p.W)(()=>{var e;(null==T?void 0:T.contains(document.activeElement))&&(null===(e=E.viewport)||void 0===e||e.focus()),d()}),G=r.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(O.current),F.current=new Date().getTime(),O.current=window.setTimeout(U,e))},[U]);r.useEffect(()=>{let e=E.viewport;if(e){let t=()=>{G(A.current),null==w||w()},n=()=>{let e=new Date().getTime()-F.current;A.current=A.current-e,window.clearTimeout(O.current),null==m||m()};return e.addEventListener(D,n),e.addEventListener(k,t),()=>{e.removeEventListener(D,n),e.removeEventListener(k,t)}}},[E.viewport,_,m,w,G]),r.useEffect(()=>{c&&!E.isClosePausedRef.current&&G(_)},[c,_,E.isClosePausedRef,G]),r.useEffect(()=>(W(),()=>V()),[W,V]);let Z=r.useMemo(()=>T?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!r){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}}),n}(T):null,[T]);return E.viewport?(0,h.jsxs)(h.Fragment,{children:[Z&&(0,h.jsx)(K,{__scopeToast:n,role:"status","aria-live":"foreground"===u?"assertive":"polite","aria-atomic":!0,children:Z}),(0,h.jsx)(L,{scope:n,onClose:U,children:o.createPortal((0,h.jsx)(y.ItemSlot,{scope:n,children:(0,h.jsx)(s.fC,{asChild:!0,onEscapeKeyDown:(0,a.M)(v,()=>{E.isFocusedToastEscapeKeyDownRef.current||U(),E.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,h.jsx)(f.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":E.swipeDirection,...C,ref:j,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.M)(e.onKeyDown,e=>{"Escape"!==e.key||(null==v||v(e.nativeEvent),e.nativeEvent.defaultPrevented||(E.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:(0,a.M)(e.onPointerDown,e=>{0===e.button&&(N.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.M)(e.onPointerMove,e=>{if(!N.current)return;let t=e.clientX-N.current.x,n=e.clientY-N.current.y,r=!!I.current,o=["left","right"].includes(E.swipeDirection),a=["left","up"].includes(E.swipeDirection)?Math.min:Math.max,i=o?a(0,t):0,u=o?0:a(0,n),l="touch"===e.pointerType?10:2,s={x:i,y:u},c={originalEvent:e,delta:s};r?(I.current=s,z("toast.swipeMove",x,c,{discrete:!1})):q(s,E.swipeDirection,l)?(I.current=s,z("toast.swipeStart",g,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(n)>l)&&(N.current=null)}),onPointerUp:(0,a.M)(e.onPointerUp,e=>{let t=I.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),I.current=null,N.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};q(t,E.swipeDirection,E.swipeThreshold)?z("toast.swipeEnd",b,r,{discrete:!0}):z("toast.swipeCancel",M,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),E.viewport)})]}):null}),K=e=>{let{__scopeToast:t,children:n,...o}=e,a=R(S,t),[i,u]=r.useState(!1),[l,s]=r.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,p.W)(e);(0,m.b)(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>u(!0)),r.useEffect(()=>{let e=window.setTimeout(()=>s(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,h.jsx)(c.h,{asChild:!0,children:(0,h.jsx)(w.TX,{...o,children:i&&(0,h.jsxs)(h.Fragment,{children:[a.label," ",n]})})})},W=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,h.jsx)(f.WV.div,{...r,ref:t})});W.displayName="ToastTitle";var V=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,h.jsx)(f.WV.div,{...r,ref:t})});V.displayName="ToastDescription";var U="ToastAction",G=r.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,h.jsx)(X,{altText:n,asChild:!0,children:(0,h.jsx)(B,{...r,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(U,"`. Expected non-empty `string`.")),null)});G.displayName=U;var Z="ToastClose",B=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,o=A(Z,n);return(0,h.jsx)(X,{asChild:!0,children:(0,h.jsx)(f.WV.button,{type:"button",...r,ref:t,onClick:(0,a.M)(e.onClick,o.onClose)})})});B.displayName=Z;var X=r.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...o}=e;return(0,h.jsx)(f.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function z(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,f.jH)(a,i):a.dispatchEvent(i)}var q=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=Math.abs(e.x),o=Math.abs(e.y),a=r>o;return"left"===t||"right"===t?a&&r>n:!a&&o>n};function H(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Y=T,J=N,Q=F,$=W,ee=V,et=G,en=B}}]);