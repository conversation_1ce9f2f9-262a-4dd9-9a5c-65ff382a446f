"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[367],{401:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5278:function(e,t,n){n.d(t,{I0:function(){return g},XB:function(){return d},fC:function(){return v}});var r,i=n(2265),o=n(6741),l=n(6840),a=n(8575),s=n(6606),u=n(7437),f="dismissableLayer.update",c=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,...b}=e,E=i.useContext(c),[R,A]=i.useState(null),C=null!==(d=null==R?void 0:R.ownerDocument)&&void 0!==d?d:null===(n=globalThis)||void 0===n?void 0:n.document,[,L]=i.useState({}),P=(0,a.e)(t,e=>A(e)),S=Array.from(E.layers),[O]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),T=S.indexOf(O),D=R?S.indexOf(R):-1,W=E.layersWithOutsidePointerEventsDisabled.size>0,k=D>=T,H=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,s.W)(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!k||n||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},C),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,s.W)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},C);return!function(e,t=globalThis?.document){let n=(0,s.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D!==E.layers.size-1||(null==v||v(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},C),i.useEffect(()=>{if(R)return p&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),h(),()=>{p&&1===E.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[R,C,p,E]),i.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),h())},[R,E]),i.useEffect(()=>{let e=()=>L({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,u.jsx)(l.WV.div,{...b,ref:P,style:{pointerEvents:W?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,H.onPointerDownCapture)})});d.displayName="DismissableLayer";var p=i.forwardRef((e,t)=>{let n=i.useContext(c),r=i.useRef(null),o=(0,a.e)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(l.WV.div,{...e,ref:o})});function h(){let e=new CustomEvent(f);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,l.jH)(o,a):o.dispatchEvent(a)}p.displayName="DismissableLayerBranch";var v=d,g=p},5345:function(e,t,n){n.d(t,{ee:function(){return e9},Eh:function(){return tt},VY:function(){return te},fC:function(){return e4},D7:function(){return e$}});var r=n(2265);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),f={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>c[e])}let x=["left","right"],b=["right","left"],E=["top","bottom"],R=["bottom","top"];function A(e){return e.replace(/left|right|bottom|top/g,e=>f[e])}function C(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function L(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function P(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),s=v(a),u=p(t),f="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,g=i[s]/2-o[s]/2;switch(u){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=g*(n&&f?-1:1);break;case"end":r[a]+=g*(n&&f?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:c}=P(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:f,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});f=null!=v?v:f,c=null!=g?g:c,p={...p,[o]:{...p[o],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:f,y:c}=P(u,d,s)),n=-1)}return{x:f,y:c,placement:d,strategy:i,middlewareData:p}};async function O(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=C(h),v=a[p?"floating"===c?"reference":"floating":c],g=L(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:s})),y="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),x=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},b=L(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:s}):y);return{top:(g.top-b.top+m.top)/x.y,bottom:(b.bottom-g.bottom+m.bottom)/x.y,left:(g.left-b.left+m.left)/x.x,right:(b.right-g.right+m.right)/x.x}}function T(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function D(e){return i.some(t=>e[t]>=0)}let W=new Set(["left","top"]);async function k(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),s="y"===y(n),u=W.has(l)?-1:1,f=o&&s?-1:1,c=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof g&&(v="end"===a?-1*g:g),s?{x:v*f,y:m*u}:{x:m*u,y:v*f}}function H(){return"undefined"!=typeof window}function j(e){return z(e)?(e.nodeName||"").toLowerCase():"#document"}function F(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function V(e){var t;return null==(t=(z(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function z(e){return!!H()&&(e instanceof Node||e instanceof F(e).Node)}function B(e){return!!H()&&(e instanceof Element||e instanceof F(e).Element)}function M(e){return!!H()&&(e instanceof HTMLElement||e instanceof F(e).HTMLElement)}function N(e){return!!H()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof F(e).ShadowRoot)}let _=new Set(["inline","contents"]);function I(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!_.has(i)}let X=new Set(["table","td","th"]),Y=[":popover-open",":modal"];function $(e){return Y.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let q=["transform","translate","scale","rotate","perspective"],Z=["transform","translate","scale","rotate","perspective","filter"],G=["paint","layout","strict","content"];function J(e){let t=K(),n=B(e)?ee(e):e;return q.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||Z.some(e=>(n.willChange||"").includes(e))||G.some(e=>(n.contain||"").includes(e))}function K(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function U(e){return Q.has(j(e))}function ee(e){return F(e).getComputedStyle(e)}function et(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===j(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||V(e);return N(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=en(t);return U(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&I(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=F(i);if(o){let e=ei(l);return t.concat(l,l.visualViewport||[],I(i)?i:[],e&&n?er(e):[])}return t.concat(i,er(i,[],n))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eo(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=M(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,s=a(n)!==o||a(r)!==l;return s&&(n=o,r=l),{width:n,height:r,$:s}}function el(e){return B(e)?e:e.contextElement}function ea(e){let t=el(e);if(!M(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=eo(t),l=(o?a(n.width):n.width)/r,s=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}let es=u(0);function eu(e){let t=F(e);return K()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:es}function ef(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=el(e),a=u(1);t&&(r?B(r)&&(a=ea(r)):a=ea(e));let s=(void 0===(i=n)&&(i=!1),r&&(!i||r===F(l))&&i)?eu(l):u(0),f=(o.left+s.x)/a.x,c=(o.top+s.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=F(l),t=r&&B(r)?F(r):r,n=e,i=ei(n);for(;i&&r&&t!==n;){let e=ea(i),t=i.getBoundingClientRect(),r=ee(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;f*=e.x,c*=e.y,d*=e.x,p*=e.y,f+=o,c+=l,i=ei(n=F(i))}}return L({width:d,height:p,x:f,y:c})}function ec(e,t){let n=et(e).scrollLeft;return t?t.left+n:ef(V(e)).left+n}function ed(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ec(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=F(e),r=V(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,s=0;if(i){o=i.width,l=i.height;let e=K();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:o,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=V(e),n=et(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ec(e),s=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:s}}(V(e));else if(B(t))r=function(e,t){let n=ef(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=M(e)?ea(e):u(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=eu(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return L(r)}function em(e){return"static"===ee(e).position}function ev(e,t){if(!M(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return V(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=F(e);if($(e))return r;if(!M(e)){let t=en(e);for(;t&&!U(t);){if(B(t)&&!em(t))return t;t=en(t)}return r}let i=ev(e,t);for(;i&&(n=i,X.has(j(n)))&&em(i);)i=ev(i,t);return i&&U(i)&&em(i)&&!J(i)?r:i||function(e){let t=en(e);for(;M(t)&&!U(t);){if(J(t))return t;if($(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=M(t),i=V(t),o="fixed"===n,l=ef(e,!0,o,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!o){if(("body"!==j(t)||I(i))&&(a=et(t)),r){let e=ef(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=ec(i))}o&&!r&&i&&(s.x=ec(i));let f=!i||r||o?u(0):ed(i,a);return{x:l.left+a.scrollLeft-s.x-f.x,y:l.top+a.scrollTop-s.y-f.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=V(r),a=!!t&&$(t.floating);if(r===l||a&&o)return n;let s={scrollLeft:0,scrollTop:0},f=u(1),c=u(0),d=M(r);if((d||!d&&!o)&&(("body"!==j(r)||I(l))&&(s=et(r)),M(r))){let e=ef(r);f=ea(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let p=!l||d||o?u(0):ed(l,s,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-s.scrollLeft*f.x+c.x+p.x,y:n.y*f.y-s.scrollTop*f.y+c.y+p.y}},getDocumentElement:V,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?$(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>B(e)&&"body"!==j(e)),i=null,o="fixed"===ee(e).position,l=o?en(e):e;for(;B(l)&&!U(l);){let t=ee(l),n=J(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&ep.has(i.position)||I(l)&&!n&&function e(t,n){let r=en(t);return!(r===n||!B(r)||U(r))&&("fixed"===ee(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=en(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=eh(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},eh(t,s,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eo(e);return{width:t,height:n}},getScale:ea,isElement:B,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:s,elements:u,middlewareData:f}=t,{element:c,padding:p=0}=d(e,t)||{};if(null==c)return{};let g=C(p),w={x:n,y:r},x=m(y(i)),b=v(x),E=await s.getDimensions(c),R="y"===x,A=R?"clientHeight":"clientWidth",L=a.reference[b]+a.reference[x]-w[x]-a.floating[b],P=w[x]-a.reference[x],S=await (null==s.getOffsetParent?void 0:s.getOffsetParent(c)),O=S?S[A]:0;O&&await (null==s.isElement?void 0:s.isElement(S))||(O=u.floating[A]||a.floating[b]);let T=O/2-E[b]/2-1,D=o(g[R?"top":"left"],T),W=o(g[R?"bottom":"right"],T),k=O-E[b]-W,H=O/2-E[b]/2+(L/2-P/2),j=l(D,o(H,k)),F=!f.arrow&&null!=h(i)&&H!==j&&a.reference[b]/2-(H<D?D:W)-E[b]/2<0,V=F?H<D?H-D:H-k:0;return{[x]:w[x]+V,data:{[x]:j,centerOffset:H-j-V,...F&&{alignmentOffset:V}},reset:F}}}),eE=(e,t,n)=>{let r=new Map,i={platform:ew,...n},o={...i.platform,_c:r};return S(e,t,{...i,platform:o})};var eR=n(4887),eA="undefined"!=typeof document?r.useLayoutEffect:function(){};function eC(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eC(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eC(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eL(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eP(e,t){let n=eL(e);return Math.round(t*n)/n}function eS(e){let t=r.useRef(e);return eA(()=>{t.current=e}),t}let eO=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}),eT=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:i,y:o,placement:l,middlewareData:a}=e,s=await k(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+s.x,y:o+s.y,data:{...s,placement:l}}}}),options:[e,t]}},eD=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:i}=e,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=d(n,e),c={x:t,y:r},h=await O(e,f),v=y(p(i)),g=m(v),w=c[g],x=c[v];if(a){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,o(w,r))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}let b=u.fn({...e,[g]:w,[v]:x});return{...b,data:{x:b.x-t,y:b.y-r,enabled:{[g]:a,[v]:s}}}}}),options:[e,t]}},eW=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=d(n,e),f={x:t,y:r},c=y(i),h=m(c),v=f[h],g=f[c],w=d(a,e),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var b,E;let e="y"===h?"width":"height",t=W.has(p(i)),n=o.reference[c]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[c])||0)+(t?0:x.crossAxis),r=o.reference[c]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[c])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:v,[c]:g}}}),options:[e,t]}},ek=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,i,o,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:f,platform:c,elements:g}=e,{mainAxis:C=!0,crossAxis:L=!0,fallbackPlacements:P,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:D=!0,...W}=d(n,e);if(null!=(t=s.arrow)&&t.alignmentOffset)return{};let k=p(a),H=y(f),j=p(f)===f,F=await (null==c.isRTL?void 0:c.isRTL(g.floating)),V=P||(j||!D?[A(f)]:function(e){let t=A(e);return[w(e),t,w(t)]}(f)),z="none"!==T;!P&&z&&V.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?E:R;default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(f,D,T,F));let B=[f,...V],M=await O(e,W),N=[],_=(null==(r=s.flip)?void 0:r.overflows)||[];if(C&&N.push(M[k]),L){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=v(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=A(l)),[l,A(l)]}(a,u,F);N.push(M[e[0]],M[e[1]])}if(_=[..._,{placement:a,overflows:N}],!N.every(e=>e<=0)){let e=((null==(i=s.flip)?void 0:i.index)||0)+1,t=B[e];if(t&&(!("alignment"===L&&H!==y(t))||_.every(e=>y(e.placement)!==H||e.overflows[0]>0)))return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(o=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let e=null==(l=_.filter(e=>{if(z){let t=y(e.placement);return t===H||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=f}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eH=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let i,a;let{placement:s,rects:u,platform:f,elements:c}=e,{apply:m=()=>{},...v}=d(n,e),g=await O(e,v),w=p(s),x=h(s),b="y"===y(s),{width:E,height:R}=u.floating;"top"===w||"bottom"===w?(i=w,a=x===(await (null==f.isRTL?void 0:f.isRTL(c.floating))?"start":"end")?"left":"right"):(a=w,i="end"===x?"top":"bottom");let A=R-g.top-g.bottom,C=E-g.left-g.right,L=o(R-g[i],A),P=o(E-g[a],C),S=!e.middlewareData.shift,T=L,D=P;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(D=C),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(T=A),S&&!x){let e=l(g.left,0),t=l(g.right,0),n=l(g.top,0),r=l(g.bottom,0);b?D=E-2*(0!==e||0!==t?e+t:l(g.left,g.right)):T=R-2*(0!==n||0!==r?n+r:l(g.top,g.bottom))}await m({...e,availableWidth:D,availableHeight:T});let W=await f.getDimensions(c.floating);return E!==W.width||R!==W.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},ej=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...i}=d(n,e);switch(r){case"referenceHidden":{let n=T(await O(e,{...i,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:D(n)}}}case"escaped":{let n=T(await O(e,{...i,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:D(n)}}}default:return{}}}}),options:[e,t]}},eF=(e,t)=>({...eO(e),options:[e,t]});var eV=n(6840),ez=n(7437),eB=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,ez.jsx)(eV.WV.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,ez.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eB.displayName="Arrow";var eM=n(8575),eN=n(3966),e_=n(6606),eI=n(1188),eX="Popper",[eY,e$]=(0,eN.b)(eX),[eq,eZ]=eY(eX),eG=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,ez.jsx)(eq,{scope:t,anchor:i,onAnchorChange:o,children:n})};eG.displayName=eX;var eJ="PopperAnchor",eK=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eZ(eJ,n),a=r.useRef(null),s=(0,eM.e)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,ez.jsx)(eV.WV.div,{...o,ref:s})});eK.displayName=eJ;var eQ="PopperContent",[eU,e0]=eY(eQ),e1=r.forwardRef((e,t)=>{var n,i,a,u,f,c,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:C="optimized",onPlaced:L,...P}=e,S=eZ(eQ,h),[O,T]=r.useState(null),D=(0,eM.e)(t,e=>T(e)),[W,k]=r.useState(null),H=function(e){let[t,n]=r.useState(void 0);return(0,eI.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(W),j=null!==(d=null==H?void 0:H.width)&&void 0!==d?d:0,F=null!==(p=null==H?void 0:H.height)&&void 0!==p?p:0,z="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},B=Array.isArray(b)?b:[b],M=B.length>0,N={padding:z,boundary:B.filter(e7),altBoundary:M},{refs:_,floatingStyles:I,placement:X,isPositioned:Y,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:f}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);eC(p,i)||h(i);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),x=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),b=l||m,E=a||g,R=r.useRef(null),A=r.useRef(null),C=r.useRef(c),L=null!=u,P=eS(u),S=eS(o),O=eS(f),T=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:p};S.current&&(e.platform=S.current),eE(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};D.current&&!eC(C.current,t)&&(C.current=t,eR.flushSync(()=>{d(t)}))})},[p,t,n,S,O]);eA(()=>{!1===f&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[f]);let D=r.useRef(!1);eA(()=>(D.current=!0,()=>{D.current=!1}),[]),eA(()=>{if(b&&(R.current=b),E&&(A.current=E),b&&E){if(P.current)return P.current(b,E,T);T()}},[b,E,T,P,L]);let W=r.useMemo(()=>({reference:R,floating:A,setReference:w,setFloating:x}),[w,x]),k=r.useMemo(()=>({reference:b,floating:E}),[b,E]),H=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!k.floating)return e;let t=eP(k.floating,c.x),r=eP(k.floating,c.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...eL(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,k.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:T,refs:W,elements:k,floatingStyles:H}),[c,T,W,k,H])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=el(e),h=a||u?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&c?function(e,t){let n,r=null,i=V(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(f,c){void 0===f&&(f=!1),void 0===c&&(c=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=d;if(f||t(),!m||!v)return;let g=s(h),y=s(i.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-s(i.clientHeight-(h+v))+"px "+-s(p)+"px",threshold:l(0,o(1,c))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==c){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ex(d,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;f&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?ef(e):null;return d&&function t(){let r=ef(e);y&&!ex(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===C})},elements:{reference:S.anchor},middleware:[eT({mainAxis:v+F,alignmentAxis:y}),x&&eD({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eW():void 0,...N}),x&&ek({...N}),eH({...N,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),W&&eF({element:W,padding:w}),e8({arrowWidth:j,arrowHeight:F}),A&&ej({strategy:"referenceHidden",...N})]}),[q,Z]=e3(X),G=(0,e_.W)(L);(0,eI.b)(()=>{Y&&(null==G||G())},[Y,G]);let J=null===(n=$.arrow)||void 0===n?void 0:n.x,K=null===(i=$.arrow)||void 0===i?void 0:i.y,Q=(null===(a=$.arrow)||void 0===a?void 0:a.centerOffset)!==0,[U,ee]=r.useState();return(0,eI.b)(()=>{O&&ee(window.getComputedStyle(O).zIndex)},[O]),(0,ez.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:Y?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[null===(u=$.transformOrigin)||void 0===u?void 0:u.x,null===(f=$.transformOrigin)||void 0===f?void 0:f.y].join(" "),...(null===(c=$.hide)||void 0===c?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,ez.jsx)(eU,{scope:h,placedSide:q,onArrowChange:k,arrowX:J,arrowY:K,shouldHideArrow:Q,children:(0,ez.jsx)(eV.WV.div,{"data-side":q,"data-align":Z,...P,ref:D,style:{...P.style,animation:Y?void 0:"none"}})})})});e1.displayName=eQ;var e2="PopperArrow",e5={top:"bottom",right:"left",bottom:"top",left:"right"},e6=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e0(e2,n),o=e5[i.placedSide];return(0,ez.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,ez.jsx)(eB,{...r,ref:t,style:{...r.style,display:"block"}})})});function e7(e){return null!==e}e6.displayName=e2;var e8=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:s,middlewareData:u}=t,f=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,c=f?0:e.arrowWidth,d=f?0:e.arrowHeight,[p,h]=e3(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!==(o=null===(r=u.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+c/2,g=(null!==(l=null===(i=u.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,y="",w="";return"bottom"===p?(y=f?m:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=f?m:"".concat(v,"px"),w="".concat(s.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=f?m:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+d,"px"),w=f?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function e3(e){let[t,n="center"]=e.split("-");return[t,n]}var e4=eG,e9=eK,te=e1,tt=e6},3832:function(e,t,n){n.d(t,{h:function(){return s}});var r=n(2265),i=n(4887),o=n(6840),l=n(1188),a=n(7437),s=r.forwardRef((e,t)=>{var n,s;let{container:u,...f}=e,[c,d]=r.useState(!1);(0,l.b)(()=>d(!0),[]);let p=u||c&&(null===(s=globalThis)||void 0===s?void 0:null===(n=s.document)||void 0===n?void 0:n.body);return p?i.createPortal((0,a.jsx)(o.WV.div,{...f,ref:t}),p):null});s.displayName="Portal"},5098:function(e,t,n){n.d(t,{C2:function(){return l},TX:function(){return a},fC:function(){return s}});var r=n(2265),i=n(6840),o=n(7437),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,o.jsx)(i.WV.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var s=a}}]);