"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[52],{6741:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},8068:function(e,t,n){n.d(t,{B:function(){return i}});var r=n(2265),o=n(3966),u=n(8575),l=n(7495),c=n(7437);function i(e){let t=e+"CollectionProvider",[n,i]=(0,o.b)(t),[f,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,o=r.useRef(null),u=r.useRef(new Map).current;return(0,c.jsx)(f,{scope:t,itemMap:u,collectionRef:o,children:n})};s.displayName=t;let d=e+"CollectionSlot",m=(0,l.Z8)(d),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=a(d,n),l=(0,u.e)(t,o.collectionRef);return(0,c.jsx)(m,{ref:l,children:r})});p.displayName=d;let v=e+"CollectionItemSlot",b="data-radix-collection-item",h=(0,l.Z8)(v),w=r.forwardRef((e,t)=>{let{scope:n,children:o,...l}=e,i=r.useRef(null),f=(0,u.e)(t,i),s=a(v,n);return r.useEffect(()=>(s.itemMap.set(i,{ref:i,...l}),()=>void s.itemMap.delete(i))),(0,c.jsx)(h,{[b]:"",ref:f,children:o})});return w.displayName=v,[{Provider:s,Slot:p,ItemSlot:w},function(t){let n=a(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(b,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},i]}},3966:function(e,t,n){n.d(t,{b:function(){return u}});var r=n(2265),o=n(7437);function u(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let l=r.createContext(u),c=n.length;n=[...n,u];let i=t=>{let{scope:n,children:u,...i}=t,f=n?.[e]?.[c]||l,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(f.Provider,{value:a,children:u})};return i.displayName=t+"Provider",[i,function(n,o){let i=o?.[e]?.[c]||l,f=r.useContext(i);if(f)return f;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},9114:function(e,t,n){n.d(t,{gm:function(){return u}});var r=n(2265);n(7437);var o=r.createContext(void 0);function u(e){let t=r.useContext(o);return e||t||"ltr"}},9255:function(e,t,n){n.d(t,{M:function(){return i}});var r,o=n(2265),u=n(1188),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),c=0;function i(e){let[t,n]=o.useState(l());return(0,u.b)(()=>{e||n(e=>e??String(c++))},[e]),e||(t?`radix-${t}`:"")}},6840:function(e,t,n){n.d(t,{WV:function(){return c},jH:function(){return i}});var r=n(2265),o=n(4887),u=n(7495),l=n(7437),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...u}=e,c=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(c,{...u,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function i(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},6606:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},886:function(e,t,n){n.d(t,{T:function(){return c}});var r,o=n(2265),u=n(1188),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function c({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,c,i]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),c=o.useRef(t);return l(()=>{c.current=t},[t]),o.useEffect(()=>{u.current!==n&&(c.current?.(n),u.current=n)},[n,u]),[n,r,c]}({defaultProp:t,onChange:n}),f=void 0!==e,a=f?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==f){let t=f?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=f},[f,r])}return[a,o.useCallback(t=>{if(f){let n="function"==typeof t?t(e):t;n!==e&&i.current?.(n)}else c(t)},[f,e,c,i])]}Symbol("RADIX:SYNC_STATE")},1188:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);