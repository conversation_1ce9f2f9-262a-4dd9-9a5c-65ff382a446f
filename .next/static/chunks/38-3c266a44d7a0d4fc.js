"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[38],{2660:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8867:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},740:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},3245:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},3247:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},9376:function(e,t,n){var r=n(5475);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},6741:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},3966:function(e,t,n){n.d(t,{b:function(){return u}});var r=n(2265),o=n(7437);function u(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),l=n.length;n=[...n,u];let a=t=>{let{scope:n,children:u,...a}=t,c=n?.[e]?.[l]||i,s=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:u})};return a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e]?.[l]||i,c=r.useContext(a);if(c)return c;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},9255:function(e,t,n){n.d(t,{M:function(){return a}});var r,o=n(2265),u=n(1188),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function a(e){let[t,n]=o.useState(i());return(0,u.b)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},1599:function(e,t,n){n.d(t,{z:function(){return i}});var r=n(2265),o=n(8575),u=n(1188),i=e=>{var t,n;let i,a;let{present:c,children:s}=e,d=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(a.current);s.current="mounted"===d?e:"none"},[d]),(0,u.b)(()=>{let t=a.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,u.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(a.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(s.current=l(a.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(c),f="function"==typeof s?s({present:d.isPresent}):r.Children.only(s),p=(0,o.e)(d.ref,(i=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in i&&i.isReactWarning?f.ref:(i=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in i&&i.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof s||d.isPresent?r.cloneElement(f,{ref:p}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},6840:function(e,t,n){n.d(t,{WV:function(){return l},jH:function(){return a}});var r=n(2265),o=n(4887),u=n(7495),i=n(7437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...u}=e,l=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...u,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},1312:function(e,t,n){n.d(t,{VY:function(){return z},fC:function(){return F},xz:function(){return W},zt:function(){return $}});var r=n(2265),o=n(6741),u=n(8575),i=n(3966),l=n(5278),a=n(9255),c=n(5345),s=(n(3832),n(1599)),d=n(6840),f=n(7495),p=n(886),m=n(5098),v=n(7437),[h,y]=(0,i.b)("Tooltip",[c.D7]),g=(0,c.D7)(),x="TooltipProvider",b="tooltip.open",[w,T]=h(x),C=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:u=!1,children:i}=e,l=r.useRef(!0),a=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(w,{scope:t,isOpenDelayedRef:l,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),l.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:a,onPointerInTransitChange:r.useCallback(e=>{a.current=e},[]),disableHoverableContent:u,children:i})};C.displayName=x;var E="Tooltip",[M,N]=h(E),R=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:u,onOpenChange:i,disableHoverableContent:l,delayDuration:s}=e,d=T(E,e.__scopeTooltip),f=g(t),[m,h]=r.useState(null),y=(0,a.M)(),x=r.useRef(0),w=null!=l?l:d.disableHoverableContent,C=null!=s?s:d.delayDuration,N=r.useRef(!1),[R,k]=(0,p.T)({prop:o,defaultProp:null!=u&&u,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),null==i||i(e)},caller:E}),P=r.useMemo(()=>R?N.current?"delayed-open":"instant-open":"closed",[R]),O=r.useCallback(()=>{window.clearTimeout(x.current),x.current=0,N.current=!1,k(!0)},[k]),_=r.useCallback(()=>{window.clearTimeout(x.current),x.current=0,k(!1)},[k]),L=r.useCallback(()=>{window.clearTimeout(x.current),x.current=window.setTimeout(()=>{N.current=!0,k(!0),x.current=0},C)},[C,k]);return r.useEffect(()=>()=>{x.current&&(window.clearTimeout(x.current),x.current=0)},[]),(0,v.jsx)(c.fC,{...f,children:(0,v.jsx)(M,{scope:t,contentId:y,open:R,stateAttribute:P,trigger:m,onTriggerChange:h,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?L():O()},[d.isOpenDelayedRef,L,O]),onTriggerLeave:r.useCallback(()=>{w?_():(window.clearTimeout(x.current),x.current=0)},[_,w]),onOpen:O,onClose:_,disableHoverableContent:w,children:n})})};R.displayName=E;var k="TooltipTrigger",P=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...i}=e,l=N(k,n),a=T(k,n),s=g(n),f=r.useRef(null),p=(0,u.e)(t,f,l.onTriggerChange),m=r.useRef(!1),h=r.useRef(!1),y=r.useCallback(()=>m.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,v.jsx)(c.ee,{asChild:!0,...s,children:(0,v.jsx)(d.WV.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...i,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||a.isPointerInTransitRef.current||(l.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,o.M)(e.onPointerLeave,()=>{l.onTriggerLeave(),h.current=!1}),onPointerDown:(0,o.M)(e.onPointerDown,()=>{l.open&&l.onClose(),m.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.M)(e.onFocus,()=>{m.current||l.onOpen()}),onBlur:(0,o.M)(e.onBlur,l.onClose),onClick:(0,o.M)(e.onClick,l.onClose)})})});P.displayName=k;var[O,_]=h("TooltipPortal",{forceMount:void 0}),L="TooltipContent",j=r.forwardRef((e,t)=>{let n=_(L,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...u}=e,i=N(L,e.__scopeTooltip);return(0,v.jsx)(s.z,{present:r||i.open,children:i.disableHoverableContent?(0,v.jsx)(U,{side:o,...u,ref:t}):(0,v.jsx)(S,{side:o,...u,ref:t})})}),S=r.forwardRef((e,t)=>{let n=N(L,e.__scopeTooltip),o=T(L,e.__scopeTooltip),i=r.useRef(null),l=(0,u.e)(t,i),[a,c]=r.useState(null),{trigger:s,onClose:d}=n,f=i.current,{onPointerInTransitChange:p}=o,m=r.useCallback(()=>{c(null),p(!1)},[p]),h=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),u=Math.abs(t.left-e.x);switch(Math.min(n,r,o,u)){case u:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>m(),[m]),r.useEffect(()=>{if(s&&f){let e=e=>h(e,f),t=e=>h(e,s);return s.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{s.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[s,f,h,m]),r.useEffect(()=>{if(a){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==s?void 0:s.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,u=t.length-1;e<t.length;u=e++){let i=t[e],l=t[u],a=i.x,c=i.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-a)*(r-c)/(d-c)+a&&(o=!o)}return o}(n,a);r?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[s,f,a,d,m]),(0,v.jsx)(U,{...e,ref:l})}),[I,D]=h(E,{isInside:!1}),A=(0,f.sA)("TooltipContent"),U=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":u,onEscapeKeyDown:i,onPointerDownOutside:a,...s}=e,d=N(L,n),f=g(n),{onClose:p}=d;return r.useEffect(()=>(document.addEventListener(b,p),()=>document.removeEventListener(b,p)),[p]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,v.jsx)(l.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,v.jsxs)(c.VY,{"data-state":d.stateAttribute,...f,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(A,{children:o}),(0,v.jsx)(I,{scope:n,isInside:!0,children:(0,v.jsx)(m.fC,{id:d.contentId,role:"tooltip",children:u||o})})]})})});j.displayName=L;var Z="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=g(n);return D(Z,n).isInside?null:(0,v.jsx)(c.Eh,{...o,...r,ref:t})}).displayName=Z;var $=C,F=R,W=P,z=j},6606:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},886:function(e,t,n){n.d(t,{T:function(){return l}});var r,o=n(2265),u=n(1188),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{u.current!==n&&(l.current?.(n),u.current=n)},[n,u]),[n,r,l]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[c,e,l,a])]}Symbol("RADIX:SYNC_STATE")},1188:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);