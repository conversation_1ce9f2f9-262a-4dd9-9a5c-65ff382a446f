(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[130],{1176:function(e,t,s){Promise.resolve().then(s.bind(s,2844))},2844:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return z}});var r=s(7437),a=s(9376),n=s(7648),o=s(2869),i=s(2660),l=s(3247),c=s(740),d=s(2265),m=s(6070),x=s(401),u=s(8867),f=s(3245),p=s(4508),h=s(1312);let g=h.zt,j=h.fC,v=h.xz,N=d.forwardRef((e,t)=>{let{className:s,sideOffset:a=4,...n}=e;return(0,r.jsx)(h.VY,{ref:t,sideOffset:a,className:(0,p.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",s),...n})});function y(e){let{emoji:t,name:s,code:a,keywords:n=[],onCopy:i,className:l}=e,[c,h]=(0,d.useState)(!1),y=e=>{e.stopPropagation(),navigator.clipboard.writeText(t),h(!0),null==i||i(t),setTimeout(()=>h(!1),2e3)};return(0,r.jsx)(m.Zb,{className:(0,p.cn)("hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer group relative overflow-hidden",l),onClick:y,children:(0,r.jsxs)(m.aY,{className:"p-4 text-center",children:[(0,r.jsx)("div",{className:"text-4xl mb-2 group-hover:scale-110 transition-transform",children:t}),(0,r.jsx)("p",{className:"text-xs font-medium truncate",children:s}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity mt-1",children:a}),(0,r.jsxs)("div",{className:"flex gap-1 justify-center mt-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,r.jsx)(o.z,{size:"sm",variant:"ghost",className:"h-7 px-2",onClick:y,children:c?(0,r.jsx)(x.Z,{className:"w-3 h-3 text-green-600"}):(0,r.jsx)(u.Z,{className:"w-3 h-3"})}),n.length>0&&(0,r.jsx)(g,{children:(0,r.jsxs)(j,{children:[(0,r.jsx)(v,{asChild:!0,children:(0,r.jsx)(o.z,{size:"sm",variant:"ghost",className:"h-7 px-2",onClick:e=>e.stopPropagation(),children:(0,r.jsx)(f.Z,{className:"w-3 h-3"})})}),(0,r.jsx)(N,{children:(0,r.jsxs)("div",{className:"max-w-xs",children:[(0,r.jsx)("p",{className:"font-semibold mb-1",children:"Keywords:"}),(0,r.jsx)("p",{className:"text-xs",children:n.join(", ")})]})})]})})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"})]})})}N.displayName=h.VY.displayName;var b=s(23),w=s(7992),C=s(5186),k=s(5974);function z(){let e=(0,a.useParams)(),t=(null==e?void 0:e.category)||"",{toast:s}=(0,w.pm)(),[m,x]=(0,d.useState)(""),[u,f]=(0,d.useState)(null),h=(0,b.bl)(t),g=h?(0,b.Zn)(h.name):[],j=(0,d.useMemo)(()=>Array.from(new Set(g.map(e=>e.subcategory))).sort(),[g]),v=(0,d.useMemo)(()=>{let e=g;if(u&&(e=e.filter(e=>e.subcategory===u)),m){let t=m.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(t)||e.keywords.some(e=>e.toLowerCase().includes(t))||e.emoji===m)}return e},[g,m,u]),N=e=>{navigator.clipboard.writeText(e),s({title:"Copied!",description:"".concat(e," copied to clipboard"),duration:2e3})};return h?(0,r.jsxs)("div",{className:"container py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)(n.default,{href:"/",children:(0,r.jsxs)(o.z,{variant:"ghost",size:"sm",className:"mb-4",children:[(0,r.jsx)(i.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsx)("div",{className:"text-6xl",children:h.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-4xl font-bold",children:h.name}),(0,r.jsx)("p",{className:"text-xl text-muted-foreground",children:h.zhName}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:h.description})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("p",{className:"text-lg mb-2",children:[(0,r.jsx)("span",{className:"font-semibold",children:v.length})," of"," ",(0,r.jsx)("span",{className:"font-semibold",children:g.length})," emojis",u&&(0,r.jsxs)("span",{className:"text-muted-foreground",children:[" in ",u]})]})}),(0,r.jsxs)("div",{className:"relative w-full sm:w-96",children:[(0,r.jsx)(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,r.jsx)(C.I,{type:"text",placeholder:"Search emojis...",value:m,onChange:e=>x(e.target.value),className:"pl-10"})]})]}),j.length>1&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)(o.z,{size:"sm",variant:null===u?"default":"outline",onClick:()=>f(null),className:"gap-1",children:[(0,r.jsx)(c.Z,{className:"w-3 h-3"}),"All"]}),j.map(e=>{let t=g.filter(t=>t.subcategory===e).length;return(0,r.jsxs)(k.C,{variant:u===e?"default":"outline",className:(0,p.cn)("cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors",u===e&&"bg-primary text-primary-foreground"),onClick:()=>f(u===e?null:e),children:[e," (",t,")"]},e)})]})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4",children:v.map(e=>(0,r.jsx)(y,{emoji:e.emoji,name:e.name,code:e.code,keywords:e.keywords,onCopy:N},e.code))}),0===v.length&&(0,r.jsxs)("div",{className:"text-center py-20",children:[(0,r.jsxs)("p",{className:"text-xl text-muted-foreground",children:["No emojis found",m&&' matching "'.concat(m,'"'),u&&" in ".concat(u)]}),(0,r.jsxs)("div",{className:"flex gap-2 justify-center mt-4",children:[m&&(0,r.jsx)(o.z,{variant:"outline",onClick:()=>x(""),children:"Clear search"}),u&&(0,r.jsx)(o.z,{variant:"outline",onClick:()=>f(null),children:"Clear filter"})]})]})]}):(0,r.jsxs)("div",{className:"container py-20 text-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Category not found"}),(0,r.jsx)(n.default,{href:"/",children:(0,r.jsxs)(o.z,{children:[(0,r.jsx)(i.Z,{className:"w-4 h-4 mr-2"}),"Back to Home"]})})]})}},5974:function(e,t,s){"use strict";s.d(t,{C:function(){return i}});var r=s(7437);s(2265);var a=s(535),n=s(4508);let o=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(o({variant:s}),t),...a})}},6070:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return o},aY:function(){return d},ll:function(){return l}});var r=s(7437),a=s(2265),n=s(4508);let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});o.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"}},function(e){e.O(0,[438,367,38,2,971,117,744],function(){return e(e.s=1176)}),_N_E=e.O()}]);