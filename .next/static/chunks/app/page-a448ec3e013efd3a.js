(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{3631:function(e,s,t){Promise.resolve().then(t.bind(t,4876)),Promise.resolve().then(t.bind(t,1003)),Promise.resolve().then(t.bind(t,5596)),Promise.resolve().then(t.t.bind(t,2972,23))},5662:function(e,s,t){"use strict";t.d(s,{f:function(){return x}});var i=t(7437),n=t(6070),a=t(2869),r=t(7992),l=t(8867),d=t(4508),o=t(2265),c=t(3145);function m(e){let{code:s,emoji:t,imageUrl:n,name:a,size:r="md",showFallback:l=!0}=e,[d,m]=(0,o.useState)(!1),x={sm:"w-8 h-8 text-2xl",md:"w-12 h-12 text-4xl",lg:"w-16 h-16 text-6xl"};return n&&!d?(0,i.jsx)("div",{className:"".concat(x[r]," relative flex items-center justify-center"),children:(0,i.jsx)(c.default,{src:n,alt:"TikTok ".concat(a," emoji"),width:"sm"===r?32:"md"===r?48:64,height:"sm"===r?32:"md"===r?48:64,className:"rounded-full",onError:()=>m(!0),unoptimized:!0})}):l?(0,i.jsx)("div",{className:"".concat(x[r]," flex items-center justify-center"),children:(0,i.jsx)("span",{className:"select-none",children:t})}):(0,i.jsx)("div",{className:"".concat(x[r]," flex items-center justify-center bg-gray-100 rounded-full text-xs font-mono text-gray-600"),children:s})}function x(e){let{emoji:s,code:t,name:o,usage:c,className:x,size:u="md",imageUrl:h,description:f}=e,{toast:p}=(0,r.pm)();return(0,i.jsx)(n.Zb,{className:(0,d.cn)("relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden","before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100",{sm:"p-2 sm:p-3",md:"p-3 sm:p-4",lg:"p-4 sm:p-6"}[u],x),children:(0,i.jsxs)("div",{className:"relative z-10 text-center",children:[(0,i.jsx)("div",{className:"mb-3 flex justify-center",children:(0,i.jsx)(m,{code:t||"",emoji:s,imageUrl:h,name:o||"",size:u})}),t&&(0,i.jsx)("code",{className:"inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono",children:t}),o&&(0,i.jsx)("p",{className:"text-xs sm:text-sm text-muted-foreground mb-1 font-medium",children:o}),c&&(0,i.jsxs)("p",{className:"text-xs text-muted-foreground mb-2 sm:mb-3",children:["Usage: ",c]}),(0,i.jsxs)(a.z,{size:"sm",variant:"outline",className:"w-full text-xs sm:text-sm",onClick:()=>{navigator.clipboard.writeText(t||s),p({title:"Copied to clipboard!",description:"".concat(t||s," copied successfully")})},children:[(0,i.jsx)(l.Z,{className:"w-3 h-3 mr-1 sm:mr-2"}),(0,i.jsx)("span",{className:"hidden sm:inline",children:"Copy"}),(0,i.jsx)("span",{className:"sm:hidden",children:"Copy"})]})]})})}},4876:function(e,s,t){"use strict";t.d(s,{HiddenEmojisPreview:function(){return o}});var i=t(7437),n=t(7648),a=t(2869),r=t(5662),l=t(2510),d=t(6858);function o(){return(0,i.jsx)("section",{className:"py-12 md:py-16 bg-gradient-to-b from-background to-muted/20","aria-labelledby":"hidden-emojis-heading",children:(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("header",{className:"flex flex-col md:flex-row md:items-end justify-between mb-8 md:mb-12",children:[(0,i.jsxs)("div",{className:"max-w-2xl",children:[(0,i.jsxs)("h2",{id:"hidden-emojis-heading",className:"text-3xl md:text-4xl font-bold mb-4 flex items-center gap-3",children:[(0,i.jsx)("span",{className:"text-4xl animate-pulse",role:"img","aria-label":"Target emoji",children:"\uD83C\uDFAF"}),"TikTok Hidden Emoji Codes"]}),(0,i.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed",children:"Click copy and paste in TikTok comments to unlock secret emojis that aren't available anywhere else"})]}),(0,i.jsx)(n.default,{href:"/hidden-emojis",className:"hidden md:block","aria-label":"View all 46 hidden emoji codes",children:(0,i.jsxs)(a.z,{variant:"ghost",className:"gap-2 hover:bg-muted/50 transition-all",children:["View All 46 Emojis",(0,i.jsx)(d.Z,{className:"w-4 h-4","aria-hidden":"true"})]})})]}),(0,i.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4 md:gap-6 mb-8 md:mb-12",role:"grid","aria-label":"Preview of hidden emoji codes",children:l.iN.slice(0,16).map((e,s)=>(0,i.jsx)("div",{className:"animate-fade-in-up",style:{animationDelay:"".concat(50*s,"ms")},role:"gridcell",children:(0,i.jsx)(r.f,{emoji:e.emoji,code:e.code,name:e.name,size:"sm",imageUrl:e.imageUrl,description:e.description})},e.code))}),(0,i.jsx)("footer",{className:"text-center md:hidden",children:(0,i.jsx)(n.default,{href:"/hidden-emojis","aria-label":"View all 46 hidden emoji codes",children:(0,i.jsxs)(a.z,{variant:"outline",className:"gap-2 hover:bg-muted/50 transition-all hover:scale-105",children:["View All 46 Hidden Emojis",(0,i.jsx)(d.Z,{className:"w-4 h-4","aria-hidden":"true"})]})})})]})})}},1003:function(e,s,t){"use strict";t.d(s,{TrendingContent:function(){return f}});var i=t(7437),n=t(7648),a=t(2869),r=t(6070),l=t(5974),d=t(2510),o=t(525),c=t(2023),m=t(6858),x=t(8867);let u=(0,t(9763).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);var h=t(7992);function f(){let{toast:e}=(0,h.pm)(),s=(s,t)=>{navigator.clipboard.writeText(s),e({title:"Copied!",description:"".concat(t," copied to clipboard")})};return(0,i.jsx)("section",{className:"py-12 md:py-16 bg-gradient-to-b from-muted/20 to-muted/40","aria-labelledby":"trending-content-heading",children:(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("header",{className:"text-center mb-8 md:mb-12",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,i.jsx)(o.Z,{className:"w-8 h-8 text-tiktok-pink","aria-hidden":"true"}),(0,i.jsx)("h2",{id:"trending-content-heading",className:"text-3xl md:text-4xl font-bold bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent",children:"What's Trending on TikTok"})]}),(0,i.jsx)("p",{className:"text-lg text-muted-foreground mb-8 max-w-2xl mx-auto",children:"Discover the hottest emoji combinations and trending symbols that TikTokers are using right now"})]}),(0,i.jsxs)("div",{className:"mb-8 md:mb-12",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(c.Z,{className:"w-6 h-6 text-tiktok-pink"}),(0,i.jsx)("h3",{className:"text-2xl font-bold",children:"Popular Combinations"}),(0,i.jsx)(l.C,{variant:"secondary",className:"ml-2",children:"Hot"})]}),(0,i.jsx)(n.default,{href:"/popular-combos",className:"hidden md:block",children:(0,i.jsxs)(a.z,{variant:"ghost",size:"sm",className:"gap-2",children:["View All",(0,i.jsx)(m.Z,{className:"w-4 h-4"})]})})]}),(0,i.jsx)("div",{className:"grid md:grid-cols-2 gap-4 mb-6",children:d.E$.slice(0,8).map(e=>(0,i.jsx)(r.Zb,{className:"group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50",children:(0,i.jsx)(r.Ol,{className:"pb-4",children:(0,i.jsxs)("div",{className:"flex items-start justify-between gap-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,i.jsx)("span",{className:"text-3xl group-hover:scale-110 transition-transform",children:e.emojis}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,i.jsx)(r.ll,{className:"text-lg",children:e.meaning}),(0,i.jsxs)(l.C,{variant:"secondary",className:"text-xs",children:[((e.popularity||0)/1e6).toFixed(1),"M uses"]})]}),(0,i.jsx)(r.SZ,{className:"text-sm line-clamp-2",children:e.usage})]})]})}),(0,i.jsxs)(a.z,{size:"sm",variant:"outline",onClick:()=>s(e.emojis,"Combination"),className:"shrink-0 gap-2",children:[(0,i.jsx)(x.Z,{className:"w-3 h-3"}),"Copy"]})]})})},e.id))})]}),(0,i.jsxs)("div",{className:"mb-8 md:mb-12",children:[(0,i.jsx)("div",{className:"flex items-center justify-between mb-8",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(o.Z,{className:"w-6 h-6 text-tiktok-blue"}),(0,i.jsx)("h3",{className:"text-2xl font-bold",children:"Trending Individual Emojis"}),(0,i.jsx)(l.C,{variant:"outline",className:"ml-2",children:"Live"})]})}),(0,i.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4",children:d.AH.slice(0,12).map(e=>(0,i.jsx)(n.default,{href:"/emoji/".concat(encodeURIComponent(e.char)),children:(0,i.jsx)(r.Zb,{className:"group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center",children:(0,i.jsxs)(r.aY,{className:"pt-6 pb-4",children:[(0,i.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform",children:e.char}),(0,i.jsx)("h4",{className:"font-semibold text-sm mb-1 line-clamp-1",children:e.name}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-2",children:e.tiktokMeaning||e.meaning}),(0,i.jsx)("div",{className:"mt-2",children:(0,i.jsx)(u,{className:"w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"})})]})})},e.char))})]}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(n.default,{href:"/popular-combos",children:(0,i.jsxs)(a.z,{size:"lg",variant:"default",className:"gap-2 min-w-[200px]",children:[(0,i.jsx)(c.Z,{className:"w-4 h-4"}),"Explore All Combinations"]})}),(0,i.jsx)(n.default,{href:"/search",children:(0,i.jsxs)(a.z,{size:"lg",variant:"outline",className:"gap-2 min-w-[200px]",children:[(0,i.jsx)(o.Z,{className:"w-4 h-4"}),"Search Trending Emojis"]})})]})})]})})}},5974:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var i=t(7437);t(2265);var n=t(535),a=t(4508);let r=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...n}=e;return(0,i.jsx)("div",{className:(0,a.cn)(r({variant:t}),s),...n})}},5596:function(e,s,t){"use strict";t.d(s,{CategoryCardSkeleton:function(){return r},LazyLoad:function(){return a}});var i=t(7437),n=t(2265);function a(e){let{children:s,fallback:t=null,rootMargin:a="50px",threshold:r=.1,className:l}=e,[d,o]=(0,n.useState)(!1),[c,m]=(0,n.useState)(!1),x=(0,n.useRef)(null);return(0,n.useEffect)(()=>{let e=new IntersectionObserver(s=>{let[t]=s;t.isIntersecting&&!c&&(o(!0),m(!0),e.disconnect())},{rootMargin:a,threshold:r}),s=x.current;return s&&e.observe(s),()=>{s&&e.unobserve(s)}},[a,r,c]),(0,i.jsx)("div",{ref:x,className:l,children:d?s:t})}function r(){return(0,i.jsx)("div",{className:"animate-pulse",children:(0,i.jsxs)("div",{className:"bg-muted rounded-2xl p-8 space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-muted-foreground/20 rounded-full"}),(0,i.jsx)("div",{className:"w-8 h-6 bg-muted-foreground/20 rounded"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("div",{className:"h-4 bg-muted-foreground/20 rounded w-3/4"}),(0,i.jsx)("div",{className:"h-3 bg-muted-foreground/20 rounded w-full"}),(0,i.jsx)("div",{className:"h-3 bg-muted-foreground/20 rounded w-2/3"})]})]})})}},6858:function(e,s,t){"use strict";t.d(s,{Z:function(){return i}});let i=(0,t(9763).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2023:function(e,s,t){"use strict";t.d(s,{Z:function(){return i}});let i=(0,t(9763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},525:function(e,s,t){"use strict";t.d(s,{Z:function(){return i}});let i=(0,t(9763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])}},function(e){e.O(0,[438,150,672,971,117,744],function(){return e(e.s=3631)}),_N_E=e.O()}]);