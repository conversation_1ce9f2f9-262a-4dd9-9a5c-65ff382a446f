(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{4647:function(e,t,r){Promise.resolve().then(r.t.bind(r,7960,23)),Promise.resolve().then(r.bind(r,7105)),Promise.resolve().then(r.bind(r,8783)),Promise.resolve().then(r.bind(r,8271)),Promise.resolve().then(r.bind(r,9556)),Promise.resolve().then(r.t.bind(r,2972,23)),Promise.resolve().then(r.t.bind(r,4742,23))},7105:function(e,t,r){"use strict";r.d(t,{ExtensionErrorHandler:function(){return s}});var n=r(2265);function s(){return(0,n.useEffect)(()=>{let e=e=>{var t,r,n,s,a;if((null===(r=e.error)||void 0===r?void 0:null===(t=r.stack)||void 0===t?void 0:t.includes("chrome-extension://"))||(null===(s=e.error)||void 0===s?void 0:null===(n=s.message)||void 0===n?void 0:n.includes("chrome-extension://"))||(null===(a=e.filename)||void 0===a?void 0:a.includes("chrome-extension://")))return e.preventDefault(),console.warn("Browser extension error caught and ignored:",e.error),!0},t=e=>{var t,r,n,s;if((null===(r=e.reason)||void 0===r?void 0:null===(t=r.stack)||void 0===t?void 0:t.includes("chrome-extension://"))||(null===(s=e.reason)||void 0===s?void 0:null===(n=s.message)||void 0===n?void 0:n.includes("chrome-extension://"))||String(e.reason).includes("chrome-extension://"))return e.preventDefault(),console.warn("Browser extension promise rejection caught and ignored:",e.reason),!0};return window.addEventListener("error",e),window.addEventListener("unhandledrejection",t),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",t)}},[]),null}},8783:function(e,t,r){"use strict";r.d(t,{Navigation:function(){return y}});var n=r(7437),s=r(7648),a=r(9376),o=r(3247),i=r(1473),d=r(875),l=r(5186),c=r(2869),u=r(2265),f=r(85),m=r(407),p=r(401),x=r(519),v=r(4508);let h=f.fC,g=f.xz;f.ZA,f.Uv,f.Tr,f.Ee,u.forwardRef((e,t)=>{let{className:r,inset:s,children:a,...o}=e;return(0,n.jsxs)(f.fF,{ref:t,className:(0,v.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",r),...o,children:[a,(0,n.jsx)(m.Z,{className:"ml-auto"})]})}).displayName=f.fF.displayName,u.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(f.tu,{ref:t,className:(0,v.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",r),...s})}).displayName=f.tu.displayName;let b=u.forwardRef((e,t)=>{let{className:r,sideOffset:s=4,...a}=e;return(0,n.jsx)(f.Uv,{children:(0,n.jsx)(f.VY,{ref:t,sideOffset:s,className:(0,v.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",r),...a})})});b.displayName=f.VY.displayName;let w=u.forwardRef((e,t)=>{let{className:r,inset:s,...a}=e;return(0,n.jsx)(f.ck,{ref:t,className:(0,v.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",r),...a})});w.displayName=f.ck.displayName,u.forwardRef((e,t)=>{let{className:r,children:s,checked:a,...o}=e;return(0,n.jsxs)(f.oC,{ref:t,className:(0,v.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:a,...o,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(f.wU,{children:(0,n.jsx)(p.Z,{className:"h-4 w-4"})})}),s]})}).displayName=f.oC.displayName,u.forwardRef((e,t)=>{let{className:r,children:s,...a}=e;return(0,n.jsxs)(f.Rk,{ref:t,className:(0,v.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...a,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(f.wU,{children:(0,n.jsx)(x.Z,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=f.Rk.displayName,u.forwardRef((e,t)=>{let{className:r,inset:s,...a}=e;return(0,n.jsx)(f.__,{ref:t,className:(0,v.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",r),...a})}).displayName=f.__.displayName,u.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(f.Z0,{ref:t,className:(0,v.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=f.Z0.displayName;var N=r(23);function y(){(0,a.usePathname)();let e=(0,a.useRouter)(),[t,r]=(0,u.useState)("");return(0,n.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,n.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,n.jsxs)(s.default,{href:"/",className:"flex items-center space-x-2 flex-shrink-0",children:[(0,n.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFB5"}),(0,n.jsx)("span",{className:"font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent",children:"TikTok Emoji"})]}),(0,n.jsxs)("form",{onSubmit:r=>{r.preventDefault(),t.trim()&&e.push("/search?q=".concat(encodeURIComponent(t)))},className:"relative flex-1 max-w-2xl mx-8",children:[(0,n.jsx)(l.I,{type:"search",placeholder:"Search emojis, codes, or meanings...",className:"pr-10 w-full",value:t,onChange:e=>r(e.target.value)}),(0,n.jsx)(c.z,{type:"submit",size:"icon",variant:"ghost",className:"absolute right-0 top-0",children:(0,n.jsx)(o.Z,{className:"h-4 w-4"})})]}),(0,n.jsxs)(h,{children:[(0,n.jsx)(g,{asChild:!0,children:(0,n.jsxs)(c.z,{variant:"outline",className:"gap-2 flex-shrink-0",children:[(0,n.jsx)(i.Z,{className:"h-4 w-4"}),"Categories",(0,n.jsx)(d.Z,{className:"h-3 w-3"})]})}),(0,n.jsx)(b,{align:"end",className:"w-64",children:N.aV.map(e=>(0,n.jsx)(w,{asChild:!0,className:"cursor-pointer",children:(0,n.jsxs)(s.default,{href:"/category/".concat(e.id),className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-lg",children:e.icon}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsx)("div",{className:"text-xs text-muted-foreground",children:e.zhName})]})]}),(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:e.count})]})},e.id))})]})]})})}},8271:function(e,t,r){"use strict";r.d(t,{PerformanceMonitor:function(){return s}});var n=r(2265);function s(){return(0,n.useEffect)(()=>{let e={},t=()=>{if("PerformanceObserver"in window)try{new PerformanceObserver(t=>{let r=t.getEntries(),n=r[r.length-1];e.lcp=n.startTime}).observe({type:"largest-contentful-paint",buffered:!0}),new PerformanceObserver(t=>{t.getEntries().forEach(t=>{e.fid=t.processingStart-t.startTime})}).observe({type:"first-input",buffered:!0});let t=0;new PerformanceObserver(r=>{r.getEntries().forEach(r=>{r.hadRecentInput||(t+=r.value,e.cls=t)})}).observe({type:"layout-shift",buffered:!0})}catch(e){console.warn("Performance monitoring failed:",e)}if("performance"in window&&"getEntriesByType"in performance){let t=performance.getEntriesByType("navigation");if(t.length>0){let r=t[0];e.ttfb=r.responseStart-r.requestStart;let n=performance.getEntriesByType("paint").find(e=>"first-contentful-paint"===e.name);n&&(e.fcp=n.startTime)}}};"complete"===document.readyState?t():window.addEventListener("load",t);let r=setTimeout(()=>{console.log("Performance Metrics:",e),"undefined"!=typeof gtag&&Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&gtag("event","web_vital",{name:t,value:Math.round("cls"===t?1e3*r:r),event_category:"Web Vitals"})})},5e3);return()=>{clearTimeout(r),window.removeEventListener("load",t)}},[]),null}},9556:function(e,t,r){"use strict";r.d(t,{Toaster:function(){return h}});var n=r(7437),s=r(2265),a=r(1915),o=r(535),i=r(2489),d=r(4508);let l=a.zt,c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.l_,{ref:t,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...s})});c.displayName=a.l_.displayName;let u=(0,o.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=s.forwardRef((e,t)=>{let{className:r,variant:s,...o}=e;return(0,n.jsx)(a.fC,{ref:t,className:(0,d.cn)(u({variant:s}),r),...o})});f.displayName=a.fC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.aU,{ref:t,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...s})}).displayName=a.aU.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.x8,{ref:t,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...s,children:(0,n.jsx)(i.Z,{className:"h-4 w-4"})})});m.displayName=a.x8.displayName;let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.Dx,{ref:t,className:(0,d.cn)("text-sm font-semibold",r),...s})});p.displayName=a.Dx.displayName;let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.dk,{ref:t,className:(0,d.cn)("text-sm opacity-90",r),...s})});x.displayName=a.dk.displayName;var v=r(7992);function h(){let{toasts:e}=(0,v.pm)();return(0,n.jsxs)(l,{children:[e.map(function(e){let{id:t,title:r,description:s,action:a,...o}=e;return(0,n.jsxs)(f,{...o,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[r&&(0,n.jsx)(p,{children:r}),s&&(0,n.jsx)(x,{children:s})]}),a,(0,n.jsx)(m,{})]},t)}),(0,n.jsx)(c,{})]})}},7960:function(){}},function(e){e.O(0,[540,438,52,367,781,435,2,971,117,744],function(){return e(e.s=4647)}),_N_E=e.O()}]);