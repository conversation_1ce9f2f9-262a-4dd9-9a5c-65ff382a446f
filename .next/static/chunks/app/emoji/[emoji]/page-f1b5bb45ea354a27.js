(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[531],{4213:function(e,s,n){Promise.resolve().then(n.bind(n,6917))},6917:function(e,s,n){"use strict";n.d(s,{default:function(){return u}});var t=n(7437),a=n(7648),i=n(2660),r=n(8867),c=n(525);let l=(0,n(9763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);var o=n(2869),d=n(6070),m=n(5974),x=n(2510),h=n(7992);function u(e){let{emoji:s}=e,n=decodeURIComponent(s),a=x.AH.find(e=>e.char===n);return a?(0,t.jsx)(j,{emoji:a}):(0,t.jsx)(j,{emoji:{char:n,name:"Emoji",meaning:"This is an emoji",tiktokMeaning:"Special meaning on TikTok",category:"Emotions",usage:"⭐⭐⭐"}})}function j(e){let{emoji:s}=e,{toast:n}=(0,h.pm)(),u=[{combo:"".concat(s.char).concat(s.char).concat(s.char),meaning:"Emphasis x3"},{combo:"".concat(s.char,"\uD83E\uDD23"),meaning:"With laughing"},{combo:"help".concat(s.char),meaning:"Help + emoji"},{combo:"not me".concat(s.char),meaning:"Not me + emoji"}],j=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Emoji";navigator.clipboard.writeText(e),n({title:"Copied!",description:"".concat(s," copied to clipboard")})};return(0,t.jsxs)("div",{className:"container py-8",children:[(0,t.jsxs)(a.default,{href:"/",className:"inline-flex items-center text-muted-foreground hover:text-foreground mb-6",children:[(0,t.jsx)(i.Z,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)(d.Zb,{children:(0,t.jsx)(d.aY,{className:"pt-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-8xl mb-4",children:s.char}),(0,t.jsx)("h1",{className:"text-3xl font-bold mb-2",children:s.name}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:s.meaning}),(0,t.jsxs)(o.z,{size:"lg",className:"bg-tiktok-pink hover:bg-pink-600",onClick:()=>j(s.char),children:[(0,t.jsx)(r.Z,{className:"w-4 h-4 mr-2"}),"Copy Emoji"]})]})})}),s.tiktokMeaning&&(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-tiktok-pink",children:"TikTok"})," Special Meaning"]})}),(0,t.jsx)(d.aY,{children:(0,t.jsx)("p",{className:"text-lg",children:s.tiktokMeaning})})]}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsx)(d.ll,{children:"Popular Combinations"})}),(0,t.jsx)(d.aY,{children:(0,t.jsx)("div",{className:"grid gap-3",children:u.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg hover:bg-muted/80 cursor-pointer",onClick:()=>j(e.combo,"Combination"),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-lg font-mono",children:e.combo}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.meaning})]}),(0,t.jsx)(r.Z,{className:"w-4 h-4 text-muted-foreground"})]},s))})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(c.Z,{className:"w-5 h-5"}),"Usage Statistics"]})}),(0,t.jsx)(d.aY,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Daily Usage"}),(0,t.jsx)("span",{className:"font-semibold",children:"2.3M"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Popularity Rank"}),(0,t.jsx)("span",{className:"font-semibold",children:"#12"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Trend"}),(0,t.jsx)("span",{className:"font-semibold text-green-600",children:"+15%"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Emotion"}),(0,t.jsx)("span",{className:"font-semibold",children:"85% positive"})]})]})})]}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsx)(d.ll,{children:"Category & Tags"})}),(0,t.jsx)(d.aY,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Category"}),(0,t.jsx)(m.C,{variant:"secondary",children:s.category})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Usage"}),(0,t.jsx)("div",{className:"flex gap-1",children:s.usage&&s.usage.split("").map((e,s)=>(0,t.jsx)(l,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"},s))})]})]})})]}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsx)(d.ll,{children:"Related Emojis"})}),(0,t.jsx)(d.aY,{children:(0,t.jsx)("div",{className:"grid grid-cols-4 gap-2",children:x.AH.filter(e=>e.category===s.category&&e.char!==s.char).slice(0,8).map(e=>(0,t.jsx)(a.default,{href:"/emoji/".concat(encodeURIComponent(e.char)),className:"text-2xl hover:scale-110 transition-transform text-center p-2",title:e.name,children:e.char},e.char))})})]})]})]})]})}},5974:function(e,s,n){"use strict";n.d(s,{C:function(){return c}});var t=n(7437);n(2265);var a=n(535),i=n(4508);let r=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:n,...a}=e;return(0,t.jsx)("div",{className:(0,i.cn)(r({variant:n}),s),...a})}},2660:function(e,s,n){"use strict";n.d(s,{Z:function(){return t}});let t=(0,n(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8867:function(e,s,n){"use strict";n.d(s,{Z:function(){return t}});let t=(0,n(9763).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},525:function(e,s,n){"use strict";n.d(s,{Z:function(){return t}});let t=(0,n(9763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])}},function(e){e.O(0,[438,672,971,117,744],function(){return e(e.s=4213)}),_N_E=e.O()}]);