(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[797],{8249:function(e,s,t){Promise.resolve().then(t.bind(t,6034))},6034:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return j}});var n=t(7437),l=t(2265),a=t(9376),i=t(7648),r=t(2660),c=t(3247),o=t(2869),d=t(5186),m=t(6070),u=t(5662),x=t(2510),h=t(2339);function f(){var e;let s=(0,a.useSearchParams)(),[t,f]=(0,l.useState)((null==s?void 0:s.get("q"))||""),[j,g]=(0,l.useState)((null==s?void 0:s.get("category"))||"all"),[p,N]=(0,l.useState)("all");(0,l.useEffect)(()=>{let e=null==s?void 0:s.get("q"),t=null==s?void 0:s.get("category");e&&f(e),t&&(g(t),N("all"))},[s]);let b=x.iN.filter(e=>(!t||e.code.toLowerCase().includes(t.toLowerCase())||e.name.toLowerCase().includes(t.toLowerCase()))&&("all"===j||"hidden"===j)),v=x.AH.filter(e=>{let s=!t||e.char.includes(t)||e.name.toLowerCase().includes(t.toLowerCase())||e.meaning.toLowerCase().includes(t.toLowerCase())||e.tiktokMeaning&&e.tiktokMeaning.toLowerCase().includes(t.toLowerCase()),n="all"===j||e.category.toLowerCase()===j.toLowerCase();return s&&n}),w=x.E$.filter(e=>!t||e.emojis.includes(t)||e.meaning.toLowerCase().includes(t.toLowerCase())||e.usage.toLowerCase().includes(t.toLowerCase())),y=b.length+v.length+w.length;return(0,n.jsxs)("div",{className:"container py-8",children:[(0,n.jsxs)(i.default,{href:"/",className:"inline-flex items-center text-muted-foreground hover:text-foreground mb-6",children:[(0,n.jsx)(r.Z,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,n.jsx)("h1",{className:"text-4xl font-bold mb-8",children:"Search Emojis"}),(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)("div",{className:"relative max-w-2xl",children:[(0,n.jsx)(c.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5"}),(0,n.jsx)(d.I,{type:"search",placeholder:"Search emojis, codes, or meanings...",className:"pl-10 text-lg",value:t,onChange:e=>f(e.target.value)})]})}),!t&&"all"!==j&&(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Current category:"}),(0,n.jsx)("span",{className:"font-semibold",children:(null===(e=x.lS.find(e=>e.id===j))||void 0===e?void 0:e.name)||"All"}),(0,n.jsx)(o.z,{size:"sm",variant:"ghost",onClick:()=>g("all"),children:"Clear Filter"})]})}),t&&(0,n.jsxs)("p",{className:"text-muted-foreground mb-6",children:["Found ",(0,n.jsx)("span",{className:"font-semibold text-foreground",children:y})," results"]}),(0,n.jsxs)(h.mQ,{value:p,onValueChange:N,className:"w-full",children:[(0,n.jsxs)(h.dr,{className:"grid w-full max-w-md grid-cols-4",children:[(0,n.jsxs)(h.SP,{value:"all",children:["All (",y,")"]}),(0,n.jsxs)(h.SP,{value:"hidden",children:["Hidden (",b.length,")"]}),(0,n.jsxs)(h.SP,{value:"emoji",children:["Emojis (",v.length,")"]}),(0,n.jsxs)(h.SP,{value:"combo",children:["Combos (",w.length,")"]})]}),(0,n.jsxs)(h.nU,{value:"all",className:"mt-6 space-y-8",children:[b.length>0&&(0,n.jsxs)("section",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Hidden Emoji Codes"}),(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:b.slice(0,12).map(e=>(0,n.jsx)(u.f,{emoji:e.emoji,code:e.code,name:e.name,usage:e.usage},e.code))}),b.length>12&&(0,n.jsx)("div",{className:"mt-4 text-center",children:(0,n.jsxs)(o.z,{variant:"outline",onClick:()=>N("hidden"),children:["View all ",b.length," hidden emojis"]})})]}),v.length>0&&(0,n.jsxs)("section",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Emojis"}),(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:v.slice(0,8).map(e=>(0,n.jsx)(i.default,{href:"/emoji/".concat(encodeURIComponent(e.char)),children:(0,n.jsx)(m.Zb,{className:"hover:shadow-lg transition-shadow cursor-pointer h-full",children:(0,n.jsxs)(m.aY,{className:"pt-6 text-center",children:[(0,n.jsx)("div",{className:"text-4xl mb-2",children:e.char}),(0,n.jsx)("h3",{className:"font-semibold",children:e.name}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:e.tiktokMeaning||e.meaning})]})})},e.char))}),v.length>8&&(0,n.jsx)("div",{className:"mt-4 text-center",children:(0,n.jsxs)(o.z,{variant:"outline",onClick:()=>N("emoji"),children:["View all ",v.length," emojis"]})})]}),w.length>0&&(0,n.jsxs)("section",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Popular Combinations"}),(0,n.jsx)("div",{className:"space-y-3",children:w.slice(0,4).map(e=>(0,n.jsx)(m.Zb,{children:(0,n.jsx)(m.aY,{className:"pt-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)("span",{className:"text-2xl",children:e.emojis}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-semibold",children:e.meaning}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:e.usage})]})]}),(0,n.jsx)(o.z,{size:"sm",variant:"outline",onClick:()=>navigator.clipboard.writeText(e.emojis),children:"Copy"})]})})},e.id))}),w.length>4&&(0,n.jsx)("div",{className:"mt-4 text-center",children:(0,n.jsxs)(o.z,{variant:"outline",onClick:()=>N("combo"),children:["View all ",w.length," combinations"]})})]})]}),(0,n.jsx)(h.nU,{value:"hidden",className:"mt-6",children:(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:b.map(e=>(0,n.jsx)(u.f,{emoji:e.emoji,code:e.code,name:e.name,usage:e.usage},e.code))})}),(0,n.jsx)(h.nU,{value:"emoji",className:"mt-6",children:(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:v.map(e=>(0,n.jsx)(i.default,{href:"/emoji/".concat(encodeURIComponent(e.char)),children:(0,n.jsx)(m.Zb,{className:"hover:shadow-lg transition-shadow cursor-pointer h-full",children:(0,n.jsxs)(m.aY,{className:"pt-6 text-center",children:[(0,n.jsx)("div",{className:"text-4xl mb-2",children:e.char}),(0,n.jsx)("h3",{className:"font-semibold",children:e.name}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:e.tiktokMeaning||e.meaning})]})})},e.char))})}),(0,n.jsx)(h.nU,{value:"combo",className:"mt-6",children:(0,n.jsx)("div",{className:"space-y-3",children:w.map(e=>(0,n.jsx)(m.Zb,{children:(0,n.jsx)(m.aY,{className:"pt-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)("span",{className:"text-2xl",children:e.emojis}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-semibold",children:e.meaning}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:e.usage})]})]}),(0,n.jsx)(o.z,{size:"sm",variant:"outline",onClick:()=>navigator.clipboard.writeText(e.emojis),children:"Copy"})]})})},e.id))})})]}),0===y&&t&&(0,n.jsx)(m.Zb,{className:"mt-8",children:(0,n.jsxs)(m.aY,{className:"pt-6 text-center",children:[(0,n.jsxs)("p",{className:"text-muted-foreground mb-4",children:['No results found for "',t,'"']}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("p",{className:"text-sm",children:"Try these suggestions:"}),(0,n.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,n.jsx)("li",{children:"• Check your spelling"}),(0,n.jsx)("li",{children:"• Use shorter keywords"}),(0,n.jsx)("li",{children:"• Try searching for the emoji itself"})]})]})]})}),!t&&"all"===j&&(0,n.jsxs)("div",{className:"mt-12",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"\uD83D\uDD25 Popular Searches"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:["smile","happy","love","\uD83D\uDE0D","\uD83D\uDD25","POV","help","not me"].map(e=>(0,n.jsx)(o.z,{variant:"outline",size:"sm",onClick:()=>f(e),children:e},e))})]})]})}function j(){return(0,n.jsx)(l.Suspense,{fallback:(0,n.jsx)("div",{className:"container py-8",children:(0,n.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})})}),children:(0,n.jsx)(f,{})})}},5662:function(e,s,t){"use strict";t.d(s,{f:function(){return u}});var n=t(7437),l=t(6070),a=t(2869),i=t(7992),r=t(8867),c=t(4508),o=t(2265),d=t(3145);function m(e){let{code:s,emoji:t,imageUrl:l,name:a,size:i="md",showFallback:r=!0}=e,[c,m]=(0,o.useState)(!1),u={sm:"w-8 h-8 text-2xl",md:"w-12 h-12 text-4xl",lg:"w-16 h-16 text-6xl"};return l&&!c?(0,n.jsx)("div",{className:"".concat(u[i]," relative flex items-center justify-center"),children:(0,n.jsx)(d.default,{src:l,alt:"TikTok ".concat(a," emoji"),width:"sm"===i?32:"md"===i?48:64,height:"sm"===i?32:"md"===i?48:64,className:"rounded-full",onError:()=>m(!0),unoptimized:!0})}):r?(0,n.jsx)("div",{className:"".concat(u[i]," flex items-center justify-center"),children:(0,n.jsx)("span",{className:"select-none",children:t})}):(0,n.jsx)("div",{className:"".concat(u[i]," flex items-center justify-center bg-gray-100 rounded-full text-xs font-mono text-gray-600"),children:s})}function u(e){let{emoji:s,code:t,name:o,usage:d,className:u,size:x="md",imageUrl:h,description:f}=e,{toast:j}=(0,i.pm)();return(0,n.jsx)(l.Zb,{className:(0,c.cn)("relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden","before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100",{sm:"p-2 sm:p-3",md:"p-3 sm:p-4",lg:"p-4 sm:p-6"}[x],u),children:(0,n.jsxs)("div",{className:"relative z-10 text-center",children:[(0,n.jsx)("div",{className:"mb-3 flex justify-center",children:(0,n.jsx)(m,{code:t||"",emoji:s,imageUrl:h,name:o||"",size:x})}),t&&(0,n.jsx)("code",{className:"inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono",children:t}),o&&(0,n.jsx)("p",{className:"text-xs sm:text-sm text-muted-foreground mb-1 font-medium",children:o}),d&&(0,n.jsxs)("p",{className:"text-xs text-muted-foreground mb-2 sm:mb-3",children:["Usage: ",d]}),(0,n.jsxs)(a.z,{size:"sm",variant:"outline",className:"w-full text-xs sm:text-sm",onClick:()=>{navigator.clipboard.writeText(t||s),j({title:"Copied to clipboard!",description:"".concat(t||s," copied successfully")})},children:[(0,n.jsx)(r.Z,{className:"w-3 h-3 mr-1 sm:mr-2"}),(0,n.jsx)("span",{className:"hidden sm:inline",children:"Copy"}),(0,n.jsx)("span",{className:"sm:hidden",children:"Copy"})]})]})})}},5186:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var n=t(7437),l=t(2265),a=t(4508);let i=l.forwardRef((e,s)=>{let{className:t,type:l,...i}=e;return(0,n.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},2339:function(e,s,t){"use strict";t.d(s,{SP:function(){return o},dr:function(){return c},mQ:function(){return r},nU:function(){return d}});var n=t(7437),l=t(2265),a=t(271),i=t(4508);let r=a.fC,c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,n.jsx)(a.aV,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...l})});c.displayName=a.aV.displayName;let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,n.jsx)(a.xz,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...l})});o.displayName=a.xz.displayName;let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,n.jsx)(a.VY,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...l})});d.displayName=a.VY.displayName},3247:function(e,s,t){"use strict";t.d(s,{Z:function(){return n}});let n=(0,t(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},9376:function(e,s,t){"use strict";var n=t(5475);t.o(n,"useParams")&&t.d(s,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(s,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(s,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(s,{useSearchParams:function(){return n.useSearchParams}})}},function(e){e.O(0,[438,52,150,963,672,971,117,744],function(){return e(e.s=8249)}),_N_E=e.O()}]);