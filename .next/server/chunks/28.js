exports.id=28,exports.ids=[28],exports.modules={5384:e=>{e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},5664:(e,t,r)=>{"use strict";r.d(t,{Ry:()=>s});var n=new WeakMap,o=new WeakMap,a={},l=0,i=function(e){return e&&(e.host||i(e.parentNode))},u=function(e,t,r,u){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=i(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var c=a[r],d=[],f=new Set,p=new Set(s),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};s.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(u),a=null!==t&&"false"!==t,l=(n.get(e)||0)+1,i=(c.get(e)||0)+1;n.set(e,l),c.set(e,i),d.push(e),1===l&&a&&o.set(e,!0),1===i&&e.setAttribute(r,"true"),a||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=n.get(e)-1,a=c.get(e)-1;n.set(e,t),c.set(e,a),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),a||e.removeAttribute(r)}),--l||(n=new WeakMap,n=new WeakMap,o=new WeakMap,a={})}},s=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n,o=Array.from(Array.isArray(e)?e:[e]),a=t||(n=e,"undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),u(o,a,r,"aria-hidden")):function(){return null}}},6557:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(7577),o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,t)=>{let r=(0,n.forwardRef)(({color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:u,className:s="",children:c,...d},f)=>(0,n.createElement)("svg",{ref:f,...o,width:l,height:l,stroke:r,strokeWidth:u?24*Number(i)/Number(l):i,className:["lucide",`lucide-${a(e)}`,s].join(" "),...d},[...t.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r}},2933:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},941:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},9183:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3982:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},924:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},8307:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},4019:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},434:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(9404),o=r.n(n)},5047:(e,t,r)=>{"use strict";var n=r(7389);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},3486:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(8974),o=r(3658);function a(e,t){return(0,o.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(3658);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5424:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callServer",{enumerable:!0,get:function(){return o}});let n=r(2994);async function o(e,t){let r=(0,n.getServerActionDispatcher)();if(!r)throw Error("Invariant: missing action dispatcher.");return new Promise((n,o)=>{r({actionId:e,actionArgs:t,resolve:n,reject:o})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return l}});let n=r(7577),o=r(962),a="next-route-announcer";function l(e){let{tree:t}=e,[r,l]=(0,n.useState)(null);(0,n.useEffect)(()=>(l(function(){var e;let t=document.getElementsByName(a)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[i,u]=(0,n.useState)(""),s=(0,n.useRef)();return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==s.current&&s.current!==e&&u(e),s.current=e},[t]),r?(0,o.createPortal)(i,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5138:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION:function(){return n},FLIGHT_PARAMETERS:function(){return u},NEXT_DID_POSTPONE_HEADER:function(){return c},NEXT_ROUTER_PREFETCH_HEADER:function(){return a},NEXT_ROUTER_STATE_TREE:function(){return o},NEXT_RSC_UNION_QUERY:function(){return s},NEXT_URL:function(){return l},RSC_CONTENT_TYPE_HEADER:function(){return i},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",o="Next-Router-State-Tree",a="Next-Router-Prefetch",l="Next-Url",i="text/x-component",u=[[r],[o],[a]],s="_rsc",c="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2994:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return M},default:function(){return I},getServerActionDispatcher:function(){return P},urlToUrlWithoutFlightMarker:function(){return S}});let n=r(8374),o=r(326),a=n._(r(7577)),l=r(2413),i=r(7767),u=r(7584),s=r(7008),c=r(7326),d=r(9727),f=r(6199),p=r(2148),h=r(3486),m=r(8038),g=r(6265),y=r(2492),v=r(9519),b=r(5138),_=r(4237),w=r(7929),x=r(8071),R=null,E=null;function P(){return E}let j={};function S(e){let t=new URL(e,location.origin);return t.searchParams.delete(b.NEXT_RSC_UNION_QUERY),t}function O(e){return e.origin!==window.location.origin}function T(e){let{appRouterState:t,sync:r}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:o}=t,a={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,u.createHrefFromUrl)(new URL(window.location.href))!==o?(n.pendingPush=!1,window.history.pushState(a,"",o)):window.history.replaceState(a,"",o),r(t)},[t,r]),null}function M(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null}}function C(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function A(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,o=null!==n?n:r;return(0,a.useDeferredValue)(r,o)}function N(e){let t,{buildId:r,initialHead:n,initialTree:u,urlParts:d,initialSeedData:b,couldBeIntercepted:P,assetPrefix:S,missingSlots:M}=e,N=(0,a.useMemo)(()=>(0,f.createInitialRouterState)({buildId:r,initialSeedData:b,urlParts:d,initialTree:u,initialParallelRoutes:R,location:null,initialHead:n,couldBeIntercepted:P}),[r,b,d,u,n,P]),[I,D,k]=(0,c.useReducerWithReduxDevtools)(N);(0,a.useEffect)(()=>{R=null},[]);let{canonicalUrl:L}=(0,c.useUnwrapState)(I),{searchParams:F,pathname:U}=(0,a.useMemo)(()=>{let e=new URL(L,"http://n");return{searchParams:e.searchParams,pathname:(0,w.hasBasePath)(e.pathname)?(0,_.removeBasePath)(e.pathname):e.pathname}},[L]),H=(0,a.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,a.startTransition)(()=>{D({type:i.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[D]),W=(0,a.useCallback)((e,t,r)=>{let n=new URL((0,h.addBasePath)(e),location.href);return D({type:i.ACTION_NAVIGATE,url:n,isExternalUrl:O(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t})},[D]);E=(0,a.useCallback)(e=>{(0,a.startTransition)(()=>{D({...e,type:i.ACTION_SERVER_ACTION})})},[D]);let z=(0,a.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r;if(!(0,p.isBot)(window.navigator.userAgent)){try{r=new URL((0,h.addBasePath)(e),window.location.href)}catch(t){throw Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL.")}O(r)||(0,a.startTransition)(()=>{var e;D({type:i.ACTION_PREFETCH,url:r,kind:null!=(e=null==t?void 0:t.kind)?e:i.PrefetchKind.FULL})})}},replace:(e,t)=>{void 0===t&&(t={}),(0,a.startTransition)(()=>{var r;W(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,a.startTransition)(()=>{var r;W(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,a.startTransition)(()=>{D({type:i.ACTION_REFRESH,origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}}),[D,W]);(0,a.useEffect)(()=>{window.next&&(window.next.router=z)},[z]),(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(j.pendingMpaPath=void 0,D({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[D]);let{pushRef:G}=(0,c.useUnwrapState)(I);if(G.mpaNavigation){if(j.pendingMpaPath!==L){let e=window.location;G.pendingPush?e.assign(L):e.replace(L),j.pendingMpaPath=L}(0,a.use)(v.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{D({type:i.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=C(t),o&&r(o)),e(t,n,o)},window.history.replaceState=function(e,n,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=C(e),o&&r(o)),t(e,n,o)};let n=e=>{let{state:t}=e;if(t){if(!t.__NA){window.location.reload();return}(0,a.startTransition)(()=>{D({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:t.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[D]);let{cache:B,tree:K,nextUrl:V,focusAndScrollRef:X}=(0,c.useUnwrapState)(I),$=(0,a.useMemo)(()=>(0,y.findHeadInCache)(B,K[1]),[B,K]),Y=(0,a.useMemo)(()=>(function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],o=Array.isArray(t),a=o?t[1]:t;!a||a.startsWith(x.PAGE_SEGMENT_KEY)||(o&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):o&&(r[t[0]]=t[1]),r=e(n,r))}return r})(K),[K]);if(null!==$){let[e,r]=$;t=(0,o.jsx)(A,{headCacheNode:e},r)}else t=null;let q=(0,o.jsxs)(g.RedirectBoundary,{children:[t,B.rsc,(0,o.jsx)(m.AppRouterAnnouncer,{tree:K})]});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(T,{appRouterState:(0,c.useUnwrapState)(I),sync:k}),(0,o.jsx)(s.PathParamsContext.Provider,{value:Y,children:(0,o.jsx)(s.PathnameContext.Provider,{value:U,children:(0,o.jsx)(s.SearchParamsContext.Provider,{value:F,children:(0,o.jsx)(l.GlobalLayoutRouterContext.Provider,{value:{buildId:r,changeByServerResponse:H,tree:K,focusAndScrollRef:X,nextUrl:V},children:(0,o.jsx)(l.AppRouterContext.Provider,{value:z,children:(0,o.jsx)(l.LayoutRouterContext.Provider,{value:{childNodes:B.parallelRoutes,tree:K,url:L,loading:B.loading},children:q})})})})})})]})}function I(e){let{globalErrorComponent:t,...r}=e;return(0,o.jsx)(d.ErrorBoundary,{errorComponent:t,children:(0,o.jsx)(N,{...r})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return a}});let n=r(4129),o=r(5869);function a(e){let t=o.staticGenerationAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw new n.BailoutToCSRError(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return a}});let n=r(326),o=r(3325);function a(e){let{Component:t,props:r}=e;return r.searchParams=(0,o.createDynamicallyTrackedSearchParams)(r.searchParams||{}),(0,n.jsx)(t,{...r})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9727:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundary:function(){return h},ErrorBoundaryHandler:function(){return d},GlobalError:function(){return f},default:function(){return p}});let n=r(1174),o=r(326),a=n._(r(7577)),l=r(7389),i=r(7313),u=r(5869),s={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function c(e){let{error:t}=e,r=u.staticGenerationAsyncStorage.getStore();if((null==r?void 0:r.isRevalidate)||(null==r?void 0:r.isStaticGeneration))throw console.error(t),t;return null}class d extends a.default.Component{static getDerivedStateFromError(e){if((0,i.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(c,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,o.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,o.jsxs)("html",{id:"__next_error__",children:[(0,o.jsx)("head",{}),(0,o.jsxs)("body",{children:[(0,o.jsx)(c,{error:t}),(0,o.jsx)("div",{style:s.error,children:(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{style:s.text,children:"Application error: a "+(r?"server":"client")+"-side exception has occurred (see the "+(r?"server logs":"browser console")+" for more information)."}),r?(0,o.jsx)("p",{style:s.text,children:"Digest: "+r}):null]})})]})]})}let p=f;function h(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:a}=e,i=(0,l.usePathname)();return t?(0,o.jsx)(d,{pathname:i,errorComponent:t,errorStyles:r,errorScripts:n,children:a}):(0,o.jsx)(o.Fragment,{children:a})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},442:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return o}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7313:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(706),o=r(2747);function a(e){return e&&e.digest&&((0,o.isRedirectError)(e)||(0,n.isNotFoundError)(e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9671:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return R}}),r(1174);let n=r(8374),o=r(326),a=n._(r(7577));r(962);let l=r(2413),i=r(9009),u=r(9519),s=r(9727),c=r(455),d=r(9976),f=r(6265),p=r(1868),h=r(2162),m=r(9886),g=r(5262),y=["bottom","height","left","right","top","width","x","y"];function v(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class b extends a.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,c.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),!r&&(r=null),!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return y.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,d.handleSmoothScroll)(()=>{if(n){r.scrollIntoView();return}let e=document.documentElement,t=e.clientHeight;!v(r,t)&&(e.scrollTop=0,v(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function _(e){let{segmentPath:t,children:r}=e,n=(0,a.useContext)(l.GlobalLayoutRouterContext);if(!n)throw Error("invariant global layout router not mounted");return(0,o.jsx)(b,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef,children:r})}function w(e){let{parallelRouterKey:t,url:r,childNodes:n,segmentPath:s,tree:d,cacheKey:f}=e,p=(0,a.useContext)(l.GlobalLayoutRouterContext);if(!p)throw Error("invariant global layout router not mounted");let{buildId:h,changeByServerResponse:m,tree:y}=p,v=n.get(f);if(void 0===v){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};v=e,n.set(f,e)}let b=null!==v.prefetchRsc?v.prefetchRsc:v.rsc,_=(0,a.useDeferredValue)(v.rsc,b),w="object"==typeof _&&null!==_&&"function"==typeof _.then?(0,a.use)(_):_;if(!w){let e=v.lazyData;if(null===e){let t=function e(t,r){if(t){let[n,o]=t,a=2===t.length;if((0,c.matchSegment)(r[0],n)&&r[1].hasOwnProperty(o)){if(a){let t=e(void 0,r[1][o]);return[r[0],{...r[1],[o]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[o]:e(t.slice(2),r[1][o])}]}}return r}(["",...s],y),n=(0,g.hasInterceptionRouteInCurrentTree)(y);v.lazyData=e=(0,i.fetchServerResponse)(new URL(r,location.origin),t,n?p.nextUrl:null,h),v.lazyDataResolved=!1}let t=(0,a.use)(e);v.lazyDataResolved||(setTimeout(()=>{(0,a.startTransition)(()=>{m({previousTree:y,serverResponse:t})})}),v.lazyDataResolved=!0),(0,a.use)(u.unresolvedThenable)}return(0,o.jsx)(l.LayoutRouterContext.Provider,{value:{tree:d[1][t],childNodes:v.parallelRoutes,url:r,loading:v.loading},children:w})}function x(e){let{children:t,hasLoading:r,loading:n,loadingStyles:l,loadingScripts:i}=e;return r?(0,o.jsx)(a.Suspense,{fallback:(0,o.jsxs)(o.Fragment,{children:[l,i,n]}),children:t}):(0,o.jsx)(o.Fragment,{children:t})}function R(e){let{parallelRouterKey:t,segmentPath:r,error:n,errorStyles:i,errorScripts:u,templateStyles:c,templateScripts:d,template:g,notFound:y,notFoundStyles:v}=e,b=(0,a.useContext)(l.LayoutRouterContext);if(!b)throw Error("invariant expected layout router to be mounted");let{childNodes:R,tree:E,url:P,loading:j}=b,S=R.get(t);S||(S=new Map,R.set(t,S));let O=E[1][t][0],T=(0,h.getSegmentValue)(O),M=[O];return(0,o.jsx)(o.Fragment,{children:M.map(e=>{let a=(0,h.getSegmentValue)(e),b=(0,m.createRouterCacheKey)(e);return(0,o.jsxs)(l.TemplateContext.Provider,{value:(0,o.jsx)(_,{segmentPath:r,children:(0,o.jsx)(s.ErrorBoundary,{errorComponent:n,errorStyles:i,errorScripts:u,children:(0,o.jsx)(x,{hasLoading:!!j,loading:null==j?void 0:j[0],loadingStyles:null==j?void 0:j[1],loadingScripts:null==j?void 0:j[2],children:(0,o.jsx)(p.NotFoundBoundary,{notFound:y,notFoundStyles:v,children:(0,o.jsx)(f.RedirectBoundary,{children:(0,o.jsx)(w,{parallelRouterKey:t,url:P,tree:E,childNodes:S,segmentPath:r,cacheKey:b,isActive:T===a})})})})})}),children:[c,d,g]},(0,m.createRouterCacheKey)(e,!0))})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},455:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{canSegmentBeOverridden:function(){return a},matchSegment:function(){return o}});let n=r(2357),o=(e,t)=>"string"==typeof e?"string"==typeof t&&e===t:"string"!=typeof t&&e[0]===t[0]&&e[1]===t[1],a=(e,t)=>{var r;return!Array.isArray(e)&&!!Array.isArray(t)&&(null==(r=(0,n.getSegmentParam)(e))?void 0:r.param)===t[0]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7389:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return u.ReadonlyURLSearchParams},RedirectType:function(){return u.RedirectType},ServerInsertedHTMLContext:function(){return s.ServerInsertedHTMLContext},notFound:function(){return u.notFound},permanentRedirect:function(){return u.permanentRedirect},redirect:function(){return u.redirect},useParams:function(){return p},usePathname:function(){return d},useRouter:function(){return f},useSearchParams:function(){return c},useSelectedLayoutSegment:function(){return m},useSelectedLayoutSegments:function(){return h},useServerInsertedHTML:function(){return s.useServerInsertedHTML}});let n=r(7577),o=r(2413),a=r(7008),l=r(2162),i=r(8071),u=r(7375),s=r(3347);function c(){let e=(0,n.useContext)(a.SearchParamsContext),t=(0,n.useMemo)(()=>e?new u.ReadonlyURLSearchParams(e):null,[e]);{let{bailoutToClientRendering:e}=r(6136);e("useSearchParams()")}return t}function d(){return(0,n.useContext)(a.PathnameContext)}function f(){let e=(0,n.useContext)(o.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function p(){return(0,n.useContext)(a.PathParamsContext)}function h(e){void 0===e&&(e="children");let t=(0,n.useContext)(o.LayoutRouterContext);return t?function e(t,r,n,o){let a;if(void 0===n&&(n=!0),void 0===o&&(o=[]),n)a=t[1][r];else{var u;let e=t[1];a=null!=(u=e.children)?u:Object.values(e)[0]}if(!a)return o;let s=a[0],c=(0,l.getSegmentValue)(s);return!c||c.startsWith(i.PAGE_SEGMENT_KEY)?o:(o.push(c),e(a,r,!1,o))}(t.tree,e):null}function m(e){void 0===e&&(e="children");let t=h(e);if(!t||0===t.length)return null;let r="children"===e?t[0]:t[t.length-1];return r===i.DEFAULT_SEGMENT_KEY?null:r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7375:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l},RedirectType:function(){return n.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(2747),o=r(706);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class l extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1868:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotFoundBoundary",{enumerable:!0,get:function(){return c}});let n=r(8374),o=r(326),a=n._(r(7577)),l=r(7389),i=r(706);r(576);let u=r(2413);class s extends a.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,i.isNotFoundError)(e))return{notFoundTriggered:!0};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.notFoundTriggered?{notFoundTriggered:!1,previousPathname:e.pathname}:{notFoundTriggered:t.notFoundTriggered,previousPathname:e.pathname}}render(){return this.state.notFoundTriggered?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("meta",{name:"robots",content:"noindex"}),!1,this.props.notFoundStyles,this.props.notFound]}):this.props.children}constructor(e){super(e),this.state={notFoundTriggered:!!e.asNotFound,previousPathname:e.pathname}}}function c(e){let{notFound:t,notFoundStyles:r,asNotFound:n,children:i}=e,c=(0,l.usePathname)(),d=(0,a.useContext)(u.MissingSlotContext);return t?(0,o.jsx)(s,{pathname:c,notFound:t,notFoundStyles:r,asNotFound:n,missingSlots:d,children:i}):(0,o.jsx)(o.Fragment,{children:i})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},706:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7815:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return s}});let n=r(8285),o=r(8817);var a=o._("_maxConcurrency"),l=o._("_runningCount"),i=o._("_queue"),u=o._("_processNext");class s{enqueue(e){let t,r;let o=new Promise((e,n)=>{t=e,r=n}),a=async()=>{try{n._(this,l)[l]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,l)[l]--,n._(this,u)[u]()}};return n._(this,i)[i].push({promiseFn:o,task:a}),n._(this,u)[u](),o}bump(e){let t=n._(this,i)[i].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,i)[i].splice(t,1)[0];n._(this,i)[i].unshift(e),n._(this,u)[u](!0)}}constructor(e=5){Object.defineProperty(this,u,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,l,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),n._(this,a)[a]=e,n._(this,l)[l]=0,n._(this,i)[i]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,l)[l]<n._(this,a)[a]||e)&&n._(this,i)[i].length>0){var t;null==(t=n._(this,i)[i].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6265:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectBoundary:function(){return c},RedirectErrorBoundary:function(){return s}});let n=r(8374),o=r(326),a=n._(r(7577)),l=r(7389),i=r(2747);function u(e){let{redirect:t,reset:r,redirectType:n}=e,o=(0,l.useRouter)();return(0,a.useEffect)(()=>{a.default.startTransition(()=>{n===i.RedirectType.push?o.push(t,{}):o.replace(t,{}),r()})},[t,n,r,o]),null}class s extends a.default.Component{static getDerivedStateFromError(e){if((0,i.isRedirectError)(e))return{redirect:(0,i.getURLFromRedirectError)(e),redirectType:(0,i.getRedirectTypeFromError)(e)};throw e}render(){let{redirect:e,redirectType:t}=this.state;return null!==e&&null!==t?(0,o.jsx)(u,{redirect:e,redirectType:t,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(e){super(e),this.state={redirect:null,redirectType:null}}}function c(e){let{children:t}=e,r=(0,l.useRouter)();return(0,o.jsx)(s,{router:r,children:t})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8778:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2747:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return u},getRedirectStatusCodeFromError:function(){return h},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return f},isRedirectError:function(){return d},permanentRedirect:function(){return c},redirect:function(){return s}});let o=r(4580),a=r(2934),l=r(8778),i="NEXT_REDIRECT";function u(e,t,r){void 0===r&&(r=l.RedirectStatusCode.TemporaryRedirect);let n=Error(i);n.digest=i+";"+t+";"+e+";"+r+";";let a=o.requestAsyncStorage.getStore();return a&&(n.mutableCookies=a.mutableCookies),n}function s(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?l.RedirectStatusCode.SeeOther:l.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?l.RedirectStatusCode.SeeOther:l.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,o]=e.digest.split(";",4),a=Number(o);return t===i&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(a)&&a in l.RedirectStatusCode}function f(e){return d(e)?e.digest.split(";",3)[2]:null}function p(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function h(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(8374),o=r(326),a=n._(r(7577)),l=r(2413);function i(){let e=(0,a.useContext)(l.TemplateContext);return(0,o.jsx)(o.Fragment,{children:e})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9894:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let n=r(114),o=r(9056);function a(e,t,r,a){let[l,i,u]=r.slice(-3);if(null===i)return!1;if(3===r.length){let r=i[2],o=i[3];t.loading=o,t.rsc=r,t.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(t,e,l,i,u,a)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,o.fillCacheWithNewSubTreeData)(t,e,r,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,i){let u;let[s,c,d,f,p]=r;if(1===t.length){let e=l(r,n,t);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,i),e}let[h,m]=t;if(!(0,o.matchSegment)(h,s))return null;if(2===t.length)u=l(c[m],n,t);else if(null===(u=e(t.slice(2),c[m],n,i)))return null;let g=[t[0],{...c,[m]:u},d,f];return p&&(g[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(g,i),g}}});let n=r(8071),o=r(455),a=r(4158);function l(e,t,r){let[a,i]=e,[u,s]=t;if(u===n.DEFAULT_SEGMENT_KEY&&a!==n.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(a,u)){let t={};for(let e in i)void 0!==s[e]?t[e]=l(i[e],s[e],r):t[e]=i[e];for(let e in s)t[e]||(t[e]=s[e]);let n=[a,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2895:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[l,i]=o,u=(0,n.createRouterCacheKey)(i),s=r.parallelRoutes.get(l),c=t.parallelRoutes.get(l);c&&c!==s||(c=new Map(s),t.parallelRoutes.set(l,c));let d=null==s?void 0:s.get(u),f=c.get(u);if(a){f&&f.lazyData&&f!==d||c.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}if(!f||!d){f||c.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}return f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),lazyDataResolved:f.lazyDataResolved,loading:f.loading},c.set(u,f)),e(f,d,o.slice(2))}}});let n=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3648:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return s}});let n=r(7356),o=r(8071),a=r(455),l=e=>"/"===e[0]?e.slice(1):e,i=e=>"string"==typeof e?"children"===e?"":e:e[1];function u(e){return e.reduce((e,t)=>""===(t=l(t))||(0,o.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function s(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===o.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(o.PAGE_SEGMENT_KEY))return"";let a=[i(r)],l=null!=(t=e[1])?t:{},c=l.children?s(l.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(l)){if("children"===e)continue;let r=s(t);void 0!==r&&a.push(r)}return u(a)}function c(e,t){let r=function e(t,r){let[o,l]=t,[u,c]=r,d=i(o),f=i(u);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,a.matchSegment)(o,u)){var p;return null!=(p=s(r))?p:""}for(let t in l)if(c[t]){let r=e(l[t],c[t]);if(null!==r)return i(u)+"/"+r}return null}(e,t);return null==r||"/"===r?r:u(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7584:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6199:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInitialRouterState",{enumerable:!0,get:function(){return s}});let n=r(7584),o=r(114),a=r(3648),l=r(9373),i=r(7767),u=r(4158);function s(e){var t;let{buildId:r,initialTree:s,initialSeedData:c,urlParts:d,initialParallelRoutes:f,location:p,initialHead:h,couldBeIntercepted:m}=e,g=d.join("/"),y=!p,v={lazyData:null,rsc:c[2],prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:y?new Map:f,lazyDataResolved:!1,loading:c[3]},b=p?(0,n.createHrefFromUrl)(p):g;(0,u.addRefreshMarkerToActiveParallelSegments)(s,b);let _=new Map;(null===f||0===f.size)&&(0,o.fillLazyItemsTillLeafWithHead)(v,void 0,s,c,h);let w={buildId:r,tree:s,cache:v,prefetchCache:_,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:b,nextUrl:null!=(t=(0,a.extractPathFromFlightRouterState)(s)||(null==p?void 0:p.pathname))?t:null};if(p){let e=new URL(""+p.pathname+p.search,p.origin),t=[["",s,null,null]];(0,l.createPrefetchCacheEntryForInitialLoad)({url:e,kind:i.PrefetchKind.AUTO,data:[t,void 0,!1,m],tree:w.tree,prefetchCache:w.prefetchCache,nextUrl:w.nextUrl})}return w}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9886:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return o}});let n=r(8071);function o(e,t){return(void 0===t&&(t=!1),Array.isArray(e))?e[0]+"|"+e[1]+"|"+e[2]:t&&e.startsWith(n.PAGE_SEGMENT_KEY)?n.PAGE_SEGMENT_KEY:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9009:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fetchServerResponse",{enumerable:!0,get:function(){return c}});let n=r(5138),o=r(2994),a=r(5424),l=r(7767),i=r(2165),{createFromFetch:u}=r(6493);function s(e){return[(0,o.urlToUrlWithoutFlightMarker)(e).toString(),void 0,!1,!1]}async function c(e,t,r,c,d){let f={[n.RSC_HEADER]:"1",[n.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(t))};d===l.PrefetchKind.AUTO&&(f[n.NEXT_ROUTER_PREFETCH_HEADER]="1"),r&&(f[n.NEXT_URL]=r);let p=(0,i.hexHash)([f[n.NEXT_ROUTER_PREFETCH_HEADER]||"0",f[n.NEXT_ROUTER_STATE_TREE],f[n.NEXT_URL]].join(","));try{var h;let t=new URL(e);t.searchParams.set(n.NEXT_RSC_UNION_QUERY,p);let r=await fetch(t,{credentials:"same-origin",headers:f}),l=(0,o.urlToUrlWithoutFlightMarker)(r.url),i=r.redirected?l:void 0,d=r.headers.get("content-type")||"",m=!!r.headers.get(n.NEXT_DID_POSTPONE_HEADER),g=!!(null==(h=r.headers.get("vary"))?void 0:h.includes(n.NEXT_URL));if(d!==n.RSC_CONTENT_TYPE_HEADER||!r.ok)return e.hash&&(l.hash=e.hash),s(l.toString());let[y,v]=await u(Promise.resolve(r),{callServer:a.callServer});if(c!==y)return s(r.url);return[v,i,m,g]}catch(t){return console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",t),[e.toString(),void 0,!1,!1]}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9056:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return function e(t,r,l,i){let u=l.length<=5,[s,c]=l,d=(0,a.createRouterCacheKey)(c),f=r.parallelRoutes.get(s);if(!f)return;let p=t.parallelRoutes.get(s);p&&p!==f||(p=new Map(f),t.parallelRoutes.set(s,p));let h=f.get(d),m=p.get(d);if(u){if(!m||!m.lazyData||m===h){let e=l[3];m={lazyData:null,rsc:e[2],prefetchRsc:null,head:null,prefetchHead:null,loading:e[3],parallelRoutes:h?new Map(h.parallelRoutes):new Map,lazyDataResolved:!1},h&&(0,n.invalidateCacheByRouterState)(m,h,l[2]),(0,o.fillLazyItemsTillLeafWithHead)(m,h,l[2],e,l[4],i),p.set(d,m)}return}m&&h&&(m===h&&(m={lazyData:m.lazyData,rsc:m.rsc,prefetchRsc:m.prefetchRsc,head:m.head,prefetchHead:m.prefetchHead,parallelRoutes:new Map(m.parallelRoutes),lazyDataResolved:!1,loading:m.loading},p.set(d,m)),e(m,h,l.slice(2),i))}}});let n=r(2498),o=r(114),a=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,a,l,i,u){if(0===Object.keys(a[1]).length){t.head=i;return}for(let s in a[1]){let c;let d=a[1][s],f=d[0],p=(0,n.createRouterCacheKey)(f),h=null!==l&&void 0!==l[1][s]?l[1][s]:null;if(r){let n=r.parallelRoutes.get(s);if(n){let r;let a=(null==u?void 0:u.kind)==="auto"&&u.status===o.PrefetchCacheEntryStatus.reusable,l=new Map(n),c=l.get(p);r=null!==h?{lazyData:null,rsc:h[2],prefetchRsc:null,head:null,prefetchHead:null,loading:h[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),lazyDataResolved:!1}:a&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),lazyDataResolved:c.lazyDataResolved,loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),lazyDataResolved:!1,loading:null},l.set(p,r),e(r,c,d,h||null,i,u),t.parallelRoutes.set(s,l);continue}}if(null!==h){let e=h[2],t=h[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};let m=t.parallelRoutes.get(s);m?m.set(p,c):t.parallelRoutes.set(s,new Map([[p,c]])),e(c,void 0,d,h,i,u)}}}});let n=r(9886),o=r(7767);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7252:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let n=r(3648);function o(e){return void 0!==e}function a(e,t){var r,a,l;let i=null==(a=t.shouldScroll)||a,u=e.nextUrl;if(o(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?u=r:u||(u=e.canonicalUrl)}return{buildId:e.buildId,canonicalUrl:o(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:o(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:o(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:o(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!i&&(!!o(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:!!t.hashFragment&&e.canonicalUrl.split("#",1)[0]===(null==(r=t.canonicalUrl)?void 0:r.split("#",1)[0]),hashFragment:i?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:i?null!=(l=null==t?void 0:t.scrollableSegments)?l:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:o(t.patchedTree)?t.patchedTree:e.tree,nextUrl:u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5652:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return o}});let n=r(5861);function o(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[l,i]=o,u=(0,n.createRouterCacheKey)(i),s=r.parallelRoutes.get(l);if(!s)return;let c=t.parallelRoutes.get(l);if(c&&c!==s||(c=new Map(s),t.parallelRoutes.set(l,c)),a){c.delete(u);return}let d=s.get(u),f=c.get(u);f&&d&&(f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),lazyDataResolved:f.lazyDataResolved},c.set(u,f)),e(f,d,o.slice(2)))}}});let n=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2498:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return o}});let n=r(9886);function o(e,t,r){for(let o in r[1]){let a=r[1][o][0],l=(0,n.createRouterCacheKey)(a),i=t.parallelRoutes.get(o);if(i){let t=new Map(i);t.delete(l),e.parallelRoutes.set(o,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3772:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],o=r[0];if(Array.isArray(n)&&Array.isArray(o)){if(n[0]!==o[0]||n[2]!==o[2])return!0}else if(n!==o)return!0;if(t[4])return!r[4];if(r[4])return!0;let a=Object.values(t[1])[0],l=Object.values(r[1])[0];return!a||!l||e(a,l)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8831:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return s},listenForDynamicRequest:function(){return i},updateCacheNodeOnNavigation:function(){return function e(t,r,i,s,c){let d=r[1],f=i[1],p=s[1],h=t.parallelRoutes,m=new Map(h),g={},y=null;for(let t in f){let r;let i=f[t],s=d[t],v=h.get(t),b=p[t],_=i[0],w=(0,a.createRouterCacheKey)(_),x=void 0!==s?s[0]:void 0,R=void 0!==v?v.get(w):void 0;if(null!==(r=_===n.PAGE_SEGMENT_KEY?l(i,void 0!==b?b:null,c):_===n.DEFAULT_SEGMENT_KEY?void 0!==s?{route:s,node:null,children:null}:l(i,void 0!==b?b:null,c):void 0!==x&&(0,o.matchSegment)(_,x)&&void 0!==R&&void 0!==s?null!=b?e(R,s,i,b,c):function(e){let t=u(e,null,null);return{route:e,node:t,children:null}}(i):l(i,void 0!==b?b:null,c))){null===y&&(y=new Map),y.set(t,r);let e=r.node;if(null!==e){let r=new Map(v);r.set(w,e),m.set(t,r)}g[t]=r.route}else g[t]=i}if(null===y)return null;let v={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,loading:t.loading,parallelRoutes:m,lazyDataResolved:!1};return{route:function(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}(i,g),node:v,children:y}}},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],o=t.parallelRoutes,l=new Map(o);for(let t in n){let r=n[t],i=r[0],u=(0,a.createRouterCacheKey)(i),s=o.get(t);if(void 0!==s){let n=s.get(u);if(void 0!==n){let o=e(n,r),a=new Map(s);a.set(u,o),l.set(t,a)}}}let i=t.rsc,u=f(i)&&"pending"===i.status;return{lazyData:null,rsc:i,head:t.head,prefetchHead:u?t.prefetchHead:null,prefetchRsc:u?t.prefetchRsc:null,loading:u?t.loading:null,parallelRoutes:l,lazyDataResolved:!1}}}});let n=r(8071),o=r(455),a=r(9886);function l(e,t,r){let n=u(e,t,r);return{route:e,node:n,children:null}}function i(e,t){t.then(t=>{for(let r of t[0]){let t=r.slice(0,-3),n=r[r.length-3],l=r[r.length-2],i=r[r.length-1];"string"!=typeof t&&function(e,t,r,n,l){let i=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],a=i.children;if(null!==a){let e=a.get(r);if(void 0!==e){let t=e.route[0];if((0,o.matchSegment)(n,t)){i=e;continue}}}return}(function e(t,r,n,l){let i=t.children,u=t.node;if(null===i){null!==u&&(function e(t,r,n,l,i){let u=r[1],s=n[1],d=l[1],p=t.parallelRoutes;for(let t in u){let r=u[t],n=s[t],l=d[t],f=p.get(t),h=r[0],m=(0,a.createRouterCacheKey)(h),g=void 0!==f?f.get(m):void 0;void 0!==g&&(void 0!==n&&(0,o.matchSegment)(h,n[0])&&null!=l?e(g,r,n,l,i):c(r,g,null))}let h=t.rsc,m=l[2];null===h?t.rsc=m:f(h)&&h.resolve(m);let g=t.head;f(g)&&g.resolve(i)}(u,t.route,r,n,l),t.node=null);return}let s=r[1],d=n[1];for(let t in r){let r=s[t],n=d[t],a=i.get(t);if(void 0!==a){let t=a.route[0];if((0,o.matchSegment)(r[0],t)&&null!=n)return e(a,r,n,l)}}})(i,r,n,l)}(e,t,n,l,i)}s(e,null)},t=>{s(e,t)})}function u(e,t,r){let n=e[1],o=null!==t?t[1]:null,l=new Map;for(let e in n){let t=n[e],i=null!==o?o[e]:null,s=t[0],c=(0,a.createRouterCacheKey)(s),d=u(t,void 0===i?null:i,r),f=new Map;f.set(c,d),l.set(e,f)}let i=0===l.size,s=null!==t?t[2]:null,c=null!==t?t[3]:null;return{lazyData:null,parallelRoutes:l,prefetchRsc:void 0!==s?s:null,prefetchHead:i?r:null,loading:void 0!==c?c:null,rsc:p(),head:i?p():null,lazyDataResolved:!1}}function s(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)c(e.route,r,t);else for(let e of n.values())s(e,t);e.node=null}function c(e,t,r){let n=e[1],o=t.parallelRoutes;for(let e in n){let t=n[e],l=o.get(e);if(void 0===l)continue;let i=t[0],u=(0,a.createRouterCacheKey)(i),s=l.get(u);void 0!==s&&c(t,s,r)}let l=t.rsc;f(l)&&(null===r?l.resolve(null):l.reject(r));let i=t.head;f(i)&&i.resolve(null)}let d=Symbol();function f(e){return e&&e.tag===d}function p(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=d,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9373:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createPrefetchCacheEntryForInitialLoad:function(){return s},getOrCreatePrefetchCacheEntry:function(){return u},prunePrefetchCache:function(){return d}});let n=r(7584),o=r(9009),a=r(7767),l=r(1156);function i(e,t){let r=(0,n.createHrefFromUrl)(e,!1);return t?t+"%"+r:r}function u(e){let t,{url:r,nextUrl:n,tree:o,buildId:l,prefetchCache:u,kind:s}=e,d=i(r,n),f=u.get(d);if(f)t=f;else{let e=i(r),n=u.get(e);n&&(t=n)}return t?(t.status=h(t),t.kind!==a.PrefetchKind.FULL&&s===a.PrefetchKind.FULL)?c({tree:o,url:r,buildId:l,nextUrl:n,prefetchCache:u,kind:null!=s?s:a.PrefetchKind.TEMPORARY}):(s&&t.kind===a.PrefetchKind.TEMPORARY&&(t.kind=s),t):c({tree:o,url:r,buildId:l,nextUrl:n,prefetchCache:u,kind:s||a.PrefetchKind.TEMPORARY})}function s(e){let{nextUrl:t,tree:r,prefetchCache:n,url:o,kind:l,data:u}=e,[,,,s]=u,c=s?i(o,t):i(o),d={treeAtTimeOfPrefetch:r,data:Promise.resolve(u),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),key:c,status:a.PrefetchCacheEntryStatus.fresh};return n.set(c,d),d}function c(e){let{url:t,kind:r,tree:n,nextUrl:u,buildId:s,prefetchCache:c}=e,d=i(t),f=l.prefetchQueue.enqueue(()=>(0,o.fetchServerResponse)(t,n,u,s,r).then(e=>{let[,,,r]=e;return r&&function(e){let{url:t,nextUrl:r,prefetchCache:n}=e,o=i(t),a=n.get(o);if(!a)return;let l=i(t,r);n.set(l,a),n.delete(o)}({url:t,nextUrl:u,prefetchCache:c}),e})),p={treeAtTimeOfPrefetch:n,data:f,kind:r,prefetchTime:Date.now(),lastUsedTime:null,key:d,status:a.PrefetchCacheEntryStatus.fresh};return c.set(d,p),p}function d(e){for(let[t,r]of e)h(r)===a.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("30"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:r,lastUsedTime:n}=e;return Date.now()<(null!=n?n:r)+f?n?a.PrefetchCacheEntryStatus.reusable:a.PrefetchCacheEntryStatus.fresh:"auto"===t&&Date.now()<r+p?a.PrefetchCacheEntryStatus.stale:"full"===t&&Date.now()<r+p?a.PrefetchCacheEntryStatus.reusable:a.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5703:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fastRefreshReducer",{enumerable:!0,get:function(){return n}}),r(9009),r(7584),r(5166),r(3772),r(5861),r(7252),r(9894),r(2994),r(5652),r(5262);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2492:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return o}});let n=r(9886);function o(e,t){return function e(t,r,o){if(0===Object.keys(r).length)return[t,o];for(let a in r){let[l,i]=r[a],u=t.parallelRoutes.get(a);if(!u)continue;let s=(0,n.createRouterCacheKey)(l),c=u.get(s);if(!c)continue;let d=e(c,i,o+"/"+s);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2162:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5262:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasInterceptionRouteInCurrentTree",{enumerable:!0,get:function(){return function e(t){let[r,o]=t;if(Array.isArray(r)&&("di"===r[2]||"ci"===r[2])||"string"==typeof r&&(0,n.isInterceptionRouteAppPath)(r))return!0;if(o){for(let t in o)if(e(o[t]))return!0}return!1}}});let n=r(7356);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5861:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return g},navigateReducer:function(){return v}}),r(9009);let n=r(7584),o=r(3193),a=r(5166),l=r(4614),i=r(3772),u=r(7767),s=r(7252),c=r(9894),d=r(1156),f=r(2994),p=r(8071),h=(r(8831),r(9373)),m=r(2895);function g(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,s.handleMutable)(e,t)}function y(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,o]of Object.entries(n))for(let n of y(o))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}let v=function(e,t){let{url:r,isExternalUrl:v,navigateType:b,shouldScroll:_}=t,w={},{hash:x}=r,R=(0,n.createHrefFromUrl)(r),E="push"===b;if((0,h.prunePrefetchCache)(e.prefetchCache),w.preserveCustomHistoryState=!1,v)return g(e,w,r.toString(),E);let P=(0,h.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,tree:e.tree,buildId:e.buildId,prefetchCache:e.prefetchCache}),{treeAtTimeOfPrefetch:j,data:S}=P;return d.prefetchQueue.bump(S),S.then(t=>{let[r,d]=t,h=!1;if(P.lastUsedTime||(P.lastUsedTime=Date.now(),h=!0),"string"==typeof r)return g(e,w,r,E);if(document.getElementById("__next-page-redirect"))return g(e,w,R,E);let v=e.tree,b=e.cache,S=[];for(let t of r){let r=t.slice(0,-4),n=t.slice(-3)[0],s=["",...r],d=(0,a.applyRouterStatePatchToTree)(s,v,n,R);if(null===d&&(d=(0,a.applyRouterStatePatchToTree)(s,j,n,R)),null!==d){if((0,i.isNavigatingToNewRootLayout)(v,d))return g(e,w,R,E);let a=(0,f.createEmptyCacheNode)(),_=!1;for(let e of(P.status!==u.PrefetchCacheEntryStatus.stale||h?_=(0,c.applyFlightData)(b,a,t,P):(_=function(e,t,r,n){let o=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),y(n).map(e=>[...r,...e])))(0,m.clearCacheNodeDataForSegmentPath)(e,t,a),o=!0;return o}(a,b,r,n),P.lastUsedTime=Date.now()),(0,l.shouldHardNavigate)(s,v)?(a.rsc=b.rsc,a.prefetchRsc=b.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(a,b,r),w.cache=a):_&&(w.cache=a,b=a),v=d,y(n))){let t=[...r,...e];t[t.length-1]!==p.DEFAULT_SEGMENT_KEY&&S.push(t)}}}return w.patchedTree=v,w.canonicalUrl=d?(0,n.createHrefFromUrl)(d):R,w.pendingPush=E,w.scrollableSegments=S,w.hashFragment=x,w.shouldScroll=_,(0,s.handleMutable)(e,w)},()=>e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1156:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return l},prefetchReducer:function(){return i}});let n=r(5138),o=r(7815),a=r(9373),l=new o.PromiseQueue(5);function i(e,t){(0,a.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return r.searchParams.delete(n.NEXT_RSC_UNION_QUERY),(0,a.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,buildId:e.buildId}),e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9809:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let n=r(9009),o=r(7584),a=r(5166),l=r(3772),i=r(5861),u=r(7252),s=r(114),c=r(2994),d=r(5652),f=r(5262),p=r(4158);function h(e,t){let{origin:r}=t,h={},m=e.canonicalUrl,g=e.tree;h.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);return y.lazyData=(0,n.fetchServerResponse)(new URL(m,r),[g[0],g[1],g[2],"refetch"],v?e.nextUrl:null,e.buildId),y.lazyData.then(async r=>{let[n,c]=r;if("string"==typeof n)return(0,i.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);for(let r of(y.lazyData=null,n)){if(3!==r.length)return console.log("REFRESH FAILED"),e;let[n]=r,u=(0,a.applyRouterStatePatchToTree)([""],g,n,e.canonicalUrl);if(null===u)return(0,d.handleSegmentMismatch)(e,t,n);if((0,l.isNavigatingToNewRootLayout)(g,u))return(0,i.handleExternalUrl)(e,h,m,e.pushRef.pendingPush);let f=c?(0,o.createHrefFromUrl)(c):void 0;c&&(h.canonicalUrl=f);let[b,_]=r.slice(-2);if(null!==b){let e=b[2];y.rsc=e,y.prefetchRsc=null,(0,s.fillLazyItemsTillLeafWithHead)(y,void 0,n,b,_),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({state:e,updatedTree:u,updatedCache:y,includeNextUrl:v,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=y,h.patchedTree=u,h.canonicalUrl=m,g=u}return(0,u.handleMutable)(e,h)},()=>e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5608:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let n=r(7584),o=r(3648);function a(e,t){var r;let{url:a,tree:l}=t,i=(0,n.createHrefFromUrl)(a),u=l||e.tree,s=e.cache;return{buildId:e.buildId,canonicalUrl:i,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:s,prefetchCache:e.prefetchCache,tree:u,nextUrl:null!=(r=(0,o.extractPathFromFlightRouterState)(u))?r:a.pathname}}r(8831),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5240:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return b}});let n=r(5424),o=r(5138),a=r(3486),l=r(7584),i=r(5861),u=r(5166),s=r(3772),c=r(7252),d=r(114),f=r(2994),p=r(5262),h=r(5652),m=r(4158),{createFromFetch:g,encodeReply:y}=r(6493);async function v(e,t,r){let l,{actionId:i,actionArgs:u}=r,s=await y(u),c=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION]:i,[o.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[o.NEXT_URL]:t}:{}},body:s}),d=c.headers.get("x-action-redirect");try{let e=JSON.parse(c.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let f=d?new URL((0,a.addBasePath)(d),new URL(e.canonicalUrl,window.location.href)):void 0;if(c.headers.get("content-type")===o.RSC_CONTENT_TYPE_HEADER){let e=await g(Promise.resolve(c),{callServer:n.callServer});if(d){let[,t]=null!=e?e:[];return{actionFlightData:t,redirectLocation:f,revalidatedParts:l}}let[t,[,r]]=null!=e?e:[];return{actionResult:t,actionFlightData:r,redirectLocation:f,revalidatedParts:l}}return{redirectLocation:f,revalidatedParts:l}}function b(e,t){let{resolve:r,reject:n}=t,o={},a=e.canonicalUrl,g=e.tree;o.preserveCustomHistoryState=!1;let y=e.nextUrl&&(0,p.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return o.inFlightServerAction=v(e,y,t),o.inFlightServerAction.then(async n=>{let{actionResult:p,actionFlightData:v,redirectLocation:b}=n;if(b&&(e.pushRef.pendingPush=!0,o.pendingPush=!0),!v)return(r(p),b)?(0,i.handleExternalUrl)(e,o,b.href,e.pushRef.pendingPush):e;if("string"==typeof v)return(0,i.handleExternalUrl)(e,o,v,e.pushRef.pendingPush);if(o.inFlightServerAction=null,b){let e=(0,l.createHrefFromUrl)(b,!1);o.canonicalUrl=e}for(let r of v){if(3!==r.length)return console.log("SERVER ACTION APPLY FAILED"),e;let[n]=r,c=(0,u.applyRouterStatePatchToTree)([""],g,n,b?(0,l.createHrefFromUrl)(b):e.canonicalUrl);if(null===c)return(0,h.handleSegmentMismatch)(e,t,n);if((0,s.isNavigatingToNewRootLayout)(g,c))return(0,i.handleExternalUrl)(e,o,a,e.pushRef.pendingPush);let[p,v]=r.slice(-2),_=null!==p?p[2]:null;if(null!==_){let t=(0,f.createEmptyCacheNode)();t.rsc=_,t.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(t,void 0,n,p,v),await (0,m.refreshInactiveParallelSegments)({state:e,updatedTree:c,updatedCache:t,includeNextUrl:!!y,canonicalUrl:o.canonicalUrl||e.canonicalUrl}),o.cache=t,o.prefetchCache=new Map}o.patchedTree=c,g=c}return r(p),(0,c.handleMutable)(e,o)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4025:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return d}});let n=r(7584),o=r(5166),a=r(3772),l=r(5861),i=r(9894),u=r(7252),s=r(2994),c=r(5652);function d(e,t){let{serverResponse:r}=t,[d,f]=r,p={};if(p.preserveCustomHistoryState=!1,"string"==typeof d)return(0,l.handleExternalUrl)(e,p,d,e.pushRef.pendingPush);let h=e.tree,m=e.cache;for(let r of d){let u=r.slice(0,-4),[d]=r.slice(-3,-2),g=(0,o.applyRouterStatePatchToTree)(["",...u],h,d,e.canonicalUrl);if(null===g)return(0,c.handleSegmentMismatch)(e,t,d);if((0,a.isNavigatingToNewRootLayout)(h,g))return(0,l.handleExternalUrl)(e,p,e.canonicalUrl,e.pushRef.pendingPush);let y=f?(0,n.createHrefFromUrl)(f):void 0;y&&(p.canonicalUrl=y);let v=(0,s.createEmptyCacheNode)();(0,i.applyFlightData)(m,v,r),p.patchedTree=g,p.cache=v,m=v,h=g}return(0,u.handleMutable)(e,p)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4158:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,o,,l]=t;for(let i in n.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==l&&(t[2]=r,t[3]="refresh"),o)e(o[i],r)}},refreshInactiveParallelSegments:function(){return l}});let n=r(9894),o=r(9009),a=r(8071);async function l(e){let t=new Set;await i({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function i(e){let{state:t,updatedTree:r,updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:s=r,canonicalUrl:c}=e,[,d,f,p]=r,h=[];if(f&&f!==c&&"refresh"===p&&!u.has(f)){u.add(f);let e=(0,o.fetchServerResponse)(new URL(f,location.origin),[s[0],s[1],s[2],"refetch"],l?t.nextUrl:null,t.buildId).then(e=>{let t=e[0];if("string"!=typeof t)for(let e of t)(0,n.applyFlightData)(a,a,e)});h.push(e)}for(let e in d){let r=i({state:t,updatedTree:d[e],updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:s,canonicalUrl:c});h.push(r)}await Promise.all(h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7767:(e,t)=>{"use strict";var r,n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_FAST_REFRESH:function(){return s},ACTION_NAVIGATE:function(){return a},ACTION_PREFETCH:function(){return u},ACTION_REFRESH:function(){return o},ACTION_RESTORE:function(){return l},ACTION_SERVER_ACTION:function(){return c},ACTION_SERVER_PATCH:function(){return i},PrefetchCacheEntryStatus:function(){return n},PrefetchKind:function(){return r},isThenable:function(){return d}});let o="refresh",a="navigate",l="restore",i="server-patch",u="prefetch",s="fast-refresh",c="server-action";function d(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(r||(r={})),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3860:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(7767),r(5861),r(4025),r(5608),r(9809),r(1156),r(5703),r(5240);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4614:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[o,a]=r,[l,i]=t;return(0,n.matchSegment)(l,o)?!(t.length<=2)&&e(t.slice(2),a[i]):!!Array.isArray(l)}}});let n=r(455);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3325:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDynamicallyTrackedSearchParams:function(){return i},createUntrackedSearchParams:function(){return l}});let n=r(5869),o=r(2846),a=r(2255);function l(e){let t=n.staticGenerationAsyncStorage.getStore();return t&&t.forceStatic?{}:e}function i(e){let t=n.staticGenerationAsyncStorage.getStore();return t?t.forceStatic?{}:t.isStaticGeneration||t.dynamicShouldError?new Proxy({},{get:(e,r,n)=>("string"==typeof r&&(0,o.trackDynamicDataAccessed)(t,"searchParams."+r),a.ReflectAdapter.get(e,r,n)),has:(e,r)=>("string"==typeof r&&(0,o.trackDynamicDataAccessed)(t,"searchParams."+r),Reflect.has(e,r)),ownKeys:e=>((0,o.trackDynamicDataAccessed)(t,"searchParams"),Reflect.ownKeys(e))}):e:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6488:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return o}});let r="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=r}}function o(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9519:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unresolvedThenable",{enumerable:!0,get:function(){return r}});let r={then:()=>{}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{useReducerWithReduxDevtools:function(){return i},useUnwrapState:function(){return l}});let n=r(8374)._(r(7577)),o=r(7767);function a(e){if(e instanceof Map){let t={};for(let[r,n]of e.entries()){if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n._bundlerConfig){t[r]="FlightData";continue}}t[r]=a(n)}return t}if("object"==typeof e&&null!==e){let t={};for(let r in e){let n=e[r];if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n.hasOwnProperty("_bundlerConfig")){t[r]="FlightData";continue}}t[r]=a(n)}return t}return Array.isArray(e)?e.map(a):e}function l(e){return(0,o.isThenable)(e)?(0,n.use)(e):e}r(3879);let i=function(e){return[e,()=>{},()=>{}]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9683:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(3658),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7929:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let n=r(4655);function o(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9404:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return v}});let n=r(1174),o=r(326),a=n._(r(7577)),l=r(5619),i=r(944),u=r(3071),s=r(1348),c=r(3416),d=r(131),f=r(2413),p=r(9408),h=r(9683),m=r(3486),g=r(7767);function y(e){return"string"==typeof e?e:(0,u.formatUrl)(e)}let v=a.default.forwardRef(function(e,t){let r,n;let{href:u,as:v,children:b,prefetch:_=null,passHref:w,replace:x,shallow:R,scroll:E,locale:P,onClick:j,onMouseEnter:S,onTouchStart:O,legacyBehavior:T=!1,...M}=e;r=b,T&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let C=a.default.useContext(d.RouterContext),A=a.default.useContext(f.AppRouterContext),N=null!=C?C:A,I=!C,D=!1!==_,k=null===_?g.PrefetchKind.AUTO:g.PrefetchKind.FULL,{href:L,as:F}=a.default.useMemo(()=>{if(!C){let e=y(u);return{href:e,as:v?y(v):e}}let[e,t]=(0,l.resolveHref)(C,u,!0);return{href:e,as:v?(0,l.resolveHref)(C,v):t||e}},[C,u,v]),U=a.default.useRef(L),H=a.default.useRef(F);T&&(n=a.default.Children.only(r));let W=T?n&&"object"==typeof n&&n.ref:t,[z,G,B]=(0,p.useIntersection)({rootMargin:"200px"}),K=a.default.useCallback(e=>{(H.current!==F||U.current!==L)&&(B(),H.current=F,U.current=L),z(e),W&&("function"==typeof W?W(e):"object"==typeof W&&(W.current=e))},[F,W,L,B,z]);a.default.useEffect(()=>{},[F,L,G,P,D,null==C?void 0:C.locale,N,I,k]);let V={ref:K,onClick(e){T||"function"!=typeof j||j(e),T&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),N&&!e.defaultPrevented&&function(e,t,r,n,o,l,u,s,c){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,i.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==u||u;"beforePopState"in t?t[o?"replace":"push"](r,n,{shallow:l,locale:s,scroll:e}):t[o?"replace":"push"](n||r,{scroll:e})};c?a.default.startTransition(f):f()}(e,N,L,F,x,R,E,P,I)},onMouseEnter(e){T||"function"!=typeof S||S(e),T&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){T||"function"!=typeof O||O(e),T&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,s.isAbsoluteUrl)(F))V.href=F;else if(!T||w||"a"===n.type&&!("href"in n.props)){let e=void 0!==P?P:null==C?void 0:C.locale,t=(null==C?void 0:C.isLocaleDomain)&&(0,h.getDomainLocale)(F,e,null==C?void 0:C.locales,null==C?void 0:C.domainLocales);V.href=t||(0,m.addBasePath)((0,c.addLocale)(F,e,null==C?void 0:C.defaultLocale))}return T?a.default.cloneElement(n,V):(0,o.jsx)("a",{...M,...V,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(3236),o=r(3067),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,o.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4237:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(7929),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},956:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5619:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(2149),o=r(3071),a=r(757),l=r(1348),i=r(3658),u=r(944),s=r(4903),c=r(1394);function d(e,t,r){let d;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,l.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,u.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,i.normalizePathTrailingSlash)(e.pathname);let t="";if((0,s.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:l,params:i}=(0,c.interpolateAs)(e.pathname,e.pathname,r);l&&(t=(0,o.formatWithValidation)({pathname:l,hash:e.hash,query:(0,a.omit)(r,i)}))}let l=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[l,t||l]:l}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9408:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return u}});let n=r(7577),o=r(956),a="function"==typeof IntersectionObserver,l=new Map,i=[];function u(e){let{rootRef:t,rootMargin:r,disabled:u}=e,s=u||!a,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(a){if(s||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:o,elements:a}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=i.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=l.get(n)))return t;let o=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:o},i.push(r),l.set(r,t),t}(r);return a.set(e,t),o.observe(e),function(){if(a.delete(e),o.unobserve(e),0===a.size){o.disconnect(),l.delete(n);let e=i.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&i.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[s,r,t,c,f.current]),[p,c,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5633:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return u},APP_DIR_ALIAS:function(){return S},CACHE_ONE_YEAR:function(){return _},DOT_NEXT_ALIAS:function(){return P},ESLINT_DEFAULT_DIRS:function(){return K},GSP_NO_RETURNED_VALUE:function(){return U},GSSP_COMPONENT_MEMBER_ERROR:function(){return z},GSSP_NO_RETURNED_VALUE:function(){return H},INSTRUMENTATION_HOOK_FILENAME:function(){return R},MIDDLEWARE_FILENAME:function(){return w},MIDDLEWARE_LOCATION_REGEXP:function(){return x},NEXT_BODY_SUFFIX:function(){return d},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return b},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return h},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return m},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return p},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return v},NEXT_CACHE_TAGS_HEADER:function(){return f},NEXT_CACHE_TAG_MAX_ITEMS:function(){return g},NEXT_CACHE_TAG_MAX_LENGTH:function(){return y},NEXT_DATA_SUFFIX:function(){return s},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return c},NEXT_QUERY_PARAM_PREFIX:function(){return r},NON_STANDARD_NODE_ENV:function(){return G},PAGES_DIR_ALIAS:function(){return E},PRERENDER_REVALIDATE_HEADER:function(){return o},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return a},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return N},ROOT_DIR_ALIAS:function(){return j},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return A},RSC_ACTION_ENCRYPTION_ALIAS:function(){return C},RSC_ACTION_PROXY_ALIAS:function(){return M},RSC_ACTION_VALIDATE_ALIAS:function(){return T},RSC_MOD_REF_PROXY_ALIAS:function(){return O},RSC_PREFETCH_SUFFIX:function(){return l},RSC_SUFFIX:function(){return i},SERVER_PROPS_EXPORT_ERROR:function(){return F},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return D},SERVER_PROPS_SSG_CONFLICT:function(){return k},SERVER_RUNTIME:function(){return V},SSG_FALLBACK_EXPORT_ERROR:function(){return B},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return I},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return L},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return W},WEBPACK_LAYERS:function(){return $},WEBPACK_RESOURCE_QUERIES:function(){return Y}});let r="nxtP",n="nxtI",o="x-prerender-revalidate",a="x-prerender-revalidate-if-generated",l=".prefetch.rsc",i=".rsc",u=".action",s=".json",c=".meta",d=".body",f="x-next-cache-tags",p="x-next-cache-soft-tags",h="x-next-revalidated-tags",m="x-next-revalidate-tag-token",g=128,y=256,v=1024,b="_N_T_",_=31536e3,w="middleware",x=`(?:src/)?${w}`,R="instrumentation",E="private-next-pages",P="private-dot-next",j="private-next-root-dir",S="private-next-app-dir",O="private-next-rsc-mod-ref-proxy",T="private-next-rsc-action-validate",M="private-next-rsc-server-reference",C="private-next-rsc-action-encryption",A="private-next-rsc-action-client-wrapper",N="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",I="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",D="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",k="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",L="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",F="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",U="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",H="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",W="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",z="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",G='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',B="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",K=["app","pages","components","lib","src"],V={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},X={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},$={...X,GROUP:{serverOnly:[X.reactServerComponents,X.actionBrowser,X.appMetadataRoute,X.appRouteHandler,X.instrument],clientOnly:[X.serverSideRendering,X.appPagesBrowser],nonClientServerTarget:[X.middleware,X.api],app:[X.reactServerComponents,X.actionBrowser,X.appMetadataRoute,X.appRouteHandler,X.serverSideRendering,X.appPagesBrowser,X.shared,X.instrument]}},Y={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},6401:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPathname:function(){return n},isFullStringUrl:function(){return o},parseUrl:function(){return a}});let r="http://n";function n(e){return new URL(e,r).pathname}function o(e){return/https?:\/\//.test(e)}function a(e){let t;try{t=new URL(e,r)}catch{}return t}},2846:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Postpone:function(){return d},createPostponedAbortSignal:function(){return y},createPrerenderState:function(){return u},formatDynamicAPIAccesses:function(){return m},markCurrentScopeAsDynamic:function(){return s},trackDynamicDataAccessed:function(){return c},trackDynamicFetch:function(){return f},usedDynamicAPIs:function(){return h}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(7577)),o=r(442),a=r(6488),l=r(6401),i="function"==typeof n.default.unstable_postpone;function u(e){return{isDebugSkeleton:e,dynamicAccesses:[]}}function s(e,t){let r=(0,l.getPathname)(e.urlPathname);if(!e.isUnstableCacheCallback){if(e.dynamicShouldError)throw new a.StaticGenBailoutError(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)p(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new o.DynamicServerError(`Route ${r} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}}function c(e,t){let r=(0,l.getPathname)(e.urlPathname);if(e.isUnstableCacheCallback)throw Error(`Route ${r} used "${t}" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "${t}" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if(e.dynamicShouldError)throw new a.StaticGenBailoutError(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)p(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new o.DynamicServerError(`Route ${r} couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}function d({reason:e,prerenderState:t,pathname:r}){p(t,e,r)}function f(e,t){e.prerenderState&&p(e.prerenderState,t,e.urlPathname)}function p(e,t,r){g();let o=`Route ${r} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;e.dynamicAccesses.push({stack:e.isDebugSkeleton?Error().stack:void 0,expression:t}),n.default.unstable_postpone(o)}function h(e){return e.dynamicAccesses.length>0}function m(e){return e.dynamicAccesses.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function g(){if(!i)throw Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js")}function y(e){g();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}},2357:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentParam",{enumerable:!0,get:function(){return o}});let n=r(7356);function o(e){let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));return(t&&(e=e.slice(t.length)),e.startsWith("[[...")&&e.endsWith("]]"))?{type:"optional-catchall",param:e.slice(5,-2)}:e.startsWith("[...")&&e.endsWith("]")?{type:t?"catchall-intercepted":"catchall",param:e.slice(4,-1)}:e.startsWith("[")&&e.endsWith("]")?{type:t?"dynamic-intercepted":"dynamic",param:e.slice(1,-1)}:null}},7356:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return l},isInterceptionRouteAppPath:function(){return a}});let n=r(2862),o=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function l(e){let t,r,a;for(let n of e.split("/"))if(r=o.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?`/${a}`:t+"/"+a;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let l=t.split("/");if(l.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);a=l.slice(0,-2).concat(a).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:a}}},1616:(e,t,r)=>{"use strict";e.exports=r(399)},2413:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.AppRouterContext},7008:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.HooksClientContext},131:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.RouterContext},3347:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.ServerInsertedHtml},962:(e,t,r)=>{"use strict";e.exports=r(1616).vendored["react-ssr"].ReactDOM},326:(e,t,r)=>{"use strict";e.exports=r(1616).vendored["react-ssr"].ReactJsxRuntime},6493:(e,t,r)=>{"use strict";e.exports=r(1616).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},7577:(e,t,r)=>{"use strict";e.exports=r(1616).vendored["react-ssr"].React},2255:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},2451:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},2165:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)&4294967295;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},4129:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return o}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},6058:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},3879:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ActionQueueContext:function(){return i},createMutableActionQueue:function(){return c}});let n=r(8374),o=r(7767),a=r(3860),l=n._(r(7577)),i=l.default.createContext(null);function u(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?s({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:o.ACTION_REFRESH,origin:window.location.origin},t)))}async function s(e){let{actionQueue:t,action:r,setState:n}=e,a=t.state;if(!a)throw Error("Invariant: Router state not initialized");t.pending=r;let l=r.payload,i=t.action(a,l);function s(e){r.discarded||(t.state=e,t.devToolsInstance&&t.devToolsInstance.send(l,e),u(t,n),r.resolve(e))}(0,o.isThenable)(i)?i.then(s,e=>{u(t,n),r.reject(e)}):s(i)}function c(){let e={state:null,dispatch:(t,r)=>(function(e,t,r){let n={resolve:r,reject:()=>{}};if(t.type!==o.ACTION_RESTORE){let e=new Promise((e,t)=>{n={resolve:e,reject:t}});(0,l.startTransition)(()=>{r(e)})}let a={payload:t,next:null,resolve:n.resolve,reject:n.reject};null===e.pending?(e.last=a,s({actionQueue:e,action:a,setState:r})):t.type===o.ACTION_NAVIGATE||t.type===o.ACTION_RESTORE?(e.pending.discarded=!0,e.last=a,e.pending.payload.type===o.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),s({actionQueue:e,action:a,setState:r})):(null!==e.last&&(e.last.next=a),e.last=a)})(e,t,r),action:async(e,t)=>{if(null===e)throw Error("Invariant: Router state not initialized");return(0,a.reducer)(e,t)},pending:null,last:null};return e}},8974:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let n=r(3067);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:a}=(0,n.parsePath)(e);return""+t+r+o+a}},2862:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return l}});let n=r(6058),o=r(8071);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function l(e){return e.replace(/\.rsc($|\?)/,"$1")}},3071:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return l}});let n=r(8374)._(r(2149)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",l=e.pathname||"",i=e.hash||"",u=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),""+a+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+i}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},9976:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange){e();return}let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},4903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let n=r(4712),o=r(5541)},1394:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return a}});let n=r(9966),o=r(7249);function a(e,t,r){let a="",l=(0,o.getRouteRegex)(e),i=l.groups,u=(t!==e?(0,n.getRouteMatcher)(l)(t):"")||r;a=e;let s=Object.keys(i);return s.every(e=>{let t=u[e]||"",{repeat:r,optional:n}=i[e],o="["+(r?"...":"")+e+"]";return n&&(o=(t?"":"/")+"["+o+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in u)&&(a=a.replace(o,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(a=""),{params:s,result:a}}},2148:(e,t)=>{"use strict";function r(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isBot",{enumerable:!0,get:function(){return r}})},5541:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return a}});let n=r(7356),o=/\/\[[^/]+?\](?=\/|$)/;function a(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),o.test(e)}},944:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(1348),o=r(7929);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},757:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},3067:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},4655:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let n=r(3067);function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},2149:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},3236:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},9966:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(1348);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},l={};return Object.keys(r).forEach(e=>{let t=r[e],n=o[t.pos];void 0!==n&&(l[e]=~n.indexOf("/")?n.split("/").map(e=>a(e)):t.repeat?[a(n)]:a(n))}),l}}},7249:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return p},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return s},parseParameter:function(){return i}});let n=r(5633),o=r(7356),a=r(2451),l=r(3236);function i(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e){let t=(0,l.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=o.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),l=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&l){let{key:e,optional:o,repeat:u}=i(l[1]);return r[e]={pos:n++,repeat:u,optional:o},"/"+(0,a.escapeStringRegexp)(t)+"([^/]+?)"}if(!l)return"/"+(0,a.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:o}=i(l[1]);return r[e]={pos:n++,repeat:t,optional:o},t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function s(e){let{parameterizedRoute:t,groups:r}=u(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function c(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:o,keyPrefix:l}=e,{key:u,optional:s,repeat:c}=i(n),d=u.replace(/\W/g,"");l&&(d=""+l+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),l?o[d]=""+l+u:o[d]=u;let p=t?(0,a.escapeStringRegexp)(t):"";return c?s?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function d(e,t){let r;let i=(0,l.removeTrailingSlash)(e).slice(1).split("/"),u=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),s={};return{namedParameterizedRoute:i.map(e=>{let r=o.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),l=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&l){let[r]=e.split(l[0]);return c({getSafeRouteKey:u,interceptionMarker:r,segment:l[1],routeKeys:s,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return l?c({getSafeRouteKey:u,segment:l[1],routeKeys:s,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,a.escapeStringRegexp)(e)}).join(""),routeKeys:s}}function f(e,t){let r=d(e,t);return{...s(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function p(e,t){let{parameterizedRoute:r}=u(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=d(e,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},4712:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let r=o.slice(1,-1),l=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),l=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function a(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(l){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');a(this.optionalRestSlugName,r),this.optionalRestSlugName=r,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');a(this.restSlugName,r),this.restSlugName=r,o="[...]"}}else{if(l)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');a(this.slugName,r),this.slugName=r,o="[]"}}this.children.has(o)||this.children.set(o,new r),this.children.get(o)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},8071:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return o},PAGE_SEGMENT_KEY:function(){return n},isGroupSegment:function(){return r}});let n="__PAGE__",o="__DEFAULT__"},1348:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return l},getURL:function(){return i},isAbsoluteUrl:function(){return a},isResSent:function(){return s},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},576:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},8260:(e,t,r)=>{"use strict";r.d(t,{Z:()=>V});var n,o,a=function(){return(a=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create,Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,r(7577)),u="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,r,n,o=(void 0===t&&(t=p),r=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(a)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return o.options=a({async:!0,ssr:!1},e),o}(),m=function(){},g=i.forwardRef(function(e,t){var r,n,o,u,s=i.useRef(null),p=i.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),g=p[0],y=p[1],v=e.forwardProps,b=e.children,_=e.className,w=e.removeScrollBar,x=e.enabled,R=e.shards,E=e.sideCar,P=e.noRelative,j=e.noIsolation,S=e.inert,O=e.allowPinchZoom,T=e.as,M=e.gapMode,C=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),A=(r=[s,t],n=function(e){return r.forEach(function(t){return c(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),n=new Set(r),o=u.current;t.forEach(function(e){n.has(e)||c(e,null)}),n.forEach(function(e){t.has(e)||c(e,o)})}f.set(u,r)},[r]),u),N=a(a({},C),g);return i.createElement(i.Fragment,null,x&&i.createElement(E,{sideCar:h,removeScrollBar:w,shards:R,noRelative:P,noIsolation:j,inert:S,setCallbacks:y,allowPinchZoom:!!O,lockRef:s,gapMode:M}),v?i.cloneElement(i.Children.only(b),a(a({},N),{ref:A})):i.createElement(void 0===T?"div":T,a({},N,{className:_,ref:A}),b))});g.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},g.classNames={fullWidth:s,zeroRight:u};var y=function(e){var t=e.sideCar,r=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return i.createElement(n,a({},r))};y.isSideCarExport=!0;var v=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||r.nc;return t&&e.setAttribute("nonce",t),e}())){var a,l;(a=t).styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=v();return function(t,r){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},_=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(r),x(n),x(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=R(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},P=_(),j="data-scroll-locked",S=function(e,t,r,n){var o=e.left,a=e.top,l=e.right,i=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(i,"px ").concat(n,";\n  }\n  body[").concat(j,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(i,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(i,"px ").concat(n,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(i,"px ").concat(n,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(j,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},O=function(){var e=parseInt(document.body.getAttribute(j)||"0",10);return isFinite(e)?e:0},T=function(){i.useEffect(function(){return document.body.setAttribute(j,(O()+1).toString()),function(){var e=O()-1;e<=0?document.body.removeAttribute(j):document.body.setAttribute(j,e.toString())}},[])},M=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;T();var a=i.useMemo(function(){return E(o)},[o]);return i.createElement(P,{styles:S(a,!t,o,r?"":"!important")})},C=!1;if("undefined"!=typeof window)try{var A=Object.defineProperty({},"passive",{get:function(){return C=!0,!0}});window.addEventListener("test",A,A),window.removeEventListener("test",A,A)}catch(e){C=!1}var N=!!C&&{passive:!1},I=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},D=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),k(e,n)){var o=L(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},k=function(e,t){return"v"===e?I(t,"overflowY"):I(t,"overflowX")},L=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,r,n,o){var a,l=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=l*n,u=r.target,s=t.contains(u),c=!1,d=i>0,f=0,p=0;do{if(!u)break;var h=L(e,u),m=h[0],g=h[1]-h[2]-l*m;(m||g)&&k(e,u)&&(f+=g,p+=m);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&i>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-i>p)&&(c=!0),c},U=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},z=0,G=[];let B=(n=function(e){var t=i.useRef([]),r=i.useRef([0,0]),n=i.useRef(),o=i.useState(z++)[0],a=i.useState(_)[0],l=i.useRef(e);i.useEffect(function(){l.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,a=U(e),i=r.current,u="deltaX"in e?e.deltaX:i[0]-a[0],s="deltaY"in e?e.deltaY:i[1]-a[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=D(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(u||s)&&(n.current=o),!o)return!0;var p=n.current||o;return F(p,t,e,"h"===p?u:s,!0)},[]),s=i.useCallback(function(e){if(G.length&&G[G.length-1]===a){var r="deltaY"in e?H(e):U(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(l.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){r.current=U(e),n.current=void 0},[]),f=i.useCallback(function(t){c(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){c(t.type,U(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return G.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,N),document.addEventListener("touchmove",s,N),document.addEventListener("touchstart",d,N),function(){G=G.filter(function(e){return e!==a}),document.removeEventListener("wheel",s,N),document.removeEventListener("touchmove",s,N),document.removeEventListener("touchstart",d,N)}},[]);var h=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?i.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(n),y);var K=i.forwardRef(function(e,t){return i.createElement(g,a({},e,{ref:t,sideCar:B}))});K.classNames=g.classNames;let V=K},7371:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(670),o=r.n(n)},8570:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return n}});let n=r(1749).createClientModuleProxy},9943:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/develop/GitHub/tiktokemoji/node_modules/next/dist/client/components/app-router.js")},3144:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/develop/GitHub/tiktokemoji/node_modules/next/dist/client/components/client-page.js")},7922:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/develop/GitHub/tiktokemoji/node_modules/next/dist/client/components/error-boundary.js")},5106:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/develop/GitHub/tiktokemoji/node_modules/next/dist/client/components/layout-router.js")},525:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/develop/GitHub/tiktokemoji/node_modules/next/dist/client/components/not-found-boundary.js")},5866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),r(3370);let n=r(9510);r(1159);let o={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function a(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("title",{children:"404: This page could not be found."}),(0,n.jsx)("div",{style:o.error,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,n.jsx)("h1",{className:"next-error-h1",style:o.h1,children:"404"}),(0,n.jsx)("div",{style:o.desc,children:(0,n.jsx)("h2",{style:o.h2,children:"This page could not be found."})})]})})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4892:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/develop/GitHub/tiktokemoji/node_modules/next/dist/client/components/render-from-template-context.js")},9181:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDynamicallyTrackedSearchParams:function(){return i},createUntrackedSearchParams:function(){return l}});let n=r(5869),o=r(6278),a=r(8238);function l(e){let t=n.staticGenerationAsyncStorage.getStore();return t&&t.forceStatic?{}:e}function i(e){let t=n.staticGenerationAsyncStorage.getStore();return t?t.forceStatic?{}:t.isStaticGeneration||t.dynamicShouldError?new Proxy({},{get:(e,r,n)=>("string"==typeof r&&(0,o.trackDynamicDataAccessed)(t,"searchParams."+r),a.ReflectAdapter.get(e,r,n)),has:(e,r)=>("string"==typeof r&&(0,o.trackDynamicDataAccessed)(t,"searchParams."+r),Reflect.has(e,r)),ownKeys:e=>((0,o.trackDynamicDataAccessed)(t,"searchParams"),Reflect.ownKeys(e))}):e:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},670:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/develop/GitHub/tiktokemoji/node_modules/next/dist/client/link.js")},5231:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRouter:function(){return o.default},ClientPageRoot:function(){return c.ClientPageRoot},LayoutRouter:function(){return a.default},NotFoundBoundary:function(){return p.NotFoundBoundary},Postpone:function(){return g.Postpone},RenderFromTemplateContext:function(){return l.default},actionAsyncStorage:function(){return s.actionAsyncStorage},createDynamicallyTrackedSearchParams:function(){return d.createDynamicallyTrackedSearchParams},createUntrackedSearchParams:function(){return d.createUntrackedSearchParams},decodeAction:function(){return n.decodeAction},decodeFormState:function(){return n.decodeFormState},decodeReply:function(){return n.decodeReply},patchFetch:function(){return _},preconnect:function(){return m.preconnect},preloadFont:function(){return m.preloadFont},preloadStyle:function(){return m.preloadStyle},renderToReadableStream:function(){return n.renderToReadableStream},requestAsyncStorage:function(){return u.requestAsyncStorage},serverHooks:function(){return f},staticGenerationAsyncStorage:function(){return i.staticGenerationAsyncStorage},taintObjectReference:function(){return y.taintObjectReference}});let n=r(1749),o=v(r(9943)),a=v(r(5106)),l=v(r(4892)),i=r(5869),u=r(4580),s=r(2934),c=r(3144),d=r(9181),f=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=b(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=o?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(n,a,l):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(4789)),p=r(525),h=r(3131);r(7922);let m=r(135),g=r(9257),y=r(526);function v(e){return e&&e.__esModule?e:{default:e}}function b(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(b=function(e){return e?r:t})(e)}function _(){return(0,h.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:i.staticGenerationAsyncStorage})}},9257:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Postpone",{enumerable:!0,get:function(){return n.Postpone}});let n=r(6278)},135:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{preconnect:function(){return l},preloadFont:function(){return a},preloadStyle:function(){return o}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(7049));function o(e,t){let r={as:"style"};"string"==typeof t&&(r.crossOrigin=t),n.default.preload(e,r)}function a(e,t,r){let o={as:"font",type:t};"string"==typeof r&&(o.crossOrigin=r),n.default.preload(e,o)}function l(e,t){n.default.preconnect(e,"string"==typeof t?{crossOrigin:t}:void 0)}},526:(e,t,r)=>{"use strict";function n(){throw Error("Taint can only be used with the taint flag.")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{taintObjectReference:function(){return o},taintUniqueValue:function(){return a}}),r(1159);let o=n,a=n},7049:(e,t,r)=>{"use strict";e.exports=r(3191).vendored["react-rsc"].ReactDOM},9510:(e,t,r)=>{"use strict";e.exports=r(3191).vendored["react-rsc"].ReactJsxRuntime},1749:(e,t,r)=>{"use strict";e.exports=r(3191).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},8238:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},2561:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:()=>n})},545:(e,t,r)=>{"use strict";r.d(t,{B:()=>u});var n=r(7577),o=r(3095),a=r(8051),l=r(4214),i=r(326);function u(e){let t=e+"CollectionProvider",[r,u]=(0,o.b)(t),[s,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),a=n.useRef(new Map).current;return(0,i.jsx)(s,{scope:t,itemMap:a,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=(0,l.Z8)(f),h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(f,r),l=(0,a.e)(t,o.collectionRef);return(0,i.jsx)(p,{ref:l,children:n})});h.displayName=f;let m=e+"CollectionItemSlot",g="data-radix-collection-item",y=(0,l.Z8)(m),v=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,u=n.useRef(null),s=(0,a.e)(t,u),d=c(m,r);return n.useEffect(()=>(d.itemMap.set(u,{ref:u,...l}),()=>void d.itemMap.delete(u))),(0,i.jsx)(y,{[g]:"",ref:s,children:o})});return v.displayName=m,[{Provider:d,Slot:h,ItemSlot:v},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},u]}},8051:(e,t,r)=>{"use strict";r.d(t,{F:()=>a,e:()=>l});var n=r(7577);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(a(...e),e)}},3095:(e,t,r)=>{"use strict";r.d(t,{b:()=>a});var n=r(7577),o=r(326);function a(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let l=n.createContext(a),i=r.length;r=[...r,a];let u=t=>{let{scope:r,children:a,...u}=t,s=r?.[e]?.[i]||l,c=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:a})};return u.displayName=t+"Provider",[u,function(r,o){let u=o?.[e]?.[i]||l,s=n.useContext(u);if(s)return s;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},7124:(e,t,r)=>{"use strict";r.d(t,{gm:()=>a});var n=r(7577);r(326);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},825:(e,t,r)=>{"use strict";r.d(t,{I0:()=>y,XB:()=>f,fC:()=>g});var n,o=r(7577),a=r(2561),l=r(5226),i=r(8051),u=r(5049),s=r(326),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:g,onInteractOutside:y,onDismiss:v,...b}=e,_=o.useContext(d),[w,x]=o.useState(null),R=w?.ownerDocument??globalThis?.document,[,E]=o.useState({}),P=(0,i.e)(t,e=>x(e)),j=Array.from(_.layers),[S]=[..._.layersWithOutsidePointerEventsDisabled].slice(-1),O=j.indexOf(S),T=w?j.indexOf(w):-1,M=_.layersWithOutsidePointerEventsDisabled.size>0,C=T>=O,A=function(e,t=globalThis?.document){let r=(0,u.W)(e),n=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=n,t.addEventListener("click",a.current,{once:!0})):n()}else t.removeEventListener("click",a.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[..._.branches].some(e=>e.contains(t));!C||r||(p?.(e),y?.(e),e.defaultPrevented||v?.())},R),N=function(e,t=globalThis?.document){let r=(0,u.W)(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[..._.branches].some(e=>e.contains(t))||(g?.(e),y?.(e),e.defaultPrevented||v?.())},R);return function(e,t=globalThis?.document){let r=(0,u.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{T!==_.layers.size-1||(f?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},R),o.useEffect(()=>{if(w)return r&&(0===_.layersWithOutsidePointerEventsDisabled.size&&(n=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),_.layersWithOutsidePointerEventsDisabled.add(w)),_.layers.add(w),h(),()=>{r&&1===_.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=n)}},[w,R,r,_]),o.useEffect(()=>()=>{w&&(_.layers.delete(w),_.layersWithOutsidePointerEventsDisabled.delete(w),h())},[w,_]),o.useEffect(()=>{let e=()=>E({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(l.WV.div,{...b,ref:P,style:{pointerEvents:M?C?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.M)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,a.M)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,a.M)(e.onPointerDownCapture,A.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),a=(0,i.e)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,s.jsx)(l.WV.div,{...e,ref:a})});function h(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,r,{discrete:n}){let o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,l.jH)(o,a):o.dispatchEvent(a)}p.displayName="DismissableLayerBranch";var g=f,y=p},9313:(e,t,r)=>{"use strict";r.d(t,{oC:()=>e4,VY:()=>e7,ZA:()=>e5,ck:()=>e3,wU:()=>te,__:()=>e6,Uv:()=>e2,Ee:()=>e8,Rk:()=>e9,fC:()=>e0,Z0:()=>tt,Tr:()=>tr,tu:()=>to,fF:()=>tn,xz:()=>e1});var n=r(7577),o=r(2561),a=r(8051),l=r(3095),i=r(2067),u=r(5226),s=r(545),c=r(7124),d=r(825),f=r(699),p=r(441),h=r(8957),m=r(9625),g=r(3078),y=r(9815),v=r(5594),b=r(4214),_=r(5049),w=r(5664),x=r(8260),R=r(326),E=["Enter"," "],P=["ArrowUp","PageDown","End"],j=["ArrowDown","PageUp","Home",...P],S={ltr:[...E,"ArrowRight"],rtl:[...E,"ArrowLeft"]},O={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[M,C,A]=(0,s.B)(T),[N,I]=(0,l.b)(T,[A,m.D7,v.Pc]),D=(0,m.D7)(),k=(0,v.Pc)(),[L,F]=N(T),[U,H]=N(T),W=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:l,modal:i=!0}=e,u=D(t),[s,d]=n.useState(null),f=n.useRef(!1),p=(0,_.W)(l),h=(0,c.gm)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,R.jsx)(m.fC,{...u,children:(0,R.jsx)(L,{scope:t,open:r,onOpenChange:p,content:s,onContentChange:d,children:(0,R.jsx)(U,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:i,children:o})})})};W.displayName=T;var z=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=D(r);return(0,R.jsx)(m.ee,{...o,...n,ref:t})});z.displayName="MenuAnchor";var G="MenuPortal",[B,K]=N(G,{forceMount:void 0}),V=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=F(G,t);return(0,R.jsx)(B,{scope:t,forceMount:r,children:(0,R.jsx)(y.z,{present:r||a.open,children:(0,R.jsx)(g.h,{asChild:!0,container:o,children:n})})})};V.displayName=G;var X="MenuContent",[$,Y]=N(X),q=n.forwardRef((e,t)=>{let r=K(X,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=F(X,e.__scopeMenu),l=H(X,e.__scopeMenu);return(0,R.jsx)(M.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(y.z,{present:n||a.open,children:(0,R.jsx)(M.Slot,{scope:e.__scopeMenu,children:l.modal?(0,R.jsx)(Z,{...o,ref:t}):(0,R.jsx)(Q,{...o,ref:t})})})})}),Z=n.forwardRef((e,t)=>{let r=F(X,e.__scopeMenu),l=n.useRef(null),i=(0,a.e)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,w.Ry)(e)},[]),(0,R.jsx)(ee,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=F(X,e.__scopeMenu);return(0,R.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),J=(0,b.Z8)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:i,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:h,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:_,onDismiss:w,disableOutsideScroll:E,...S}=e,O=F(X,r),T=H(X,r),M=D(r),A=k(r),N=C(r),[I,L]=n.useState(null),U=n.useRef(null),W=(0,a.e)(t,U,O.onContentChange),z=n.useRef(0),G=n.useRef(""),B=n.useRef(0),K=n.useRef(null),V=n.useRef("right"),Y=n.useRef(0),q=E?x.Z:n.Fragment,Z=e=>{let t=G.current+e,r=N().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(r.map(e=>e.textValue),t,o),l=r.find(e=>e.textValue===a)?.ref.current;(function e(t){G.current=t,window.clearTimeout(z.current),""!==t&&(z.current=window.setTimeout(()=>e(""),1e3))})(t),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(z.current),[]),(0,f.EW)();let Q=n.useCallback(e=>V.current===K.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e],i=t[a],u=l.x,s=l.y,c=i.x,d=i.y;s>n!=d>n&&r<(c-u)*(n-s)/(d-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,K.current?.area),[]);return(0,R.jsx)($,{scope:r,searchRef:G,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{Q(e)||(U.current?.focus(),L(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:B,onPointerGraceIntentChange:n.useCallback(e=>{K.current=e},[]),children:(0,R.jsx)(q,{...E?{as:J,allowPinchZoom:!0}:void 0,children:(0,R.jsx)(p.M,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.M)(u,e=>{e.preventDefault(),U.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,R.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:_,onDismiss:w,children:(0,R.jsx)(v.fC,{asChild:!0,...A,dir:T.dir,orientation:"vertical",loop:l,currentTabStopId:I,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.M)(h,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,R.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eO(O.open),"data-radix-menu-content":"",dir:T.dir,...M,...S,ref:W,style:{outline:"none",...S.style},onKeyDown:(0,o.M)(S.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Z(e.key));let o=U.current;if(e.target!==o||!j.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);P.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(z.current),G.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eC(e=>{let t=e.target,r=Y.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>Y.current?"right":"left";V.current=t,Y.current=e.clientX}}))})})})})})})});q.displayName=X;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,R.jsx)(u.WV.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,R.jsx)(u.WV.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:l,...i}=e,s=n.useRef(null),c=H(en,e.__scopeMenu),d=Y(en,e.__scopeMenu),f=(0,a.e)(t,s),p=n.useRef(!1);return(0,R.jsx)(el,{...i,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>l?.(e),{once:!0}),(0,u.jH)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!r&&(!t||" "!==e.key)&&E.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var el=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:l=!1,textValue:i,...s}=e,c=Y(en,r),d=k(r),f=n.useRef(null),p=(0,a.e)(t,f),[h,m]=n.useState(!1),[g,y]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&y((e.textContent??"").trim())},[s.children]),(0,R.jsx)(M.ItemSlot,{scope:r,disabled:l,textValue:i??g,children:(0,R.jsx)(v.ck,{asChild:!0,...d,focusable:!l,children:(0,R.jsx)(u.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eC(e=>{l?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eC(e=>c.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),ei=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,R.jsx)(em,{scope:e.__scopeMenu,checked:r,children:(0,R.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eT(r)?"mixed":r,...a,ref:t,"data-state":eM(r),onSelect:(0,o.M)(a.onSelect,()=>n?.(!!eT(r)||!r),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[es,ec]=N(eu,{value:void 0,onValueChange:()=>{}}),ed=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,_.W)(n);return(0,R.jsx)(es,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,R.jsx)(et,{...o,ref:t})})});ed.displayName=eu;var ef="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ec(ef,e.__scopeMenu),l=r===a.value;return(0,R.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,R.jsx)(ea,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":eM(l),onSelect:(0,o.M)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var eh="MenuItemIndicator",[em,eg]=N(eh,{checked:!1}),ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eg(eh,r);return(0,R.jsx)(y.z,{present:n||eT(a.checked)||!0===a.checked,children:(0,R.jsx)(u.WV.span,{...o,ref:t,"data-state":eM(a.checked)})})});ey.displayName=eh;var ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,R.jsx)(u.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ev.displayName="MenuSeparator";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=D(r);return(0,R.jsx)(m.Eh,{...o,...n,ref:t})});eb.displayName="MenuArrow";var e_="MenuSub",[ew,ex]=N(e_),eR=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:a}=e,l=F(e_,t),i=D(t),[u,s]=n.useState(null),[c,d]=n.useState(null),f=(0,_.W)(a);return n.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,R.jsx)(m.fC,{...i,children:(0,R.jsx)(L,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,R.jsx)(ew,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:u,onTriggerChange:s,children:r})})})};eR.displayName=e_;var eE="MenuSubTrigger",eP=n.forwardRef((e,t)=>{let r=F(eE,e.__scopeMenu),l=H(eE,e.__scopeMenu),i=ex(eE,e.__scopeMenu),u=Y(eE,e.__scopeMenu),s=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,R.jsx)(z,{asChild:!0,...f,children:(0,R.jsx)(el,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":eO(r.open),...e,ref:(0,a.F)(t,i.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eC(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eC(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],l=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:l,y:t.top},{x:l,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==u.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&S[l.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eP.displayName=eE;var ej="MenuSubContent",eS=n.forwardRef((e,t)=>{let r=K(X,e.__scopeMenu),{forceMount:l=r.forceMount,...i}=e,u=F(X,e.__scopeMenu),s=H(X,e.__scopeMenu),c=ex(ej,e.__scopeMenu),d=n.useRef(null),f=(0,a.e)(t,d);return(0,R.jsx)(M.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(y.z,{present:l||u.open,children:(0,R.jsx)(M.Slot,{scope:e.__scopeMenu,children:(0,R.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...i,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=O[s.dir].includes(e.key);t&&r&&(u.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eO(e){return e?"open":"closed"}function eT(e){return"indeterminate"===e}function eM(e){return eT(e)?"indeterminate":e?"checked":"unchecked"}function eC(e){return t=>"mouse"===t.pointerType?e(t):void 0}eS.displayName=ej;var eA="DropdownMenu",[eN,eI]=(0,l.b)(eA,[I]),eD=I(),[ek,eL]=eN(eA),eF=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:l,onOpenChange:u,modal:s=!0}=e,c=eD(t),d=n.useRef(null),[f,p]=(0,i.T)({prop:a,defaultProp:l??!1,onChange:u,caller:eA});return(0,R.jsx)(ek,{scope:t,triggerId:(0,h.M)(),triggerRef:d,contentId:(0,h.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,R.jsx)(W,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:r})})};eF.displayName=eA;var eU="DropdownMenuTrigger",eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...l}=e,i=eL(eU,r),s=eD(r);return(0,R.jsx)(z,{asChild:!0,...s,children:(0,R.jsx)(u.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.F)(t,i.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eH.displayName=eU;var eW=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eD(t);return(0,R.jsx)(V,{...n,...r})};eW.displayName="DropdownMenuPortal";var ez="DropdownMenuContent",eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,l=eL(ez,r),i=eD(r),u=n.useRef(!1);return(0,R.jsx)(q,{id:l.contentId,"aria-labelledby":l.triggerId,...i,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{u.current||l.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=ez;var eB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(et,{...o,...n,ref:t})});eB.displayName="DropdownMenuGroup";var eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(er,{...o,...n,ref:t})});eK.displayName="DropdownMenuLabel";var eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ea,{...o,...n,ref:t})});eV.displayName="DropdownMenuItem";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ei,{...o,...n,ref:t})});eX.displayName="DropdownMenuCheckboxItem";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ed,{...o,...n,ref:t})});e$.displayName="DropdownMenuRadioGroup";var eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ep,{...o,...n,ref:t})});eY.displayName="DropdownMenuRadioItem";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ey,{...o,...n,ref:t})});eq.displayName="DropdownMenuItemIndicator";var eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ev,{...o,...n,ref:t})});eZ.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(eb,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(eP,{...o,...n,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(eS,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eJ.displayName="DropdownMenuSubContent";var e0=eF,e1=eH,e2=eW,e7=eG,e5=eB,e6=eK,e3=eV,e4=eX,e8=e$,e9=eY,te=eq,tt=eZ,tr=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:a}=e,l=eD(t),[u,s]=(0,i.T)({prop:n,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,R.jsx)(eR,{...l,open:u,onOpenChange:s,children:r})},tn=eQ,to=eJ},699:(e,t,r)=>{"use strict";r.d(t,{EW:()=>a});var n=r(7577),o=0;function a(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},441:(e,t,r)=>{"use strict";r.d(t,{M:()=>d});var n=r(7577),o=r(8051),a=r(5226),l=r(5049),i=r(326),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:g,onUnmountAutoFocus:y,...v}=e,[b,_]=n.useState(null),w=(0,l.W)(g),x=(0,l.W)(y),R=n.useRef(null),E=(0,o.e)(t,e=>_(e)),P=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(P.paused||!b)return;let t=e.target;b.contains(t)?R.current=t:h(R.current,{select:!0})},t=function(e){if(P.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||h(R.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,b,P.paused]),n.useEffect(()=>{if(b){m.add(P);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,c);b.addEventListener(u,w),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(h(n,{select:t}),document.activeElement!==r)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(u,w),setTimeout(()=>{let t=new CustomEvent(s,c);b.addEventListener(s,x),b.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),b.removeEventListener(s,x),m.remove(P)},0)}}},[b,w,x,P]);let j=n.useCallback(e=>{if(!r&&!d||P.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||n!==a?e.shiftKey&&n===o&&(e.preventDefault(),r&&h(a,{select:!0})):(e.preventDefault(),r&&h(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,P.paused]);return(0,i.jsx)(a.WV.div,{tabIndex:-1,...v,ref:E,onKeyDown:j})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function h(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=g(e,t)).unshift(t)},remove(t){e=g(e,t),e[0]?.resume()}}}();function g(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},8957:(e,t,r)=>{"use strict";r.d(t,{M:()=>u});var n,o=r(7577),a=r(5819),l=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function u(e){let[t,r]=o.useState(l());return(0,a.b)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},9625:(e,t,r)=>{"use strict";r.d(t,{ee:()=>e9,Eh:()=>tt,VY:()=>te,fC:()=>e8,D7:()=>eV});var n=r(7577);let o=["top","right","bottom","left"],a=Math.min,l=Math.max,i=Math.round,u=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}let y=new Set(["top","bottom"]);function v(e){return y.has(p(e))?"y":"x"}function b(e){return e.replace(/start|end/g,e=>d[e])}let _=["left","right"],w=["right","left"],x=["top","bottom"],R=["bottom","top"];function E(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function P(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function j(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function S(e,t,r){let n,{reference:o,floating:a}=e,l=v(t),i=m(v(t)),u=g(i),s=p(t),c="y"===l,d=o.x+o.width/2-a.width/2,f=o.y+o.height/2-a.height/2,y=o[u]/2-a[u]/2;switch(s){case"top":n={x:d,y:o.y-a.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-a.width,y:f};break;default:n={x:o.x,y:o.y}}switch(h(t)){case"start":n[i]-=y*(r&&c?-1:1);break;case"end":n[i]+=y*(r&&c?-1:1)}return n}let O=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:a=[],platform:l}=r,i=a.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=S(s,n,u),f=n,p={},h=0;for(let r=0;r<i.length;r++){let{name:a,fn:m}=i[r],{x:g,y:y,data:v,reset:b}=await m({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,d=null!=y?y:d,p={...p,[a]:{...p[a],...v}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(s=!0===b.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=S(s,f,u)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function T(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:a,rects:l,elements:i,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=P(h),g=i[p?"floating"===d?"reference":"floating":d],y=j(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(g)))||r?g:g.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(i.floating)),boundary:s,rootBoundary:c,strategy:u})),v="floating"===d?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(i.floating)),_=await (null==a.isElement?void 0:a.isElement(b))&&await (null==a.getScale?void 0:a.getScale(b))||{x:1,y:1},w=j(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:v,offsetParent:b,strategy:u}):v);return{top:(y.top-w.top+m.top)/_.y,bottom:(w.bottom-y.bottom+m.bottom)/_.y,left:(y.left-w.left+m.left)/_.x,right:(w.right-y.right+m.right)/_.x}}function M(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function C(e){return o.some(t=>e[t]>=0)}let A=new Set(["left","top"]);async function N(e,t){let{placement:r,platform:n,elements:o}=e,a=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=p(r),i=h(r),u="y"===v(r),s=A.has(l)?-1:1,c=a&&u?-1:1,d=f(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return i&&"number"==typeof y&&(g="end"===i?-1*y:y),u?{x:g*c,y:m*s}:{x:m*s,y:g*c}}function I(){return"undefined"!=typeof window}function D(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!I()&&(e instanceof Node||e instanceof k(e).Node)}function U(e){return!!I()&&(e instanceof Element||e instanceof k(e).Element)}function H(e){return!!I()&&(e instanceof HTMLElement||e instanceof k(e).HTMLElement)}function W(e){return!!I()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof k(e).ShadowRoot)}let z=new Set(["inline","contents"]);function G(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!z.has(o)}let B=new Set(["table","td","th"]),K=[":popover-open",":modal"];function V(e){return K.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let X=["transform","translate","scale","rotate","perspective"],$=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function q(e){let t=Z(),r=U(e)?ee(e):e;return X.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||$.some(e=>(r.willChange||"").includes(e))||Y.some(e=>(r.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(D(e))}function ee(e){return k(e).getComputedStyle(e)}function et(e){return U(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function er(e){if("html"===D(e))return e;let t=e.assignedSlot||e.parentNode||W(e)&&e.host||L(e);return W(t)?t.host:t}function en(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=er(t);return J(r)?t.ownerDocument?t.ownerDocument.body:t.body:H(r)&&G(r)?r:e(r)}(e),a=o===(null==(n=e.ownerDocument)?void 0:n.body),l=k(o);if(a){let e=eo(l);return t.concat(l,l.visualViewport||[],G(o)?o:[],e&&r?en(e):[])}return t.concat(o,en(o,[],r))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ea(e){let t=ee(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=H(e),a=o?e.offsetWidth:r,l=o?e.offsetHeight:n,u=i(r)!==a||i(n)!==l;return u&&(r=a,n=l),{width:r,height:n,$:u}}function el(e){return U(e)?e:e.contextElement}function ei(e){let t=el(e);if(!H(t))return s(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:a}=ea(t),l=(a?i(r.width):r.width)/n,u=(a?i(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let eu=s(0);function es(e){let t=k(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function ec(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),l=el(e),i=s(1);t&&(n?U(n)&&(i=ei(n)):i=ei(e));let u=(void 0===(o=r)&&(o=!1),n&&(!o||n===k(l))&&o)?es(l):s(0),c=(a.left+u.x)/i.x,d=(a.top+u.y)/i.y,f=a.width/i.x,p=a.height/i.y;if(l){let e=k(l),t=n&&U(n)?k(n):n,r=e,o=eo(r);for(;o&&n&&t!==r;){let e=ei(o),t=o.getBoundingClientRect(),n=ee(o),a=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=a,d+=l,o=eo(r=k(o))}}return j({width:f,height:p,x:c,y:d})}function ed(e,t){let r=et(e).scrollLeft;return t?t.left+r:ec(L(e)).left+r}function ef(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:ed(e,n)),y:n.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=k(e),n=L(e),o=r.visualViewport,a=n.clientWidth,l=n.clientHeight,i=0,u=0;if(o){a=o.width,l=o.height;let e=Z();(!e||e&&"fixed"===t)&&(i=o.offsetLeft,u=o.offsetTop)}return{width:a,height:l,x:i,y:u}}(e,r);else if("document"===t)n=function(e){let t=L(e),r=et(e),n=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=l(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+ed(e),u=-r.scrollTop;return"rtl"===ee(n).direction&&(i+=l(t.clientWidth,n.clientWidth)-o),{width:o,height:a,x:i,y:u}}(L(e));else if(U(t))n=function(e,t){let r=ec(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,a=H(e)?ei(e):s(1),l=e.clientWidth*a.x;return{width:l,height:e.clientHeight*a.y,x:o*a.x,y:n*a.y}}(t,r);else{let r=es(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return j(n)}function em(e){return"static"===ee(e).position}function eg(e,t){if(!H(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let r=e.offsetParent;return L(e)===r&&(r=r.ownerDocument.body),r}function ey(e,t){var r;let n=k(e);if(V(e))return n;if(!H(e)){let t=er(e);for(;t&&!J(t);){if(U(t)&&!em(t))return t;t=er(t)}return n}let o=eg(e,t);for(;o&&(r=o,B.has(D(r)))&&em(o);)o=eg(o,t);return o&&J(o)&&em(o)&&!q(o)?n:o||function(e){let t=er(e);for(;H(t)&&!J(t);){if(q(t))return t;if(V(t))break;t=er(t)}return null}(e)||n}let ev=async function(e){let t=this.getOffsetParent||ey,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=H(t),o=L(t),a="fixed"===r,l=ec(e,!0,a,t),i={scrollLeft:0,scrollTop:0},u=s(0);if(n||!n&&!a){if(("body"!==D(t)||G(o))&&(i=et(t)),n){let e=ec(t,!0,a,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ed(o))}a&&!n&&o&&(u.x=ed(o));let c=!o||n||a?s(0):ef(o,i);return{x:l.left+i.scrollLeft-u.x-c.x,y:l.top+i.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,a="fixed"===o,l=L(n),i=!!t&&V(t.floating);if(n===l||i&&a)return r;let u={scrollLeft:0,scrollTop:0},c=s(1),d=s(0),f=H(n);if((f||!f&&!a)&&(("body"!==D(n)||G(l))&&(u=et(n)),H(n))){let e=ec(n);c=ei(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let p=!l||f||a?s(0):ef(l,u,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-u.scrollLeft*c.x+d.x+p.x,y:r.y*c.y-u.scrollTop*c.y+d.y+p.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?V(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=en(e,[],!1).filter(e=>U(e)&&"body"!==D(e)),o=null,a="fixed"===ee(e).position,l=a?er(e):e;for(;U(l)&&!J(l);){let t=ee(l),r=q(l);r||"fixed"!==t.position||(o=null),(a?!r&&!o:!r&&"static"===t.position&&!!o&&ep.has(o.position)||G(l)&&!r&&function e(t,r){let n=er(t);return!(n===r||!U(n)||J(n))&&("fixed"===ee(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=er(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],u=i[0],s=i.reduce((e,r)=>{let n=eh(t,r,o);return e.top=l(n.top,e.top),e.right=a(n.right,e.right),e.bottom=a(n.bottom,e.bottom),e.left=l(n.left,e.left),e},eh(t,u,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ey,getElementRects:ev,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ea(e);return{width:t,height:r}},getScale:ei,isElement:U,isRTL:function(e){return"rtl"===ee(e).direction}};function e_(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ew=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:u,elements:s,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=P(p),b={x:r,y:n},_=m(v(o)),w=g(_),x=await u.getDimensions(d),R="y"===_,E=R?"clientHeight":"clientWidth",j=i.reference[w]+i.reference[_]-b[_]-i.floating[w],S=b[_]-i.reference[_],O=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),T=O?O[E]:0;T&&await (null==u.isElement?void 0:u.isElement(O))||(T=s.floating[E]||i.floating[w]);let M=T/2-x[w]/2-1,C=a(y[R?"top":"left"],M),A=a(y[R?"bottom":"right"],M),N=T-x[w]-A,I=T/2-x[w]/2+(j/2-S/2),D=l(C,a(I,N)),k=!c.arrow&&null!=h(o)&&I!==D&&i.reference[w]/2-(I<C?C:A)-x[w]/2<0,L=k?I<C?I-C:I-N:0;return{[_]:b[_]+L,data:{[_]:D,centerOffset:I-D-L,...k&&{alignmentOffset:L}},reset:k}}}),ex=(e,t,r)=>{let n=new Map,o={platform:eb,...r},a={...o.platform,_c:n};return O(e,t,{...o,platform:a})};var eR=r(962),eE="undefined"!=typeof document?n.useLayoutEffect:function(){};function eP(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eP(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!eP(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ej(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eS(e,t){let r=ej(e);return Math.round(t*r)/r}function eO(e){let t=n.useRef(e);return eE(()=>{t.current=e}),t}let eT=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ew({element:r.current,padding:n}).fn(t):{}:r?ew({element:r,padding:n}).fn(t):{}}}),eM=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:a,placement:l,middlewareData:i}=t,u=await N(t,e);return l===(null==(r=i.offset)?void 0:r.placement)&&null!=(n=i.arrow)&&n.alignmentOffset?{}:{x:o+u.x,y:a+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eC=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=f(e,t),d={x:r,y:n},h=await T(t,c),g=v(p(o)),y=m(g),b=d[y],_=d[g];if(i){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",r=b+h[e],n=b-h[t];b=l(r,a(b,n))}if(u){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",r=_+h[e],n=_-h[t];_=l(r,a(_,n))}let w=s.fn({...t,[y]:b,[g]:_});return{...w,data:{x:w.x-r,y:w.y-n,enabled:{[y]:i,[g]:u}}}}}}(e),options:[e,t]}),eA=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:a,middlewareData:l}=t,{offset:i=0,mainAxis:u=!0,crossAxis:s=!0}=f(e,t),c={x:r,y:n},d=v(o),h=m(d),g=c[h],y=c[d],b=f(i,t),_="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(u){let e="y"===h?"height":"width",t=a.reference[h]-a.floating[e]+_.mainAxis,r=a.reference[h]+a.reference[e]-_.mainAxis;g<t?g=t:g>r&&(g=r)}if(s){var w,x;let e="y"===h?"width":"height",t=A.has(p(o)),r=a.reference[d]-a.floating[e]+(t&&(null==(w=l.offset)?void 0:w[d])||0)+(t?0:_.crossAxis),n=a.reference[d]+a.reference[e]+(t?0:(null==(x=l.offset)?void 0:x[d])||0)-(t?_.crossAxis:0);y<r?y=r:y>n&&(y=n)}return{[h]:g,[d]:y}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,a,l;let{placement:i,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:y}=t,{mainAxis:P=!0,crossAxis:j=!0,fallbackPlacements:S,fallbackStrategy:O="bestFit",fallbackAxisSideDirection:M="none",flipAlignment:C=!0,...A}=f(e,t);if(null!=(r=u.arrow)&&r.alignmentOffset)return{};let N=p(i),I=v(c),D=p(c)===c,k=await (null==d.isRTL?void 0:d.isRTL(y.floating)),L=S||(D||!C?[E(c)]:function(e){let t=E(e);return[b(e),t,b(t)]}(c)),F="none"!==M;!S&&F&&L.push(...function(e,t,r,n){let o=h(e),a=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?w:_;return t?_:w;case"left":case"right":return t?x:R;default:return[]}}(p(e),"start"===r,n);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(b)))),a}(c,C,M,k));let U=[c,...L],H=await T(t,A),W=[],z=(null==(n=u.flip)?void 0:n.overflows)||[];if(P&&W.push(H[N]),j){let e=function(e,t,r){void 0===r&&(r=!1);let n=h(e),o=m(v(e)),a=g(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(l=E(l)),[l,E(l)]}(i,s,k);W.push(H[e[0]],H[e[1]])}if(z=[...z,{placement:i,overflows:W}],!W.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=U[e];if(t&&(!("alignment"===j&&I!==v(t))||z.every(e=>v(e.placement)!==I||e.overflows[0]>0)))return{data:{index:e,overflows:z},reset:{placement:t}};let r=null==(a=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(O){case"bestFit":{let e=null==(l=z.filter(e=>{if(F){let t=v(e.placement);return t===I||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=c}if(i!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eI=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i;let{placement:u,rects:s,platform:c,elements:d}=t,{apply:m=()=>{},...g}=f(e,t),y=await T(t,g),b=p(u),_=h(u),w="y"===v(u),{width:x,height:R}=s.floating;"top"===b||"bottom"===b?(o=b,i=_===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(i=b,o="end"===_?"top":"bottom");let E=R-y.top-y.bottom,P=x-y.left-y.right,j=a(R-y[o],E),S=a(x-y[i],P),O=!t.middlewareData.shift,M=j,C=S;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(C=P),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(M=E),O&&!_){let e=l(y.left,0),t=l(y.right,0),r=l(y.top,0),n=l(y.bottom,0);w?C=x-2*(0!==e||0!==t?e+t:l(y.left,y.right)):M=R-2*(0!==r||0!==n?r+n:l(y.top,y.bottom))}await m({...t,availableWidth:C,availableHeight:M});let A=await c.getDimensions(d.floating);return x!==A.width||R!==A.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=f(e,t);switch(n){case"referenceHidden":{let e=M(await T(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:C(e)}}}case"escaped":{let e=M(await T(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:C(e)}}}default:return{}}}}}(e),options:[e,t]}),ek=(e,t)=>({...eT(e),options:[e,t]});var eL=r(5226),eF=r(326),eU=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...a}=e;return(0,eF.jsx)(eL.WV.svg,{...a,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eU.displayName="Arrow";var eH=r(8051),eW=r(3095),ez=r(5049),eG=r(5819),eB="Popper",[eK,eV]=(0,eW.b)(eB),[eX,e$]=eK(eB),eY=e=>{let{__scopePopper:t,children:r}=e,[o,a]=n.useState(null);return(0,eF.jsx)(eX,{scope:t,anchor:o,onAnchorChange:a,children:r})};eY.displayName=eB;var eq="PopperAnchor",eZ=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...a}=e,l=e$(eq,r),i=n.useRef(null),u=(0,eH.e)(t,i);return n.useEffect(()=>{l.onAnchorChange(o?.current||i.current)}),o?null:(0,eF.jsx)(eL.WV.div,{...a,ref:u})});eZ.displayName=eq;var eQ="PopperContent",[eJ,e0]=eK(eQ),e1=n.forwardRef((e,t)=>{let{__scopePopper:r,side:o="bottom",sideOffset:i=0,align:s="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:y="optimized",onPlaced:v,...b}=e,_=e$(eQ,r),[w,x]=n.useState(null),R=(0,eH.e)(t,e=>x(e)),[E,P]=n.useState(null),j=function(e){let[t,r]=n.useState(void 0);return(0,eG.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(E),S=j?.width??0,O=j?.height??0,T="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(p)?p:[p],C=M.length>0,A={padding:T,boundary:M.filter(e6),altBoundary:C},{refs:N,floatingStyles:I,placement:D,isPositioned:k,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:a,elements:{reference:l,floating:i}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=n.useState(o);eP(p,o)||h(o);let[m,g]=n.useState(null),[y,v]=n.useState(null),b=n.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),_=n.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),w=l||m,x=i||y,R=n.useRef(null),E=n.useRef(null),P=n.useRef(d),j=null!=s,S=eO(s),O=eO(a),T=eO(c),M=n.useCallback(()=>{if(!R.current||!E.current)return;let e={placement:t,strategy:r,middleware:p};O.current&&(e.platform=O.current),ex(R.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};C.current&&!eP(P.current,t)&&(P.current=t,eR.flushSync(()=>{f(t)}))})},[p,t,r,O,T]);eE(()=>{!1===c&&P.current.isPositioned&&(P.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let C=n.useRef(!1);eE(()=>(C.current=!0,()=>{C.current=!1}),[]),eE(()=>{if(w&&(R.current=w),x&&(E.current=x),w&&x){if(S.current)return S.current(w,x,M);M()}},[w,x,M,S,j]);let A=n.useMemo(()=>({reference:R,floating:E,setReference:b,setFloating:_}),[b,_]),N=n.useMemo(()=>({reference:w,floating:x}),[w,x]),I=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!N.floating)return e;let t=eS(N.floating,d.x),n=eS(N.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+n+"px)",...ej(N.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,u,N.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:M,refs:A,elements:N,floatingStyles:I}),[d,M,A,N,I])}({strategy:"fixed",placement:o+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,p=el(e),h=i||s?[...p?en(p):[],...en(t)]:[];h.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),s&&e.addEventListener("resize",r)});let m=p&&d?function(e,t){let r,n=null,o=L(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function s(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),i();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=f;if(c||t(),!m||!g)return;let y=u(h),v=u(o.clientWidth-(p+m)),b={rootMargin:-y+"px "+-v+"px "+-u(o.clientHeight-(h+g))+"px "+-u(p)+"px",threshold:l(0,a(1,d))||1},_=!0;function w(t){let n=t[0].intersectionRatio;if(n!==d){if(!_)return s();n?s(!1,n):r=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==n||e_(f,e.getBoundingClientRect())||s(),_=!1}try{n=new IntersectionObserver(w,{...b,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(w,b)}n.observe(e)}(!0),i}(p,r):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),r()}),p&&!f&&y.observe(p),y.observe(t));let v=f?ec(e):null;return f&&function t(){let n=ec(e);v&&!e_(v,n)&&r(),v=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;h.forEach(e=>{i&&e.removeEventListener("scroll",r),s&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:_.anchor},middleware:[eM({mainAxis:i+O,alignmentAxis:c}),f&&eC({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eA():void 0,...A}),f&&eN({...A}),eI({...A,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:a}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${a}px`)}}),E&&ek({element:E,padding:d}),e3({arrowWidth:S,arrowHeight:O}),g&&eD({strategy:"referenceHidden",...A})]}),[U,H]=e4(D),W=(0,ez.W)(v);(0,eG.b)(()=>{k&&W?.()},[k,W]);let z=F.arrow?.x,G=F.arrow?.y,B=F.arrow?.centerOffset!==0,[K,V]=n.useState();return(0,eG.b)(()=>{w&&V(window.getComputedStyle(w).zIndex)},[w]),(0,eF.jsx)("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:k?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(eJ,{scope:r,placedSide:U,onArrowChange:P,arrowX:z,arrowY:G,shouldHideArrow:B,children:(0,eF.jsx)(eL.WV.div,{"data-side":U,"data-align":H,...b,ref:R,style:{...b.style,animation:k?void 0:"none"}})})})});e1.displayName=eQ;var e2="PopperArrow",e7={top:"bottom",right:"left",bottom:"top",left:"right"},e5=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=e0(e2,r),a=e7[o.placedSide];return(0,eF.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eU,{...n,ref:t,style:{...n.style,display:"block"}})})});function e6(e){return null!==e}e5.displayName=e2;var e3=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,a=o.arrow?.centerOffset!==0,l=a?0:e.arrowWidth,i=a?0:e.arrowHeight,[u,s]=e4(r),c={start:"0%",center:"50%",end:"100%"}[s],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+i/2,p="",h="";return"bottom"===u?(p=a?c:`${d}px`,h=`${-i}px`):"top"===u?(p=a?c:`${d}px`,h=`${n.floating.height+i}px`):"right"===u?(p=`${-i}px`,h=a?c:`${f}px`):"left"===u&&(p=`${n.floating.width+i}px`,h=a?c:`${f}px`),{data:{x:p,y:h}}}});function e4(e){let[t,r="center"]=e.split("-");return[t,r]}var e8=eY,e9=eZ,te=e1,tt=e5},3078:(e,t,r)=>{"use strict";r.d(t,{h:()=>u});var n=r(7577),o=r(962),a=r(5226),l=r(5819),i=r(326),u=n.forwardRef((e,t)=>{let{container:r,...u}=e,[s,c]=n.useState(!1);(0,l.b)(()=>c(!0),[]);let d=r||s&&globalThis?.document?.body;return d?o.createPortal((0,i.jsx)(a.WV.div,{...u,ref:t}),d):null});u.displayName="Portal"},9815:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var n=r(7577),o=r(8051),a=r(5819),l=e=>{let{present:t,children:r}=e,l=function(e){var t,r;let[o,l]=n.useState(),u=n.useRef(null),s=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=i(u.current);c.current="mounted"===d?e:"none"},[d]),(0,a.b)(()=>{let t=u.current,r=s.current;if(r!==e){let n=c.current,o=i(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=i(u.current).includes(r.animationName);if(r.target===o&&n&&(f("ANIMATION_END"),!s.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(c.current=i(u.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof r?r({present:l.isPresent}):n.Children.only(r),s=(0,o.e)(l.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof r||l.isPresent?n.cloneElement(u,{ref:s}):null};function i(e){return e?.animationName||"none"}l.displayName="Presence"},5226:(e,t,r)=>{"use strict";r.d(t,{WV:()=>i,jH:()=>u});var n=r(7577),o=r(962),a=r(4214),l=r(326),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e,i=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5594:(e,t,r)=>{"use strict";r.d(t,{Pc:()=>w,ck:()=>C,fC:()=>M});var n=r(7577),o=r(2561),a=r(545),l=r(8051),i=r(3095),u=r(8957),s=r(5226),c=r(5049),d=r(2067),f=r(7124),p=r(326),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[y,v,b]=(0,a.B)(g),[_,w]=(0,i.b)(g,[b]),[x,R]=_(g),E=n.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(P,{...e,ref:t})})}));E.displayName=g;var P=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:u,currentTabStopId:y,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:_,onEntryFocus:w,preventScrollOnEntryFocus:R=!1,...E}=e,P=n.useRef(null),j=(0,l.e)(t,P),S=(0,f.gm)(u),[O,M]=(0,d.T)({prop:y,defaultProp:b??null,onChange:_,caller:g}),[C,A]=n.useState(!1),N=(0,c.W)(w),I=v(r),D=n.useRef(!1),[k,L]=n.useState(0);return n.useEffect(()=>{let e=P.current;if(e)return e.addEventListener(h,N),()=>e.removeEventListener(h,N)},[N]),(0,p.jsx)(x,{scope:r,orientation:a,dir:S,loop:i,currentTabStopId:O,onItemFocus:n.useCallback(e=>M(e),[M]),onItemShiftTab:n.useCallback(()=>A(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,p.jsx)(s.WV.div,{tabIndex:C||0===k?-1:0,"data-orientation":a,...E,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=I().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===O),...e].filter(Boolean).map(e=>e.ref.current),R)}}D.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>A(!1))})})}),j="RovingFocusGroupItem",S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:l=!1,tabStopId:i,children:c,...d}=e,f=(0,u.M)(),h=i||f,m=R(j,r),g=m.currentTabStopId===h,b=v(r),{onFocusableItemAdd:_,onFocusableItemRemove:w,currentTabStopId:x}=m;return n.useEffect(()=>{if(a)return _(),()=>w()},[a,_,w]),(0,p.jsx)(y.ItemSlot,{scope:r,id:h,focusable:a,active:l,children:(0,p.jsx)(s.WV.span,{tabIndex:g?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return O[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>T(r))}}),children:"function"==typeof c?c({isCurrentTabStop:g,hasTabStop:null!=x}):c})})});S.displayName=j;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var M=E,C=S},4214:(e,t,r)=>{"use strict";r.d(t,{Z8:()=>l,g7:()=>i,sA:()=>s});var n=r(7577),o=r(8051),a=r(326);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,u=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{let t=a(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.F)(t,i):i),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,i=n.Children.toArray(o),u=i.find(c);if(u){let e=u.props.children,o=i.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),u=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},321:(e,t,r)=>{"use strict";r.d(t,{Dx:()=>J,aU:()=>et,dk:()=>ee,fC:()=>Q,l_:()=>Z,x8:()=>er,zt:()=>q});var n=r(7577),o=r(962),a=r(2561),l=r(8051),i=r(545),u=r(3095),s=r(825),c=r(3078),d=r(9815),f=r(5226),p=r(5049),h=r(2067),m=r(5819),g=r(6009),y=r(326),v="ToastProvider",[b,_,w]=(0,i.B)("Toast"),[x,R]=(0,u.b)("Toast",[w]),[E,P]=x(v),j=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:l=50,children:i}=e,[u,s]=n.useState(null),[c,d]=n.useState(0),f=n.useRef(!1),p=n.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${v}\`. Expected non-empty \`string\`.`),(0,y.jsx)(b.Provider,{scope:t,children:(0,y.jsx)(E,{scope:t,label:r,duration:o,swipeDirection:a,swipeThreshold:l,toastCount:c,viewport:u,onViewportChange:s,onToastAdd:n.useCallback(()=>d(e=>e+1),[]),onToastRemove:n.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:i})})};j.displayName=v;var S="ToastViewport",O=["F8"],T="toast.viewportPause",M="toast.viewportResume",C=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=O,label:a="Notifications ({hotkey})",...i}=e,u=P(S,r),c=_(r),d=n.useRef(null),p=n.useRef(null),h=n.useRef(null),m=n.useRef(null),g=(0,l.e)(t,m,u.onViewportChange),v=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=u.toastCount>0;n.useEffect(()=>{let e=e=>{0!==o.length&&o.every(t=>e[t]||e.code===t)&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{let e=d.current,t=m.current;if(w&&e&&t){let r=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(T);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},n=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(M);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},a=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",a),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",a),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[w,u.isClosePausedRef]);let x=n.useCallback(({tabbingDirection:e})=>{let t=c().map(t=>{let r=t.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[c]);return n.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){let r=document.activeElement,n=t.shiftKey;if(t.target===e&&n){p.current?.focus();return}let o=x({tabbingDirection:n?"backwards":"forwards"}),a=o.findIndex(e=>e===r);Y(o.slice(a+1))?t.preventDefault():n?p.current?.focus():h.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,x]),(0,y.jsxs)(s.I0,{ref:d,role:"region","aria-label":a.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&(0,y.jsx)(N,{ref:p,onFocusFromOutsideViewport:()=>{Y(x({tabbingDirection:"forwards"}))}}),(0,y.jsx)(b.Slot,{scope:r,children:(0,y.jsx)(f.WV.ol,{tabIndex:-1,...i,ref:g})}),w&&(0,y.jsx)(N,{ref:h,onFocusFromOutsideViewport:()=>{Y(x({tabbingDirection:"backwards"}))}})]})});C.displayName=S;var A="ToastFocusProxy",N=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,a=P(A,r);return(0,y.jsx)(g.TX,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;a.viewport?.contains(t)||n()}})});N.displayName=A;var I="Toast",D=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:l,...i}=e,[u,s]=(0,h.T)({prop:n,defaultProp:o??!0,onChange:l,caller:I});return(0,y.jsx)(d.z,{present:r||u,children:(0,y.jsx)(F,{open:u,...i,ref:t,onClose:()=>s(!1),onPause:(0,p.W)(e.onPause),onResume:(0,p.W)(e.onResume),onSwipeStart:(0,a.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:(0,a.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),s(!1)})})})});D.displayName=I;var[k,L]=x(I,{onClose(){}}),F=n.forwardRef((e,t)=>{let{__scopeToast:r,type:i="foreground",duration:u,open:c,onClose:d,onEscapeKeyDown:h,onPause:m,onResume:g,onSwipeStart:v,onSwipeMove:_,onSwipeCancel:w,onSwipeEnd:x,...R}=e,E=P(I,r),[j,S]=n.useState(null),O=(0,l.e)(t,e=>S(e)),C=n.useRef(null),A=n.useRef(null),N=u||E.duration,D=n.useRef(0),L=n.useRef(N),F=n.useRef(0),{onToastAdd:H,onToastRemove:W}=E,z=(0,p.W)(()=>{j?.contains(document.activeElement)&&E.viewport?.focus(),d()}),G=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(F.current),D.current=new Date().getTime(),F.current=window.setTimeout(z,e))},[z]);n.useEffect(()=>{let e=E.viewport;if(e){let t=()=>{G(L.current),g?.()},r=()=>{let e=new Date().getTime()-D.current;L.current=L.current-e,window.clearTimeout(F.current),m?.()};return e.addEventListener(T,r),e.addEventListener(M,t),()=>{e.removeEventListener(T,r),e.removeEventListener(M,t)}}},[E.viewport,N,m,g,G]),n.useEffect(()=>{c&&!E.isClosePausedRef.current&&G(N)},[c,N,E.isClosePausedRef,G]),n.useEffect(()=>(H(),()=>W()),[H,W]);let B=n.useMemo(()=>j?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(j):null,[j]);return E.viewport?(0,y.jsxs)(y.Fragment,{children:[B&&(0,y.jsx)(U,{__scopeToast:r,role:"status","aria-live":"foreground"===i?"assertive":"polite","aria-atomic":!0,children:B}),(0,y.jsx)(k,{scope:r,onClose:z,children:o.createPortal((0,y.jsx)(b.ItemSlot,{scope:r,children:(0,y.jsx)(s.fC,{asChild:!0,onEscapeKeyDown:(0,a.M)(h,()=>{E.isFocusedToastEscapeKeyDownRef.current||z(),E.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,y.jsx)(f.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":E.swipeDirection,...R,ref:O,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.M)(e.onKeyDown,e=>{"Escape"!==e.key||(h?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(E.isFocusedToastEscapeKeyDownRef.current=!0,z()))}),onPointerDown:(0,a.M)(e.onPointerDown,e=>{0===e.button&&(C.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.M)(e.onPointerMove,e=>{if(!C.current)return;let t=e.clientX-C.current.x,r=e.clientY-C.current.y,n=!!A.current,o=["left","right"].includes(E.swipeDirection),a=["left","up"].includes(E.swipeDirection)?Math.min:Math.max,l=o?a(0,t):0,i=o?0:a(0,r),u="touch"===e.pointerType?10:2,s={x:l,y:i},c={originalEvent:e,delta:s};n?(A.current=s,X("toast.swipeMove",_,c,{discrete:!1})):$(s,E.swipeDirection,u)?(A.current=s,X("toast.swipeStart",v,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(r)>u)&&(C.current=null)}),onPointerUp:(0,a.M)(e.onPointerUp,e=>{let t=A.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),A.current=null,C.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};$(t,E.swipeDirection,E.swipeThreshold)?X("toast.swipeEnd",x,n,{discrete:!0}):X("toast.swipeCancel",w,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),E.viewport)})]}):null}),U=e=>{let{__scopeToast:t,children:r,...o}=e,a=P(I,t),[l,i]=n.useState(!1),[u,s]=n.useState(!1);return function(e=()=>{}){let t=(0,p.W)(e);(0,m.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>i(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>s(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,y.jsx)(c.h,{asChild:!0,children:(0,y.jsx)(g.TX,{...o,children:l&&(0,y.jsxs)(y.Fragment,{children:[a.label," ",r]})})})},H=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(f.WV.div,{...n,ref:t})});H.displayName="ToastTitle";var W=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(f.WV.div,{...n,ref:t})});W.displayName="ToastDescription";var z="ToastAction",G=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,y.jsx)(V,{altText:r,asChild:!0,children:(0,y.jsx)(K,{...n,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${z}\`. Expected non-empty \`string\`.`),null)});G.displayName=z;var B="ToastClose",K=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=L(B,r);return(0,y.jsx)(V,{asChild:!0,children:(0,y.jsx)(f.WV.button,{type:"button",...n,ref:t,onClick:(0,a.M)(e.onClick,o.onClose)})})});K.displayName=B;var V=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,y.jsx)(f.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function X(e,t,r,{discrete:n}){let o=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,f.jH)(o,a):o.dispatchEvent(a)}var $=(e,t,r=0)=>{let n=Math.abs(e.x),o=Math.abs(e.y),a=n>o;return"left"===t||"right"===t?a&&n>r:!a&&o>r};function Y(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var q=j,Z=C,Q=D,J=H,ee=W,et=G,er=K},5049:(e,t,r)=>{"use strict";r.d(t,{W:()=>o});var n=r(7577);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},2067:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var n,o=r(7577),a=r(5819),l=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.b;function i({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,i,u]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),a=o.useRef(r),i=o.useRef(t);return l(()=>{i.current=t},[t]),o.useEffect(()=>{a.current!==r&&(i.current?.(r),a.current=r)},[r,a]),[r,n,i]}({defaultProp:t,onChange:r}),s=void 0!==e,c=s?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,n])}return[c,o.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&u.current?.(r)}else i(t)},[s,e,i,u])]}Symbol("RADIX:SYNC_STATE")},5819:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var n=r(7577),o=globalThis?.document?n.useLayoutEffect:()=>{}},6009:(e,t,r)=>{"use strict";r.d(t,{C2:()=>l,TX:()=>i,fC:()=>u});var n=r(7577),o=r(5226),a=r(326),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=n.forwardRef((e,t)=>(0,a.jsx)(o.WV.span,{...e,ref:t,style:{...l,...e.style}}));i.displayName="VisuallyHidden";var u=i},8285:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n,_class_private_field_loose_base:()=>n})},8817:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>o,_class_private_field_loose_key:()=>o});var n=0;function o(e){return"__private_"+n+++"_"+e}},1174:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})},8374:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function o(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=a?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(o,l,i):o[l]=e[l]}return o.default=e,r&&r.set(e,o),o}r.r(t),r.d(t,{_:()=>o,_interop_require_wildcard:()=>o})},9360:(e,t,r)=>{"use strict";r.d(t,{j:()=>l});var n=r(1135);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.W,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,u=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let a=o(t)||o(n);return l[e][a]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,u,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...s}[t]):({...i,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},1135:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{W:()=>n})},1009:(e,t,r)=>{"use strict";r.d(t,{m6:()=>Y});let n=e=>{let t=i(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||l(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),a=n?o(e.slice(1),n):void 0;if(a)return a;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},a=/^\[(.+)\]$/,l=e=>{if(a.test(e)){let t=a.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},i=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{u(r,n,e,t)}),n},u=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:s(t,e)).classGroupId=r;return}if("function"==typeof e){if(c(e)){u(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{u(o,s(t,e),r,n)})})},s=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],a=t.length,l=e=>{let r;let l=[],i=0,u=0;for(let s=0;s<e.length;s++){let c=e[s];if(0===i){if(c===o&&(n||e.slice(s,s+a)===t)){l.push(e.slice(u,s)),u=s+a;continue}if("/"===c){r=s;continue}}"["===c?i++:"]"===c&&i--}let s=0===l.length?e:e.substring(u),c=s.startsWith("!"),d=c?s.substring(1):s;return{modifiers:l,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>u?r-u:void 0}};return r?e=>r({className:e,parseClassName:l}):l},h=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},m=e=>({cache:f(e.cacheSize),parseClassName:p(e),...n(e)}),g=/\s+/,y=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,a=[],l=e.trim().split(g),i="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{modifiers:u,hasImportantModifier:s,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,p=n(f?c.substring(0,d):c);if(!p){if(!f||!(p=n(c))){i=t+(i.length>0?" "+i:i);continue}f=!1}let m=h(u).join(":"),g=s?m+"!":m,y=g+p;if(a.includes(y))continue;a.push(y);let v=o(p,f);for(let e=0;e<v.length;++e){let t=v[e];a.push(g+t)}i=t+(i.length>0?" "+i:i)}return i};function v(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},_=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,x=/^\d+\/\d+$/,R=new Set(["px","full","screen"]),E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,P=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,j=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,T=e=>C(e)||R.has(e)||x.test(e),M=e=>B(e,"length",K),C=e=>!!e&&!Number.isNaN(Number(e)),A=e=>B(e,"number",C),N=e=>!!e&&Number.isInteger(Number(e)),I=e=>e.endsWith("%")&&C(e.slice(0,-1)),D=e=>w.test(e),k=e=>E.test(e),L=new Set(["length","size","percentage"]),F=e=>B(e,L,V),U=e=>B(e,"position",V),H=new Set(["image","url"]),W=e=>B(e,H,$),z=e=>B(e,"",X),G=()=>!0,B=(e,t,r)=>{let n=w.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},K=e=>P.test(e)&&!j.test(e),V=()=>!1,X=e=>S.test(e),$=e=>O.test(e);Symbol.toStringTag;let Y=function(e,...t){let r,n,o;let a=function(i){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,a=l,l(i)};function l(e){let t=n(e);if(t)return t;let a=y(e,r);return o(e,a),a}return function(){return a(v.apply(null,arguments))}}(()=>{let e=_("colors"),t=_("spacing"),r=_("blur"),n=_("brightness"),o=_("borderColor"),a=_("borderRadius"),l=_("borderSpacing"),i=_("borderWidth"),u=_("contrast"),s=_("grayscale"),c=_("hueRotate"),d=_("invert"),f=_("gap"),p=_("gradientColorStops"),h=_("gradientColorStopPositions"),m=_("inset"),g=_("margin"),y=_("opacity"),v=_("padding"),b=_("saturate"),w=_("scale"),x=_("sepia"),R=_("skew"),E=_("space"),P=_("translate"),j=()=>["auto","contain","none"],S=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto",D,t],L=()=>[D,t],H=()=>["",T,M],B=()=>["auto",C,D],K=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],V=()=>["solid","dashed","dotted","double","none"],X=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],$=()=>["start","end","center","between","around","evenly","stretch"],Y=()=>["","0",D],q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[C,D];return{cacheSize:500,separator:":",theme:{colors:[G],spacing:[T,M],blur:["none","",k,D],brightness:Z(),borderColor:[e],borderRadius:["none","","full",k,D],borderSpacing:L(),borderWidth:H(),contrast:Z(),grayscale:Y(),hueRotate:Z(),invert:Y(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[I,M],inset:O(),margin:O(),opacity:Z(),padding:L(),saturate:Z(),scale:Z(),sepia:Y(),skew:Z(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",D]}],container:["container"],columns:[{columns:[k]}],"break-after":[{"break-after":q()}],"break-before":[{"break-before":q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...K(),D]}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",N,D]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",D]}],grow:[{grow:Y()}],shrink:[{shrink:Y()}],order:[{order:["first","last","none",N,D]}],"grid-cols":[{"grid-cols":[G]}],"col-start-end":[{col:["auto",{span:["full",N,D]},D]}],"col-start":[{"col-start":B()}],"col-end":[{"col-end":B()}],"grid-rows":[{"grid-rows":[G]}],"row-start-end":[{row:["auto",{span:[N,D]},D]}],"row-start":[{"row-start":B()}],"row-end":[{"row-end":B()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",D]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",D]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...$()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...$(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...$(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",D,t]}],"min-w":[{"min-w":[D,t,"min","max","fit"]}],"max-w":[{"max-w":[D,t,"none","full","min","max","fit","prose",{screen:[k]},k]}],h:[{h:[D,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[D,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[D,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[D,t,"auto","min","max","fit"]}],"font-size":[{text:["base",k,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",A]}],"font-family":[{font:[G]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",D]}],"line-clamp":[{"line-clamp":["none",C,A]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",T,D]}],"list-image":[{"list-image":["none",D]}],"list-style-type":[{list:["none","disc","decimal",D]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...V(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",T,M]}],"underline-offset":[{"underline-offset":["auto",T,D]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",D]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",D]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...K(),U]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},W]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[h]}],"gradient-via-pos":[{via:[h]}],"gradient-to-pos":[{to:[h]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...V(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:V()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...V()]}],"outline-offset":[{"outline-offset":[T,D]}],"outline-w":[{outline:[T,M]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[T,M]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",k,z]}],"shadow-color":[{shadow:[G]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...X(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":X()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",k,D]}],grayscale:[{grayscale:[s]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",D]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",D]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",D]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[N,D]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[R]}],"skew-y":[{"skew-y":[R]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",D]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",D]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",D]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[T,M,A]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},3370:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})}};