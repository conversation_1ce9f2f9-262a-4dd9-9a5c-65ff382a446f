<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/bc19b31da0bd50c9.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-1b400f1de436fe28.js"/><script src="/_next/static/chunks/fd9d1056-346a4d4db4c146e1.js" async=""></script><script src="/_next/static/chunks/117-4ced7f814a77fa18.js" async=""></script><script src="/_next/static/chunks/main-app-31faf2160eb37e61.js" async=""></script><script src="/_next/static/chunks/438-3d6b98e82ab59c7b.js" async=""></script><script src="/_next/static/chunks/52-e89dc12467f890af.js" async=""></script><script src="/_next/static/chunks/963-40a18a252de9e9a5.js" async=""></script><script src="/_next/static/chunks/672-2b366b7389252487.js" async=""></script><script src="/_next/static/chunks/app/popular-combos/page-41e306f0392622f2.js" async=""></script><script src="/_next/static/chunks/367-70ef8c60d69865f7.js" async=""></script><script src="/_next/static/chunks/781-3944b942f49751e0.js" async=""></script><script src="/_next/static/chunks/435-dc8290f2b1c966aa.js" async=""></script><script src="/_next/static/chunks/2-bcbe5cd3b8eae62a.js" async=""></script><script src="/_next/static/chunks/app/layout-0af87e3f0fffa538.js" async=""></script><script src="/_next/static/chunks/150-dde69c0d40954ea8.js" async=""></script><script src="/_next/static/chunks/app/page-a448ec3e013efd3a.js" async=""></script><title>TikTok Emoji - Hidden Codes &amp; Popular Combinations</title><meta name="description" content="Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings. Copy with one click to make your TikTok comments more engaging!"/><meta name="author" content="TikTok Emoji Team"/><meta name="keywords" content="TikTok emoji,hidden emoji codes,TikTok symbols,emoji combinations,TikTok comments,secret emojis"/><meta name="creator" content="TikTok Emoji"/><meta name="publisher" content="TikTok Emoji"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="google-site-verification" content="google-site-verification-code"/><meta property="og:title" content="TikTok Emoji - Hidden Codes &amp; Popular Combinations"/><meta property="og:description" content="Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings."/><meta property="og:url" content="https://tiktokemoji.com"/><meta property="og:site_name" content="TikTok Emoji"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="https://tiktokemoji.com/og-image.png"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="TikTok Emoji - Hidden Codes &amp; Popular Combinations"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="TikTok Emoji - Hidden Codes &amp; Popular Combinations"/><meta name="twitter:description" content="Discover 46+ TikTok hidden emoji codes and popular combinations"/><meta name="twitter:image" content="https://tiktokemoji.com/og-image.png"/><meta name="next-size-adjust"/><script type="application/ld+json">{"@context":"https://schema.org","@type":"WebSite","name":"TikTok Emoji","alternateName":"TikTok Hidden Emoji Codes","url":"https://tiktokemoji.com","description":"Discover TikTok hidden emoji codes, popular combinations, and special meanings","potentialAction":{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://tiktokemoji.com/search?q={search_term_string}"},"query-input":"required name=search_term_string"},"publisher":{"@type":"Organization","name":"TikTok Emoji","url":"https://tiktokemoji.com","logo":{"@type":"ImageObject","url":"https://tiktokemoji.com/logo.png","width":600,"height":60}},"sameAs":[]}</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"><div class="container flex h-16 items-center justify-between"><a class="flex items-center space-x-2 flex-shrink-0" href="/"><span class="text-2xl">🎵</span><span class="font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">TikTok Emoji</span></a><form class="relative flex-1 max-w-2xl mx-8"><input type="search" class="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-10 w-full" placeholder="Search emojis, codes, or meanings..." value=""/><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 absolute right-0 top-0" type="submit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search h-4 w-4"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg></button></form><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 gap-2 flex-shrink-0" type="button" id="radix-:Rqkq:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-grid3x3 h-4 w-4"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M3 15h18"></path><path d="M9 3v18"></path><path d="M15 3v18"></path></svg>Categories<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3 w-3"><path d="m6 9 6 6 6-6"></path></svg></button></div></header><main class="min-h-screen"><div class="container py-8"><a class="inline-flex items-center text-muted-foreground hover:text-foreground mb-6" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-4 h-4 mr-2"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>Back to Home</a><div class="mb-8"><h1 class="text-4xl font-bold mb-4 flex items-center gap-3">🔥 Popular Emoji Combinations</h1><p class="text-muted-foreground text-lg">Explore the most popular emoji combinations on TikTok to make your comments more engaging!</p></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm mb-8 border-tiktok-pink/20 bg-gradient-to-br from-pink-50/50 to-blue-50/50"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up w-5 h-5 text-tiktok-pink"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg>Today&#x27;s Viral Combos</h3><p class="text-sm text-muted-foreground">Combinations with surging usage in the last 24 hours</p></div><div class="p-6 pt-0"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"><div class="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm"><div class="flex items-center gap-3"><span class="text-2xl">👁👄👁</span><div><p class="font-medium text-sm">I&#x27;m watching / Shocked</p><p class="text-xs text-muted-foreground">Used when seeing something strange or shocking, reacting to unexpected content, or expressing disbelief</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><div class="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm"><div class="flex items-center gap-3"><span class="text-2xl">🥺👉👈</span><div><p class="font-medium text-sm">Shy / Pleading</p><p class="text-xs text-muted-foreground">Used when being cute or asking for help, showing vulnerability, or making a request</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><div class="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm"><div class="flex items-center gap-3"><span class="text-2xl">😭😭😭</span><div><p class="font-medium text-sm">Crying from laughter/emotion</p><p class="text-xs text-muted-foreground">Used when something is extremely funny or emotionally touching, expressing overwhelming feelings</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><div class="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm"><div class="flex items-center gap-3"><span class="text-2xl">💀💀💀</span><div><p class="font-medium text-sm">Dead from laughing x3</p><p class="text-xs text-muted-foreground">Extremely funny content, when something is so hilarious it &quot;kills&quot; you</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><div class="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm"><div class="flex items-center gap-3"><span class="text-2xl">🫠🫠🫠</span><div><p class="font-medium text-sm">Melting from embarrassment</p><p class="text-xs text-muted-foreground">Used when feeling extremely embarrassed, awkward, or overwhelmed by heat/stress</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><div class="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm"><div class="flex items-center gap-3"><span class="text-2xl">🙃💔</span><div><p class="font-medium text-sm">Fake smile hiding pain</p><p class="text-xs text-muted-foreground">Used when pretending to be okay while actually hurt, masking true emotions</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div></div></div><div dir="ltr" data-orientation="horizontal"><div role="tablist" aria-orientation="horizontal" class="h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground grid w-full max-w-md grid-cols-5" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:R4jtrkq:-content-all" data-state="active" id="radix-:R4jtrkq:-trigger-all" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">All</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R4jtrkq:-content-funny" data-state="inactive" id="radix-:R4jtrkq:-trigger-funny" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Funny</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R4jtrkq:-content-emotional" data-state="inactive" id="radix-:R4jtrkq:-trigger-emotional" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Emotional</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R4jtrkq:-content-reaction" data-state="inactive" id="radix-:R4jtrkq:-trigger-reaction" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Reaction</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R4jtrkq:-content-trending" data-state="inactive" id="radix-:R4jtrkq:-trigger-trending" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Trending</button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R4jtrkq:-trigger-all" id="radix-:R4jtrkq:-content-all" tabindex="0" class="ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-6" style="animation-duration:0s"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">👁👄👁</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">I&#x27;m watching / Shocked</h3><p class="text-sm text-muted-foreground mb-3">Used when seeing something strange or shocking, reacting to unexpected content, or expressing disbelief</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">👁👄👁 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🥺👉👈</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Shy / Pleading</h3><p class="text-sm text-muted-foreground mb-3">Used when being cute or asking for help, showing vulnerability, or making a request</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🥺👉👈 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">😭😭😭</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Crying from laughter/emotion</h3><p class="text-sm text-muted-foreground mb-3">Used when something is extremely funny or emotionally touching, expressing overwhelming feelings</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">😭😭😭 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">💀💀💀</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Dead from laughing x3</h3><p class="text-sm text-muted-foreground mb-3">Extremely funny content, when something is so hilarious it &quot;kills&quot; you</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">💀💀💀 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🫠🫠🫠</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Melting from embarrassment</h3><p class="text-sm text-muted-foreground mb-3">Used when feeling extremely embarrassed, awkward, or overwhelmed by heat/stress</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🫠🫠🫠 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🙃💔</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Fake smile hiding pain</h3><p class="text-sm text-muted-foreground mb-3">Used when pretending to be okay while actually hurt, masking true emotions</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🙃💔 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">😮‍💨😤</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">7<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Speechless/Frustrated</h3><p class="text-sm text-muted-foreground mb-3">Used when feeling exasperated, frustrated, or at a loss for words</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">😮‍💨😤 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🤡🎪</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Circus clown</h3><p class="text-sm text-muted-foreground mb-3">Self-deprecation or mocking absurd things, calling out foolish behavior</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🤡🎪 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">😈😈😈</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Mischievous thoughts</h3><p class="text-sm text-muted-foreground mb-3">Used when planning something playfully evil or having naughty thoughts</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">😈😈😈 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🥲✨</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">3<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Bittersweet happiness</h3><p class="text-sm text-muted-foreground mb-3">Used when feeling happy but with a touch of sadness, mixed emotions</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🥲✨ This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">✨💅✨</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Elegant / Refined</h3><p class="text-sm text-muted-foreground mb-3">Used when showing confidence or achievements, self-care moments, or feeling fabulous</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">✨💅✨ This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">👀☕</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">3<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Watching drama unfold</h3><p class="text-sm text-muted-foreground mb-3">Used when observing gossip or drama, being a spectator to interesting situations</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">👀☕ This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🗿🗿🗿</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Stone face deadpan reaction</h3><p class="text-sm text-muted-foreground mb-3">Used for dry, sarcastic, or unimpressed responses, showing disapproval</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🗿🗿🗿 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">💅🏻✨</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">5<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Self-care queen</h3><p class="text-sm text-muted-foreground mb-3">Used when focusing on self-care, being classy, or showing confidence</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">💅🏻✨ This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🫡🫡🫡</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Saluting (often sarcastic)</h3><p class="text-sm text-muted-foreground mb-3">Used to show respect or obedience, often with sarcastic undertones</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🫡🫡🫡 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🤪🤪🤪</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Going crazy/wild</h3><p class="text-sm text-muted-foreground mb-3">Used when feeling excited, acting silly, or going wild with enthusiasm</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🤪🤪🤪 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🫵💀</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">You&#x27;re dead to me</h3><p class="text-sm text-muted-foreground mb-3">Used when calling someone out or expressing mock anger/disappointment</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🫵💀 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">👑💎</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Queen energy</h3><p class="text-sm text-muted-foreground mb-3">Used when feeling royal, confident, or celebrating achievements</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">👑💎 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🧠🚫</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">No thoughts head empty</h3><p class="text-sm text-muted-foreground mb-3">Used when feeling mindless, confused, or having a brain fog moment</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🧠🚫 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🤌🏻✨</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">5<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Chef&#x27;s kiss perfection</h3><p class="text-sm text-muted-foreground mb-3">Used when something is absolutely perfect, showing appreciation for excellence</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🤌🏻✨ This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🏃‍♂️💨</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">7<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Gotta run</h3><p class="text-sm text-muted-foreground mb-3">Indicating the need to escape or leave quickly, avoiding awkward situations</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🏃‍♂️💨 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🔥🔥🔥</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">6<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Absolutely fire</h3><p class="text-sm text-muted-foreground mb-3">Used when something is extremely hot, trendy, or amazing</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🔥🔥🔥 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🎵🎶</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Music vibes</h3><p class="text-sm text-muted-foreground mb-3">Used when sharing music, feeling the rhythm, or expressing musical mood</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🎵🎶 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🌈🦄</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Magical/unique</h3><p class="text-sm text-muted-foreground mb-3">Used when something is special, magical, or one-of-a-kind</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🌈🦄 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">📸✨</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">3<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Photo ready</h3><p class="text-sm text-muted-foreground mb-3">Used when looking good for photos, sharing aesthetic content</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">📸✨ This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">💯🔥</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">100% fire</h3><p class="text-sm text-muted-foreground mb-3">Used when something is absolutely perfect and amazing</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">💯🔥 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🚀🌟</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Shooting for the stars</h3><p class="text-sm text-muted-foreground mb-3">Used when aiming high, achieving goals, or expressing ambition</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🚀🌟 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">⚡️💥</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Electric energy</h3><p class="text-sm text-muted-foreground mb-3">Used when feeling energetic, excited, or describing explosive content</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">⚡️💥 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🎯💯</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">4<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">On target perfection</h3><p class="text-sm text-muted-foreground mb-3">Used when hitting goals perfectly, being accurate, or achieving success</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🎯💯 This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow"><div class="p-6 pt-6"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-3"><span class="text-3xl">🌊🏄‍♀️</span><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">7<!-- --> emojis</div></div><h3 class="font-semibold text-lg mb-1">Riding the wave</h3><p class="text-sm text-muted-foreground mb-3">Used when going with the flow, catching trends, or enjoying the moment</p><div class="bg-muted rounded-md p-3 text-sm"><p class="text-muted-foreground mb-1">Example:</p><p class="italic">🌊🏄‍♀️ This is amazing!</p></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div></div></div></div><div class="mt-12 space-y-6"><h2 class="text-2xl font-bold">💡 Creative Combination Tips</h2><div class="grid md:grid-cols-2 gap-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="font-semibold tracking-tight text-lg">Repetition for Emphasis</h3></div><div class="p-6 pt-0"><p class="text-muted-foreground mb-3">Repeat the same emoji to strengthen emotional expression</p><div class="space-y-2"><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">😭😭😭</span><span class="text-sm text-muted-foreground">Super moved</span></div><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">🔥🔥🔥</span><span class="text-sm text-muted-foreground">So hot</span></div><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">💀💀💀</span><span class="text-sm text-muted-foreground">I&#x27;m dead</span></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="font-semibold tracking-tight text-lg">Progressive Expression</h3></div><div class="p-6 pt-0"><p class="text-muted-foreground mb-3">Use emojis of different intensities to show emotional progression</p><div class="space-y-2"><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">🙂😊😄</span><span class="text-sm text-muted-foreground">Getting happier</span></div><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">😢😭💔</span><span class="text-sm text-muted-foreground">Gradually heartbroken</span></div><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">😐😑😴</span><span class="text-sm text-muted-foreground">Getting sleepier</span></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="font-semibold tracking-tight text-lg">Storytelling Method</h3></div><div class="p-6 pt-0"><p class="text-muted-foreground mb-3">Use emoji combinations to tell a short story</p><div class="space-y-2"><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">👀➡️😱</span><span class="text-sm text-muted-foreground">Shocked after seeing</span></div><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">🏃‍♂️💨🏠</span><span class="text-sm text-muted-foreground">Rush home</span></div><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">📱💬❤️</span><span class="text-sm text-muted-foreground">Send love message</span></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="font-semibold tracking-tight text-lg">Contrast Method</h3></div><div class="p-6 pt-0"><p class="text-muted-foreground mb-3">Use contrasting emojis to create humorous effects</p><div class="space-y-2"><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">😇😈</span><span class="text-sm text-muted-foreground">Surface vs Inner</span></div><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">🤓➡️🥳</span><span class="text-sm text-muted-foreground">Work vs Off work</span></div><div class="flex items-center justify-between p-2 bg-muted rounded"><span class="text-xl">💪😭</span><span class="text-sm text-muted-foreground">Strong yet fragile</span></div></div></div></div></div></div><div class="rounded-lg border text-card-foreground shadow-sm mt-8 bg-muted"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">🎯 Usage Suggestions</h3></div><div class="p-6 pt-0"><ul class="space-y-2 text-muted-foreground"><li>• Choose appropriate emoji combinations based on video content to enhance expression</li><li>• Avoid overuse - combinations of 2-5 emojis work best</li><li>• Combine with text to make comments more vivid and interesting</li><li>• Follow the latest trends and update your emoji library regularly</li><li>• Create your own unique combinations to develop a personal style</li></ul></div></div></div></main><footer class="mt-20 border-t bg-muted/30"><div class="container py-12"><div class="grid gap-8 md:grid-cols-4"><div class="md:col-span-2"><div class="flex items-center gap-2 mb-4"><span class="text-2xl">🎵</span><span class="font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">TikTok Emoji</span></div><p class="text-sm text-muted-foreground mb-4">Your ultimate resource for TikTok hidden emojis, creative combinations, and emoji trends. Make your TikTok comments more expressive and engaging!</p></div><div><h3 class="font-semibold mb-4">Quick Links</h3><ul class="space-y-2 text-sm"><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/hidden-emojis">Hidden Emojis</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/popular-combos">Popular Combos</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/title-generator">Title Generator</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/search">Search Emojis</a></li></ul></div><div><h3 class="font-semibold mb-4">Categories</h3><ul class="space-y-2 text-sm"><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/smileys-emotion">Smileys &amp; Emotion</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/people-body">People &amp; Body</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/animals-nature">Animals &amp; Nature</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/food-drink">Food &amp; Drink</a></li></ul></div></div><div class="mt-8 pt-8 border-t text-center text-sm text-muted-foreground"><p>© 2024 TikTok Emoji. Not affiliated with TikTok or ByteDance.</p><p class="mt-2">Made with 💕 for the TikTok community</p></div></div></footer><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div><script src="/_next/static/chunks/webpack-1b400f1de436fe28.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/bc19b31da0bd50c9.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"3:I[2846,[],\"\"]\n5:I[9107,[],\"ClientPageRoot\"]\n6:I[3779,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"963\",\"static/chunks/963-40a18a252de9e9a5.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"27\",\"static/chunks/app/popular-combos/page-41e306f0392622f2.js\"],\"default\",1]\n7:I[4707,[],\"\"]\n8:I[6423,[],\"\"]\n9:I[7105,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"ExtensionErrorHandler\"]\na:I[8783,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"Navigation\"]\nb:I[2972,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"150\",\"static/chunks/150-dde69c0d40954ea8.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"931\",\"static/chunks/app/page-a448ec3e013efd3a.js\"],\"\"]\nc:I[9556,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"Toaster\"]\nd:I[8271,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"PerformanceMonitor\"]\nf:I[1060,[],\"\"]\n10:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$L3\",null,{\"buildId\":\"w8mdcKjlUfOtXTnBxYEHi\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"popular-combos\"],\"initialTree\":[\"\",{\"children\":[\"popular-combos\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"popular-combos\",{\"children\":[\"__PAGE__\",{},[[\"$L4\",[\"$\",\"$L5\",null,{\"props\":{\"params\":{},\"searchParams\":{}},\"Component\":\"$6\"}],null],null],null]},[null,[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"popular-combos\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/bc19b31da0bd50c9.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"WebSite\\\",\\\"name\\\":\\\"TikTok Emoji\\\",\\\"alternateName\\\":\\\"TikTok Hidden Emoji Codes\\\",\\\"url\\\":\\\"https://tiktokemoji.com\\\",\\\"description\\\":\\\"Discover TikTok hidden emoji codes, popular combinations, and special meanings\\\",\\\"potentialAction\\\":{\\\"@type\\\":\\\"SearchAction\\\",\\\"target\\\":{\\\"@type\\\":\\\"EntryPoint\\\",\\\"urlTemplate\\\":\\\"https://tiktokemoji.com/search?q={search_term_string}\\\"},\\\"query-input\\\":\\\"required name=search_term_string\\\"},\\\"publisher\\\":{\\\"@type\\\":\\\"Organization\\\",\\\"name\\\":\\\"TikTok Emoji\\\",\\\"url\\\":\\\"https://tiktokemoji.com\\\",\\\"logo\\\":{\\\"@type\\\":\\\"ImageObject\\\",\\\"url\\\":\\\"https://tiktokemoji.com/logo.png\\\",\\\"width\\\":600,\\\"height\\\":60}},\\\"sameAs\\\":[]}\"}}]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[[\"$\",\"$L9\",null,{}],[\"$\",\"$La\",null,{}],[\"$\",\"main\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}]}],[\"$\",\"footer\",null,{\"className\":\"mt-20 border-t bg-muted/30\",\"children\":[\"$\",\"div\",null,{\"className\":\"container py-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grid gap-8 md:grid-cols-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"md:col-span-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2 mb-4\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-2xl\",\"children\":\"🎵\"}],[\"$\",\"span\",null,{\"className\":\"font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent\",\"children\":\"TikTok Emoji\"}]]}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground mb-4\",\"children\":\"Your ultimate resource for TikTok hidden emojis, creative combinations, and emoji trends. Make your TikTok comments more expressive and engaging!\"}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"font-semibold mb-4\",\"children\":\"Quick Links\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-sm\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$Lb\",null,{\"href\":\"/hidden-emojis\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Hidden Emojis\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Lb\",null,{\"href\":\"/popular-combos\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Popular Combos\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Lb\",null,{\"href\":\"/title-generator\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Title Generator\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Lb\",null,{\"href\":\"/search\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Search Emojis\"}]}]]}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"font-semibold mb-4\",\"children\":\"Categories\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-sm\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$Lb\",null,{\"href\":\"/category/smileys-emotion\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Smileys \u0026 Emotion\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Lb\",null,{\"href\":\"/category/people-body\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"People \u0026 Body\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Lb\",null,{\"href\":\"/category/animals-nature\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Animals \u0026 Nature\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Lb\",null,{\"href\":\"/category/food-drink\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Food \u0026 Drink\"}]}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 pt-8 border-t text-center text-sm text-muted-foreground\",\"children\":[[\"$\",\"p\",null,{\"children\":\"© 2024 TikTok Emoji. Not affiliated with TikTok or ByteDance.\"}],[\"$\",\"p\",null,{\"className\":\"mt-2\",\"children\":\"Made with 💕 for the TikTok community\"}]]}]]}]}],[\"$\",\"$Lc\",null,{}],[\"$\",\"$Ld\",null,{}]]}]]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$Le\"],\"globalErrorComponent\":\"$f\",\"missingSlots\":\"$W10\"}]\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"TikTok Emoji - Hidden Codes \u0026 Popular Combinations\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings. Copy with one click to make your TikTok comments more engaging!\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"TikTok Emoji Team\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"TikTok emoji,hidden emoji codes,TikTok symbols,emoji combinations,TikTok comments,secret emojis\"}],[\"$\",\"meta\",\"6\",{\"name\":\"creator\",\"content\":\"TikTok Emoji\"}],[\"$\",\"meta\",\"7\",{\"name\":\"publisher\",\"content\":\"TikTok Emoji\"}],[\"$\",\"meta\",\"8\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"9\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"10\",{\"name\":\"google-site-verification\",\"content\":\"google-site-verification-code\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:title\",\"content\":\"TikTok Emoji - Hidden Codes \u0026 Popular Combinations\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:description\",\"content\":\"Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings.\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:url\",\"content\":\"https://tiktokemoji.com\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:site_name\",\"content\":\"TikTok Emoji\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image\",\"content\":\"https://tiktokemoji.com/og-image.png\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:image:alt\",\"content\":\"TikTok Emoji - Hidden Codes \u0026 Popular Combinations\"}],[\"$\",\"meta\",\"20\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:title\",\"content\":\"TikTok Emoji - Hidden Codes \u0026 Popular Combinations\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:description\",\"content\":\"Discover 46+ TikTok hidden emoji codes and popular combinations\"}],[\"$\",\"meta\",\"24\",{\"name\":\"twitter:image\",\"content\":\"https://tiktokemoji.com/og-image.png\"}],[\"$\",\"meta\",\"25\",{\"name\":\"next-size-adjust\"}]]\n"])</script><script>self.__next_f.push([1,"4:null\n"])</script></body></html>