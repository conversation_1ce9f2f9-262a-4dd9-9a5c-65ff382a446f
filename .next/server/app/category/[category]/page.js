(()=>{var e={};e.id=130,e.ids=[130],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1280:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(1212),r(6537),r(5866);var n=r(3191),o=r(8716),a=r(7922),s=r.n(a),i=r(5231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["category",{children:["[category]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1212)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/category/[category]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6537)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/develop/GitHub/tiktokemoji/app/category/[category]/page.tsx"],u="/category/[category]/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/category/[category]/page",pathname:"/category/[category]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3248:(e,t,r)=>{Promise.resolve().then(r.bind(r,5641))},5641:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eo});var n=r(326),o=r(5047),a=r(434),s=r(772),i=r(6333),l=r(8307),c=r(6557);let d=(0,c.Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var u=r(7577),p=r(2643),x=r(2933),m=r(3810);let f=(0,c.Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var g=r(7863),h=r(2561),y=r(8051),v=r(3095),j=r(825),b=r(8957),w=r(9625),N=(r(3078),r(9815)),C=r(5226),k=r(4214),T=r(2067),E=r(6009),[P,R]=(0,v.b)("Tooltip",[w.D7]),M=(0,w.D7)(),_="TooltipProvider",L="tooltip.open",[z,Z]=P(_),A=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:a=!1,children:s}=e,i=u.useRef(!0),l=u.useRef(!1),c=u.useRef(0);return u.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,n.jsx)(z,{scope:t,isOpenDelayedRef:i,delayDuration:r,onOpen:u.useCallback(()=>{window.clearTimeout(c.current),i.current=!1},[]),onClose:u.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.current=!0,o)},[o]),isPointerInTransitRef:l,onPointerInTransitChange:u.useCallback(e=>{l.current=e},[]),disableHoverableContent:a,children:s})};A.displayName=_;var D="Tooltip",[O,H]=P(D),I=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:a,onOpenChange:s,disableHoverableContent:i,delayDuration:l}=e,c=Z(D,e.__scopeTooltip),d=M(t),[p,x]=u.useState(null),m=(0,b.M)(),f=u.useRef(0),g=i??c.disableHoverableContent,h=l??c.delayDuration,y=u.useRef(!1),[v,j]=(0,T.T)({prop:o,defaultProp:a??!1,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(L))):c.onClose(),s?.(e)},caller:D}),N=u.useMemo(()=>v?y.current?"delayed-open":"instant-open":"closed",[v]),C=u.useCallback(()=>{window.clearTimeout(f.current),f.current=0,y.current=!1,j(!0)},[j]),k=u.useCallback(()=>{window.clearTimeout(f.current),f.current=0,j(!1)},[j]),E=u.useCallback(()=>{window.clearTimeout(f.current),f.current=window.setTimeout(()=>{y.current=!0,j(!0),f.current=0},h)},[h,j]);return u.useEffect(()=>()=>{f.current&&(window.clearTimeout(f.current),f.current=0)},[]),(0,n.jsx)(w.fC,{...d,children:(0,n.jsx)(O,{scope:t,contentId:m,open:v,stateAttribute:N,trigger:p,onTriggerChange:x,onTriggerEnter:u.useCallback(()=>{c.isOpenDelayedRef.current?E():C()},[c.isOpenDelayedRef,E,C]),onTriggerLeave:u.useCallback(()=>{g?k():(window.clearTimeout(f.current),f.current=0)},[k,g]),onOpen:C,onClose:k,disableHoverableContent:g,children:r})})};I.displayName=D;var S="TooltipTrigger",G=u.forwardRef((e,t)=>{let{__scopeTooltip:r,...o}=e,a=H(S,r),s=Z(S,r),i=M(r),l=u.useRef(null),c=(0,y.e)(t,l,a.onTriggerChange),d=u.useRef(!1),p=u.useRef(!1),x=u.useCallback(()=>d.current=!1,[]);return u.useEffect(()=>()=>document.removeEventListener("pointerup",x),[x]),(0,n.jsx)(w.ee,{asChild:!0,...i,children:(0,n.jsx)(C.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...o,ref:c,onPointerMove:(0,h.M)(e.onPointerMove,e=>{"touch"===e.pointerType||p.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),p.current=!0)}),onPointerLeave:(0,h.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),p.current=!1}),onPointerDown:(0,h.M)(e.onPointerDown,()=>{a.open&&a.onClose(),d.current=!0,document.addEventListener("pointerup",x,{once:!0})}),onFocus:(0,h.M)(e.onFocus,()=>{d.current||a.onOpen()}),onBlur:(0,h.M)(e.onBlur,a.onClose),onClick:(0,h.M)(e.onClick,a.onClose)})})});G.displayName=S;var[q,B]=P("TooltipPortal",{forceMount:void 0}),F="TooltipContent",Y=u.forwardRef((e,t)=>{let r=B(F,e.__scopeTooltip),{forceMount:o=r.forceMount,side:a="top",...s}=e,i=H(F,e.__scopeTooltip);return(0,n.jsx)(N.z,{present:o||i.open,children:i.disableHoverableContent?(0,n.jsx)(K,{side:a,...s,ref:t}):(0,n.jsx)(U,{side:a,...s,ref:t})})}),U=u.forwardRef((e,t)=>{let r=H(F,e.__scopeTooltip),o=Z(F,e.__scopeTooltip),a=u.useRef(null),s=(0,y.e)(t,a),[i,l]=u.useState(null),{trigger:c,onClose:d}=r,p=a.current,{onPointerInTransitChange:x}=o,m=u.useCallback(()=>{l(null),x(!1)},[x]),f=u.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());l(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),x(!0)},[x]);return u.useEffect(()=>()=>m(),[m]),u.useEffect(()=>{if(c&&p){let e=e=>f(e,p),t=e=>f(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,f,m]),u.useEffect(()=>{if(i){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||p?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let s=t[e],i=t[a],l=s.x,c=s.y,d=i.x,u=i.y;c>n!=u>n&&r<(d-l)*(n-c)/(u-c)+l&&(o=!o)}return o}(r,i);n?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,i,d,m]),(0,n.jsx)(K,{...e,ref:s})}),[V,X]=P(D,{isInside:!1}),$=(0,k.sA)("TooltipContent"),K=u.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":a,onEscapeKeyDown:s,onPointerDownOutside:i,...l}=e,c=H(F,r),d=M(r),{onClose:p}=c;return u.useEffect(()=>(document.addEventListener(L,p),()=>document.removeEventListener(L,p)),[p]),u.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;t?.contains(c.trigger)&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,p]),(0,n.jsx)(j.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,n.jsxs)(w.VY,{"data-state":c.stateAttribute,...d,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,n.jsx)($,{children:o}),(0,n.jsx)(V,{scope:r,isInside:!0,children:(0,n.jsx)(E.fC,{id:c.contentId,role:"tooltip",children:a||o})})]})})});Y.displayName=F;var W="TooltipArrow";u.forwardRef((e,t)=>{let{__scopeTooltip:r,...o}=e,a=M(r);return X(W,r).isInside?null:(0,n.jsx)(w.Eh,{...a,...o,ref:t})}).displayName=W;let J=u.forwardRef(({className:e,sideOffset:t=4,...r},o)=>n.jsx(Y,{ref:o,sideOffset:t,className:(0,g.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",e),...r}));function Q({emoji:e,name:t,code:r,keywords:o=[],onCopy:a,className:i}){let[l,c]=(0,u.useState)(!1),d=t=>{t.stopPropagation(),navigator.clipboard.writeText(e),c(!0),a?.(e),setTimeout(()=>c(!1),2e3)};return n.jsx(p.Zb,{className:(0,g.cn)("hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer group relative overflow-hidden",i),onClick:d,children:(0,n.jsxs)(p.aY,{className:"p-4 text-center",children:[n.jsx("div",{className:"text-4xl mb-2 group-hover:scale-110 transition-transform",children:e}),n.jsx("p",{className:"text-xs font-medium truncate",children:t}),n.jsx("p",{className:"text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity mt-1",children:r}),(0,n.jsxs)("div",{className:"flex gap-1 justify-center mt-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[n.jsx(s.z,{size:"sm",variant:"ghost",className:"h-7 px-2",onClick:d,children:l?n.jsx(x.Z,{className:"w-3 h-3 text-green-600"}):n.jsx(m.Z,{className:"w-3 h-3"})}),o.length>0&&n.jsx(A,{children:(0,n.jsxs)(I,{children:[n.jsx(G,{asChild:!0,children:n.jsx(s.z,{size:"sm",variant:"ghost",className:"h-7 px-2",onClick:e=>e.stopPropagation(),children:n.jsx(f,{className:"w-3 h-3"})})}),n.jsx(J,{children:(0,n.jsxs)("div",{className:"max-w-xs",children:[n.jsx("p",{className:"font-semibold mb-1",children:"Keywords:"}),n.jsx("p",{className:"text-xs",children:o.join(", ")})]})})]})})]}),n.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"})]})})}J.displayName=Y.displayName;var ee=r(6e3),et=r(4008),er=r(4432),en=r(567);function eo(){let e=(0,o.useParams)(),t=e?.category||"",{toast:r}=(0,et.pm)(),[c,p]=(0,u.useState)(""),[x,m]=(0,u.useState)(null),f=(0,ee.bl)(t),h=f?(0,ee.Zn)(f.name):[],y=(0,u.useMemo)(()=>Array.from(new Set(h.map(e=>e.subcategory))).sort(),[h]),v=(0,u.useMemo)(()=>{let e=h;if(x&&(e=e.filter(e=>e.subcategory===x)),c){let t=c.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(t)||e.keywords.some(e=>e.toLowerCase().includes(t))||e.emoji===c)}return e},[h,c,x]),j=e=>{navigator.clipboard.writeText(e),r({title:"Copied!",description:`${e} copied to clipboard`,duration:2e3})};return f?(0,n.jsxs)("div",{className:"container py-8",children:[(0,n.jsxs)("div",{className:"mb-8",children:[n.jsx(a.default,{href:"/",children:(0,n.jsxs)(s.z,{variant:"ghost",size:"sm",className:"mb-4",children:[n.jsx(i.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,n.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[n.jsx("div",{className:"text-6xl",children:f.icon}),(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-4xl font-bold",children:f.name}),n.jsx("p",{className:"text-xl text-muted-foreground",children:f.zhName}),n.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:f.description})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[n.jsx("div",{className:"flex-1",children:(0,n.jsxs)("p",{className:"text-lg mb-2",children:[n.jsx("span",{className:"font-semibold",children:v.length})," of"," ",n.jsx("span",{className:"font-semibold",children:h.length})," emojis",x&&(0,n.jsxs)("span",{className:"text-muted-foreground",children:[" in ",x]})]})}),(0,n.jsxs)("div",{className:"relative w-full sm:w-96",children:[n.jsx(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),n.jsx(er.I,{type:"text",placeholder:"Search emojis...",value:c,onChange:e=>p(e.target.value),className:"pl-10"})]})]}),y.length>1&&(0,n.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,n.jsxs)(s.z,{size:"sm",variant:null===x?"default":"outline",onClick:()=>m(null),className:"gap-1",children:[n.jsx(d,{className:"w-3 h-3"}),"All"]}),y.map(e=>{let t=h.filter(t=>t.subcategory===e).length;return(0,n.jsxs)(en.C,{variant:x===e?"default":"outline",className:(0,g.cn)("cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors",x===e&&"bg-primary text-primary-foreground"),onClick:()=>m(x===e?null:e),children:[e," (",t,")"]},e)})]})]})]}),n.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4",children:v.map(e=>n.jsx(Q,{emoji:e.emoji,name:e.name,code:e.code,keywords:e.keywords,onCopy:j},e.code))}),0===v.length&&(0,n.jsxs)("div",{className:"text-center py-20",children:[(0,n.jsxs)("p",{className:"text-xl text-muted-foreground",children:["No emojis found",c&&` matching "${c}"`,x&&` in ${x}`]}),(0,n.jsxs)("div",{className:"flex gap-2 justify-center mt-4",children:[c&&n.jsx(s.z,{variant:"outline",onClick:()=>p(""),children:"Clear search"}),x&&n.jsx(s.z,{variant:"outline",onClick:()=>m(null),children:"Clear filter"})]})]})]}):(0,n.jsxs)("div",{className:"container py-20 text-center",children:[n.jsx("h1",{className:"text-3xl font-bold mb-4",children:"Category not found"}),n.jsx(a.default,{href:"/",children:(0,n.jsxs)(s.z,{children:[n.jsx(i.Z,{className:"w-4 h-4 mr-2"}),"Back to Home"]})})]})}},567:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var n=r(326);r(7577);var o=r(9360),a=r(7863);let s=(0,o.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return n.jsx("div",{className:(0,a.cn)(s({variant:t}),e),...r})}},2643:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>i,SZ:()=>c,Zb:()=>s,aY:()=>d,ll:()=>l});var n=r(326),o=r(7577),a=r(7863);let s=o.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));s.displayName="Card";let i=o.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=o.forwardRef(({className:e,...t},r)=>n.jsx("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=o.forwardRef(({className:e,...t},r)=>n.jsx("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=o.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",o.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},6333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3810:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},1212:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(8570).createProxy)(String.raw`/Users/<USER>/develop/GitHub/tiktokemoji/app/category/[category]/page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[948,28,87],()=>r(1280));module.exports=n})();