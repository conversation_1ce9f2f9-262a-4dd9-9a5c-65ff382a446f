<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/bc19b31da0bd50c9.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-1b400f1de436fe28.js"/><script src="/_next/static/chunks/fd9d1056-346a4d4db4c146e1.js" async=""></script><script src="/_next/static/chunks/117-4ced7f814a77fa18.js" async=""></script><script src="/_next/static/chunks/main-app-31faf2160eb37e61.js" async=""></script><script src="/_next/static/chunks/438-3d6b98e82ab59c7b.js" async=""></script><script src="/_next/static/chunks/150-dde69c0d40954ea8.js" async=""></script><script src="/_next/static/chunks/672-2b366b7389252487.js" async=""></script><script src="/_next/static/chunks/app/hidden-emojis/page-cae103774b4d97e2.js" async=""></script><script src="/_next/static/chunks/52-e89dc12467f890af.js" async=""></script><script src="/_next/static/chunks/367-70ef8c60d69865f7.js" async=""></script><script src="/_next/static/chunks/781-3944b942f49751e0.js" async=""></script><script src="/_next/static/chunks/435-dc8290f2b1c966aa.js" async=""></script><script src="/_next/static/chunks/2-bcbe5cd3b8eae62a.js" async=""></script><script src="/_next/static/chunks/app/layout-0af87e3f0fffa538.js" async=""></script><script src="/_next/static/chunks/app/page-a448ec3e013efd3a.js" async=""></script><title>46 TikTok Hidden Emoji Codes - Complete List 2024 | TikTok Emoji</title><meta name="description" content="Complete list of 46 TikTok hidden emoji codes with meanings and usage. Discover secret emojis like [smile], [happy], [angry] and more. Copy with one click!"/><meta name="author" content="TikTok Emoji Team"/><meta name="keywords" content="TikTok hidden emojis,secret emoji codes,TikTok symbols,[smile] emoji,[happy] emoji,TikTok comment tricks"/><meta name="creator" content="TikTok Emoji"/><meta name="publisher" content="TikTok Emoji"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><link rel="canonical" href="https://tiktokemoji.com/hidden-emojis"/><meta name="google-site-verification" content="google-site-verification-code"/><meta property="og:title" content="46 TikTok Hidden Emoji Codes - Complete List"/><meta property="og:description" content="All TikTok secret emoji codes in one place. Learn how to use hidden emojis in comments."/><meta property="og:image" content="https://tiktokemoji.com/og-hidden-emojis.png"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="TikTok Hidden Emoji Codes Complete List"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="46 TikTok Hidden Emoji Codes"/><meta name="twitter:description" content="All TikTok secret emoji codes in one place"/><meta name="twitter:image" content="https://tiktokemoji.com/og-hidden-emojis.png"/><meta name="next-size-adjust"/><script type="application/ld+json">{"@context":"https://schema.org","@type":"WebSite","name":"TikTok Emoji","alternateName":"TikTok Hidden Emoji Codes","url":"https://tiktokemoji.com","description":"Discover TikTok hidden emoji codes, popular combinations, and special meanings","potentialAction":{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://tiktokemoji.com/search?q={search_term_string}"},"query-input":"required name=search_term_string"},"publisher":{"@type":"Organization","name":"TikTok Emoji","url":"https://tiktokemoji.com","logo":{"@type":"ImageObject","url":"https://tiktokemoji.com/logo.png","width":600,"height":60}},"sameAs":[]}</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"><div class="container flex h-16 items-center justify-between"><a class="flex items-center space-x-2 flex-shrink-0" href="/"><span class="text-2xl">🎵</span><span class="font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">TikTok Emoji</span></a><form class="relative flex-1 max-w-2xl mx-8"><input type="search" class="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-10 w-full" placeholder="Search emojis, codes, or meanings..." value=""/><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 absolute right-0 top-0" type="submit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search h-4 w-4"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg></button></form><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 gap-2 flex-shrink-0" type="button" id="radix-:Rqkq:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-grid3x3 h-4 w-4"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M3 15h18"></path><path d="M9 3v18"></path><path d="M15 3v18"></path></svg>Categories<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3 w-3"><path d="m6 9 6 6 6-6"></path></svg></button></div></header><main class="min-h-screen"><div class="container py-8"><a class="inline-flex items-center text-muted-foreground hover:text-foreground mb-6" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-4 h-4 mr-2"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>Back to Home</a><h1 class="text-4xl font-bold mb-8">TikTok Hidden Emoji Codes</h1><div class="rounded-lg border text-card-foreground shadow-sm mb-8 bg-amber-50 border-amber-200"><div class="p-6 pt-6"><div class="flex items-start gap-3"><span class="text-2xl">💡</span><div><p class="font-semibold mb-1">How to use:</p><p class="text-sm">Type the code in square brackets in TikTok comments, like [smile], and it will display the corresponding hidden emoji. These emojis are only visible on TikTok!</p></div></div></div></div><div class="mb-8"><div class="relative max-w-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><input type="search" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-10" placeholder="Search hidden emojis..." value=""/></div></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-12"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Smile emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/smile.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[smile]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Smile</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->2.3M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Happy emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/happy.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[happy]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Happy</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.8M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Angry emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/angry.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[angry]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Angry</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->980K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Cry emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/cry.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[cry]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Cry</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.2M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Embarrassed emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/embarrassed.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[embarrassed]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Embarrassed</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->856K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Surprised emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/surprised.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[surprised]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Surprised</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.5M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Wronged emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/wronged.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[wronged]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Wronged</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->2.1M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Shout emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/shout.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[shout]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Shout</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.3M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Flushed emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/flushed.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[flushed]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Flushed</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->967K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Yummy emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/yummy.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[yummy]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Yummy</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->789K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Complacent emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/complacent.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[complacent]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Complacent</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.1M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Drool emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/drool.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[drool]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Drool</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->892K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Scream emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/scream.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[scream]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Scream</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.4M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Weep emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/weep.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[weep]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Weep</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->2.5M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Speechless emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/speechless.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[speechless]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Speechless</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.2M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Funny Face emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/funnyface.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[funnyface]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Funny Face</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.6M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Laugh with Tears emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/laughwithtears.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[laughwithtears]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Laugh with Tears</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->3.2M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Wicked emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/wicked.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[wicked]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Wicked</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.4M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Face with Rolling Eyes emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/facewithrollingeyes.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[facewithrollingeyes]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Face with Rolling Eyes</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.8M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Sulk emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/sulk.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[sulk]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Sulk</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->920K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Thinking emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/thinking.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[thinking]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Thinking</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.7M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Lovely emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/lovely.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[lovely]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Lovely</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->2.4M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Greedy emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/greedy.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[greedy]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Greedy</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->680K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Wow emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/wow.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[wow]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Wow</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.9M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Joyful emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/joyful.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[joyful]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Joyful</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.6M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Hehe emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/hehe.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[hehe]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Hehe</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.3M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Slap emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/slap.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[slap]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Slap</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->890K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Tears emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/tears.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[tears]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Tears</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.1M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Stun emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/stun.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[stun]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Stun</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->760K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Cute emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/cute.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[cute]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Cute</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->2.8M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Blink emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/blink.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[blink]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Blink</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.4M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Disdain emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/disdain.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[disdain]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Disdain</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->980K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Astonish emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/astonish.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[astonish]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Astonish</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.5M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Rage emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/rage.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[rage]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Rage</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->720K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Cool emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/cool.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[cool]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Cool</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->2.2M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Excited emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/excited.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[excited]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Excited</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.9M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Proud emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/proud.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[proud]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Proud</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.1M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Smile Face emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/smileface.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[smileface]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Smile Face</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.7M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Evil emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/evil.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[evil]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Evil</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->890K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Angel emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/angel.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[angel]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Angel</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.3M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Laugh emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/laugh.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[laugh]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Laugh</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->2.1M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Pride emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/pride.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[pride]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Pride</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->980K</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Nap emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/nap.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[nap]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Nap</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.2M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Love Face emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/loveface.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[loveface]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Love Face</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->2.9M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Awkward emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/awkward.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[awkward]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Awkward</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.5M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-3 sm:p-4"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-12 h-12 text-4xl relative flex items-center justify-center"><img alt="TikTok Shock emoji" loading="lazy" width="48" height="48" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/shock.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[shock]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Shock</p><p class="text-xs text-muted-foreground mb-2 sm:mb-3">Usage: <!-- -->1.6M</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm bg-gradient-to-br from-pink-50 to-blue-50 border-pink-200 mb-8"><div class="p-6 pt-6"><h3 class="text-xl font-bold mb-6 text-center">📚 TikTok Emoji Meanings</h3><p class="text-sm text-muted-foreground mb-6 text-center">Complete list of TikTok hidden emoji meanings and descriptions</p><div class="grid gap-3 max-h-96 overflow-y-auto"><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[smile]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Smile</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">2.3M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A small, round, pink smiling face used to show happiness or appreciation.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[happy]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Happy</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.8M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A peach-colored face with squinty eyes and a big open mouth, expressing extreme excitement.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[angry]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Angry</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">980K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A red face with furrowed brows, used to convey anger or displeasure.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[cry]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Cry</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.2M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A blue face with tears streaming down, representing sadness or emotional overwhelm.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[embarrassed]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Embarrassed</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">856K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with rosy cheeks and a shy smile, indicating embarrassment or shyness.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[surprised]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Surprised</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.5M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with wide eyes and an open mouth, expressing shock or surprise.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[wronged]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Wronged</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">2.1M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A yellow face with sad eyes and two fingers pointing at each other, indicating shyness or embarrassment.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[shout]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Shout</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.3M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with an open mouth, often used to express excitement or yelling.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[flushed]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Flushed</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">967K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with red cheeks, indicating embarrassment or shyness.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[yummy]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Yummy</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">789K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face licking its lips, expressing deliciousness or craving for food.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[complacent]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Complacent</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.1M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A smug face with a self-satisfied smile, indicating complacency.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[drool]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Drool</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">892K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with drool coming out of its mouth, indicating hunger or desire.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[scream]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Scream</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.4M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with wide eyes and an open mouth, expressing fear or shock.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[weep]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Weep</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">2.5M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with tears, indicating deep sadness or sorrow.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[speechless]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Speechless</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.2M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a hand over its mouth, indicating shock or being speechless.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[funnyface]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Funny Face</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.6M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A silly face with exaggerated features, used to indicate humor.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[laughwithtears]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Laugh with Tears</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">3.2M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face laughing with tears streaming down, indicating extreme laughter.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[wicked]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Wicked</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.4M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A mischievous face with a sly smile, indicating wickedness or playfulness.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[facewithrollingeyes]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Face with Rolling Eyes</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.8M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face rolling its eyes, indicating annoyance or disbelief.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[sulk]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Sulk</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">920K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a frown, indicating sulking or disappointment.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[thinking]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Thinking</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.7M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a hand on its chin, indicating deep thought or contemplation.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[lovely]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Lovely</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">2.4M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with hearts in its eyes, indicating love or admiration.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[greedy]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Greedy</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">680K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a greedy smile, indicating greed or desire for more.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[wow]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Wow</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.9M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with wide eyes and an open mouth, expressing amazement or wonder.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[joyful]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Joyful</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.6M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a big smile and sparkling eyes, indicating joy or happiness.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[hehe]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Hehe</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.3M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a mischievous grin, indicating a light-hearted or playful mood.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[slap]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Slap</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">890K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a hand raised, indicating a slap or playful hit.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[tears]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Tears</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.1M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with tears, indicating sadness or emotional release.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[stun]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Stun</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">760K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a shocked expression, indicating surprise or disbelief.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[cute]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Cute</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">2.8M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a sweet smile, indicating cuteness or affection.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[blink]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Blink</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.4M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with one eye closed, indicating a wink or playful gesture.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[disdain]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Disdain</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">980K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a raised eyebrow, indicating disdain or disapproval.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[astonish]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Astonish</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.5M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with wide eyes and raised eyebrows, indicating astonishment.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[rage]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Rage</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">720K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a furious expression, indicating intense anger.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[cool]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Cool</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">2.2M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with sunglasses, indicating coolness or confidence.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[excited]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Excited</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.9M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with sparkling eyes and a big smile, indicating excitement.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[proud]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Proud</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.1M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a smug smile, indicating pride or satisfaction.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[smileface]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Smile Face</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.7M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a big smile, indicating happiness or friendliness.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[evil]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Evil</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">890K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a devilish grin, indicating mischief or evil intent.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[angel]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Angel</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.3M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a halo, indicating innocence or goodness.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[laugh]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Laugh</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">2.1M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face laughing, indicating joy or amusement.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[pride]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Pride</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">980K<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a proud expression, indicating self-satisfaction.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[nap]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Nap</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.2M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with closed eyes, indicating sleepiness or a desire to nap.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[loveface]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Love Face</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">2.9M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with heart-shaped eyes, indicating love or infatuation.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[awkward]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Awkward</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.5M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a nervous smile, indicating awkwardness or discomfort.</p></div></div><div class="flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50"><div class="flex-shrink-0"><span class="text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs">[shock]</span></div><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-semibold text-sm">Shock</span><span class="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded">1.6M<!-- --> uses</span></div><p class="text-xs text-muted-foreground leading-relaxed">A face with a shocked expression, indicating surprise or disbelief.</p></div></div></div></div></div><div class="rounded-lg border text-card-foreground shadow-sm bg-muted"><div class="p-6 pt-6"><h3 class="text-lg font-semibold mb-4">🎯 Pro Tips</h3><ul class="space-y-2 text-sm text-muted-foreground"><li>• These hidden emojis are only visible within TikTok - they&#x27;ll appear as text codes on other platforms</li><li>• You can combine multiple hidden emojis, like [happy][happy][happy] for super happy</li><li>• Some hidden emojis may display slightly differently in different regions</li><li>• Bookmark this page for easy access whenever you need these codes</li></ul></div></div></div></main><footer class="mt-20 border-t bg-muted/30"><div class="container py-12"><div class="grid gap-8 md:grid-cols-4"><div class="md:col-span-2"><div class="flex items-center gap-2 mb-4"><span class="text-2xl">🎵</span><span class="font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">TikTok Emoji</span></div><p class="text-sm text-muted-foreground mb-4">Your ultimate resource for TikTok hidden emojis, creative combinations, and emoji trends. Make your TikTok comments more expressive and engaging!</p></div><div><h3 class="font-semibold mb-4">Quick Links</h3><ul class="space-y-2 text-sm"><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/hidden-emojis">Hidden Emojis</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/popular-combos">Popular Combos</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/title-generator">Title Generator</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/search">Search Emojis</a></li></ul></div><div><h3 class="font-semibold mb-4">Categories</h3><ul class="space-y-2 text-sm"><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/smileys-emotion">Smileys &amp; Emotion</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/people-body">People &amp; Body</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/animals-nature">Animals &amp; Nature</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/food-drink">Food &amp; Drink</a></li></ul></div></div><div class="mt-8 pt-8 border-t text-center text-sm text-muted-foreground"><p>© 2024 TikTok Emoji. Not affiliated with TikTok or ByteDance.</p><p class="mt-2">Made with 💕 for the TikTok community</p></div></div></footer><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div><script src="/_next/static/chunks/webpack-1b400f1de436fe28.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/bc19b31da0bd50c9.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"3:I[2846,[],\"\"]\n5:I[5467,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"150\",\"static/chunks/150-dde69c0d40954ea8.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"745\",\"static/chunks/app/hidden-emojis/page-cae103774b4d97e2.js\"],\"default\"]\n6:I[4707,[],\"\"]\n7:I[6423,[],\"\"]\n8:I[7105,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"ExtensionErrorHandler\"]\n9:I[8783,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"Navigation\"]\na:I[2972,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"150\",\"static/chunks/150-dde69c0d40954ea8.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"931\",\"static/chunks/app/page-a448ec3e013efd3a.js\"],\"\"]\nb:I[9556,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"Toaster\"]\nc:I[8271,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"PerformanceMonitor\"]\ne:I[1060,[],\"\"]\nf:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$L3\",null,{\"buildId\":\"w8mdcKjlUfOtXTnBxYEHi\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"hidden-emojis\"],\"initialTree\":[\"\",{\"children\":[\"hidden-emojis\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"hidden-emojis\",{\"children\":[\"__PAGE__\",{},[[\"$L4\",[\"$\",\"$L5\",null,{}],null],null],null]},[null,[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"hidden-emojis\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/bc19b31da0bd50c9.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"WebSite\\\",\\\"name\\\":\\\"TikTok Emoji\\\",\\\"alternateName\\\":\\\"TikTok Hidden Emoji Codes\\\",\\\"url\\\":\\\"https://tiktokemoji.com\\\",\\\"description\\\":\\\"Discover TikTok hidden emoji codes, popular combinations, and special meanings\\\",\\\"potentialAction\\\":{\\\"@type\\\":\\\"SearchAction\\\",\\\"target\\\":{\\\"@type\\\":\\\"EntryPoint\\\",\\\"urlTemplate\\\":\\\"https://tiktokemoji.com/search?q={search_term_string}\\\"},\\\"query-input\\\":\\\"required name=search_term_string\\\"},\\\"publisher\\\":{\\\"@type\\\":\\\"Organization\\\",\\\"name\\\":\\\"TikTok Emoji\\\",\\\"url\\\":\\\"https://tiktokemoji.com\\\",\\\"logo\\\":{\\\"@type\\\":\\\"ImageObject\\\",\\\"url\\\":\\\"https://tiktokemoji.com/logo.png\\\",\\\"width\\\":600,\\\"height\\\":60}},\\\"sameAs\\\":[]}\"}}]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[[\"$\",\"$L8\",null,{}],[\"$\",\"$L9\",null,{}],[\"$\",\"main\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}]}],[\"$\",\"footer\",null,{\"className\":\"mt-20 border-t bg-muted/30\",\"children\":[\"$\",\"div\",null,{\"className\":\"container py-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grid gap-8 md:grid-cols-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"md:col-span-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2 mb-4\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-2xl\",\"children\":\"🎵\"}],[\"$\",\"span\",null,{\"className\":\"font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent\",\"children\":\"TikTok Emoji\"}]]}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground mb-4\",\"children\":\"Your ultimate resource for TikTok hidden emojis, creative combinations, and emoji trends. Make your TikTok comments more expressive and engaging!\"}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"font-semibold mb-4\",\"children\":\"Quick Links\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-sm\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$La\",null,{\"href\":\"/hidden-emojis\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Hidden Emojis\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$La\",null,{\"href\":\"/popular-combos\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Popular Combos\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$La\",null,{\"href\":\"/title-generator\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Title Generator\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$La\",null,{\"href\":\"/search\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Search Emojis\"}]}]]}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"font-semibold mb-4\",\"children\":\"Categories\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-sm\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$La\",null,{\"href\":\"/category/smileys-emotion\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Smileys \u0026 Emotion\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$La\",null,{\"href\":\"/category/people-body\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"People \u0026 Body\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$La\",null,{\"href\":\"/category/animals-nature\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Animals \u0026 Nature\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$La\",null,{\"href\":\"/category/food-drink\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Food \u0026 Drink\"}]}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 pt-8 border-t text-center text-sm text-muted-foreground\",\"children\":[[\"$\",\"p\",null,{\"children\":\"© 2024 TikTok Emoji. Not affiliated with TikTok or ByteDance.\"}],[\"$\",\"p\",null,{\"className\":\"mt-2\",\"children\":\"Made with 💕 for the TikTok community\"}]]}]]}]}],[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{}]]}]]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$Ld\"],\"globalErrorComponent\":\"$e\",\"missingSlots\":\"$Wf\"}]\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"46 TikTok Hidden Emoji Codes - Complete List 2024 | TikTok Emoji\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Complete list of 46 TikTok hidden emoji codes with meanings and usage. Discover secret emojis like [smile], [happy], [angry] and more. Copy with one click!\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"TikTok Emoji Team\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"TikTok hidden emojis,secret emoji codes,TikTok symbols,[smile] emoji,[happy] emoji,TikTok comment tricks\"}],[\"$\",\"meta\",\"6\",{\"name\":\"creator\",\"content\":\"TikTok Emoji\"}],[\"$\",\"meta\",\"7\",{\"name\":\"publisher\",\"content\":\"TikTok Emoji\"}],[\"$\",\"meta\",\"8\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"9\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"link\",\"10\",{\"rel\":\"canonical\",\"href\":\"https://tiktokemoji.com/hidden-emojis\"}],[\"$\",\"meta\",\"11\",{\"name\":\"google-site-verification\",\"content\":\"google-site-verification-code\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:title\",\"content\":\"46 TikTok Hidden Emoji Codes - Complete List\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:description\",\"content\":\"All TikTok secret emoji codes in one place. Learn how to use hidden emojis in comments.\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image\",\"content\":\"https://tiktokemoji.com/og-hidden-emojis.png\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:alt\",\"content\":\"TikTok Hidden Emoji Codes Complete List\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:title\",\"content\":\"46 TikTok Hidden Emoji Codes\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:description\",\"content\":\"All TikTok secret emoji codes in one place\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:image\",\"content\":\"https://tiktokemoji.com/og-hidden-emojis.png\"}],[\"$\",\"meta\",\"23\",{\"name\":\"next-size-adjust\"}]]\n"])</script><script>self.__next_f.push([1,"4:null\n"])</script></body></html>