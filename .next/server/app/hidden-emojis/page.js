(()=>{var e={};e.id=745,e.ids=[745],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3354:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>d}),t(2672),t(6537),t(5866);var i=t(3191),o=t(8716),r=t(7922),n=t.n(r),a=t(5231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(s,l);let d=["",{children:["hidden-emojis",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2672)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/hidden-emojis/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6537)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],m=["/Users/<USER>/develop/GitHub/tiktokemoji/app/hidden-emojis/page.tsx"],c="/hidden-emojis/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/hidden-emojis/page",pathname:"/hidden-emojis",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6061:(e,s,t)=>{Promise.resolve().then(t.bind(t,3125))},6245:(e,s,t)=>{"use strict";t.d(s,{f:()=>x});var i=t(326),o=t(2643),r=t(772),n=t(4008),a=t(3810),l=t(7863),d=t(7577),m=t(6226);function c({code:e,emoji:s,imageUrl:t,name:o,size:r="md",showFallback:n=!0}){let[a,l]=(0,d.useState)(!1),c={sm:"w-8 h-8 text-2xl",md:"w-12 h-12 text-4xl",lg:"w-16 h-16 text-6xl"};return t&&!a?i.jsx("div",{className:`${c[r]} relative flex items-center justify-center`,children:i.jsx(m.default,{src:t,alt:`TikTok ${o} emoji`,width:"sm"===r?32:"md"===r?48:64,height:"sm"===r?32:"md"===r?48:64,className:"rounded-full",onError:()=>l(!0),unoptimized:!0})}):n?i.jsx("div",{className:`${c[r]} flex items-center justify-center`,children:i.jsx("span",{className:"select-none",children:s})}):i.jsx("div",{className:`${c[r]} flex items-center justify-center bg-gray-100 rounded-full text-xs font-mono text-gray-600`,children:e})}function x({emoji:e,code:s,name:t,usage:d,className:m,size:x="md",imageUrl:p,description:h}){let{toast:u}=(0,n.pm)();return i.jsx(o.Zb,{className:(0,l.cn)("relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden","before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100",{sm:"p-2 sm:p-3",md:"p-3 sm:p-4",lg:"p-4 sm:p-6"}[x],m),children:(0,i.jsxs)("div",{className:"relative z-10 text-center",children:[i.jsx("div",{className:"mb-3 flex justify-center",children:i.jsx(c,{code:s||"",emoji:e,imageUrl:p,name:t||"",size:x})}),s&&i.jsx("code",{className:"inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono",children:s}),t&&i.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground mb-1 font-medium",children:t}),d&&(0,i.jsxs)("p",{className:"text-xs text-muted-foreground mb-2 sm:mb-3",children:["Usage: ",d]}),(0,i.jsxs)(r.z,{size:"sm",variant:"outline",className:"w-full text-xs sm:text-sm",onClick:()=>{navigator.clipboard.writeText(s||e),u({title:"Copied to clipboard!",description:`${s||e} copied successfully`})},children:[i.jsx(a.Z,{className:"w-3 h-3 mr-1 sm:mr-2"}),i.jsx("span",{className:"hidden sm:inline",children:"Copy"}),i.jsx("span",{className:"sm:hidden",children:"Copy"})]})]})})}},3125:(e,s,t)=>{"use strict";t.d(s,{default:()=>x});var i=t(326),o=t(7577),r=t(434),n=t(6333),a=t(8307),l=t(4432),d=t(6245),m=t(2056),c=t(2643);function x(){let[e,s]=(0,o.useState)(""),t=m.iN.filter(s=>s.code.toLowerCase().includes(e.toLowerCase())||s.name.toLowerCase().includes(e.toLowerCase()));return(0,i.jsxs)("div",{className:"container py-8",children:[(0,i.jsxs)(r.default,{href:"/",className:"inline-flex items-center text-muted-foreground hover:text-foreground mb-6",children:[i.jsx(n.Z,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),i.jsx("h1",{className:"text-4xl font-bold mb-8",children:"TikTok Hidden Emoji Codes"}),i.jsx(c.Zb,{className:"mb-8 bg-amber-50 border-amber-200",children:i.jsx(c.aY,{className:"pt-6",children:(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[i.jsx("span",{className:"text-2xl",children:"\uD83D\uDCA1"}),(0,i.jsxs)("div",{children:[i.jsx("p",{className:"font-semibold mb-1",children:"How to use:"}),i.jsx("p",{className:"text-sm",children:"Type the code in square brackets in TikTok comments, like [smile], and it will display the corresponding hidden emoji. These emojis are only visible on TikTok!"})]})]})})}),i.jsx("div",{className:"mb-8",children:(0,i.jsxs)("div",{className:"relative max-w-md",children:[i.jsx(a.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),i.jsx(l.I,{type:"search",placeholder:"Search hidden emojis...",className:"pl-10",value:e,onChange:e=>s(e.target.value)})]})}),i.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-12",children:t.map(e=>i.jsx(d.f,{emoji:e.emoji,code:e.code,name:e.name,usage:e.usage,imageUrl:e.imageUrl,description:e.description},e.code))}),0===t.length&&i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-muted-foreground",children:"No matching emojis found"})}),i.jsx(c.Zb,{className:"bg-gradient-to-br from-pink-50 to-blue-50 border-pink-200 mb-8",children:(0,i.jsxs)(c.aY,{className:"pt-6",children:[i.jsx("h3",{className:"text-xl font-bold mb-6 text-center",children:"\uD83D\uDCDA TikTok Emoji Meanings"}),i.jsx("p",{className:"text-sm text-muted-foreground mb-6 text-center",children:"Complete list of TikTok hidden emoji meanings and descriptions"}),i.jsx("div",{className:"grid gap-3 max-h-96 overflow-y-auto",children:m.iN.map(e=>(0,i.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-white/70 rounded-lg border border-white/50",children:[i.jsx("div",{className:"flex-shrink-0",children:i.jsx("span",{className:"text-lg font-mono text-pink-600 bg-pink-100 px-2 py-1 rounded text-xs",children:e.code})}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[i.jsx("span",{className:"font-semibold text-sm",children:e.name}),e.usage&&(0,i.jsxs)("span",{className:"text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded",children:[e.usage," uses"]})]}),i.jsx("p",{className:"text-xs text-muted-foreground leading-relaxed",children:e.description})]})]},e.code))})]})}),i.jsx(c.Zb,{className:"bg-muted",children:(0,i.jsxs)(c.aY,{className:"pt-6",children:[i.jsx("h3",{className:"text-lg font-semibold mb-4",children:"\uD83C\uDFAF Pro Tips"}),(0,i.jsxs)("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[i.jsx("li",{children:"• These hidden emojis are only visible within TikTok - they'll appear as text codes on other platforms"}),i.jsx("li",{children:"• You can combine multiple hidden emojis, like [happy][happy][happy] for super happy"}),i.jsx("li",{children:"• Some hidden emojis may display slightly differently in different regions"}),i.jsx("li",{children:"• Bookmark this page for easy access whenever you need these codes"})]})]})})]})}},6333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(6557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2672:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>r});var i=t(9510);let o=(0,t(8570).createProxy)(String.raw`/Users/<USER>/develop/GitHub/tiktokemoji/components/hidden-emojis-client.tsx#default`),r={title:"46 TikTok Hidden Emoji Codes - Complete List 2024",description:"Complete list of 46 TikTok hidden emoji codes with meanings and usage. Discover secret emojis like [smile], [happy], [angry] and more. Copy with one click!",keywords:["TikTok hidden emojis","secret emoji codes","TikTok symbols","[smile] emoji","[happy] emoji","TikTok comment tricks"],openGraph:{title:"46 TikTok Hidden Emoji Codes - Complete List",description:"All TikTok secret emoji codes in one place. Learn how to use hidden emojis in comments.",type:"website",images:[{url:"/og-hidden-emojis.png",width:1200,height:630,alt:"TikTok Hidden Emoji Codes Complete List"}]},twitter:{card:"summary_large_image",title:"46 TikTok Hidden Emoji Codes",description:"All TikTok secret emoji codes in one place",images:["/og-hidden-emojis.png"]},alternates:{canonical:"/hidden-emojis"}};function n(){return i.jsx(o,{})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[948,28,108,87,917],()=>t(3354));module.exports=i})();