(()=>{var e={};e.id=797,e.ids=[797],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4412:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),t(3243),t(6537),t(5866);var a=t(3191),i=t(8716),n=t(7922),l=t.n(n),r=t(5231),o={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>r[e]);t.d(s,o);let d=["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3243)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/search/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6537)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/develop/GitHub/tiktokemoji/app/search/page.tsx"],m="/search/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8661:(e,s,t)=>{Promise.resolve().then(t.bind(t,734))},734:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(326),i=t(7577),n=t(5047),l=t(434),r=t(6333),o=t(8307),d=t(772),c=t(4432),m=t(2643),x=t(6245),u=t(2056),h=t(3427);function p(){let e=(0,n.useSearchParams)(),[s,t]=(0,i.useState)(e?.get("q")||""),[p,j]=(0,i.useState)(e?.get("category")||"all"),[g,f]=(0,i.useState)("all"),v=u.iN.filter(e=>(!s||e.code.toLowerCase().includes(s.toLowerCase())||e.name.toLowerCase().includes(s.toLowerCase()))&&("all"===p||"hidden"===p)),b=u.AH.filter(e=>{let t=!s||e.char.includes(s)||e.name.toLowerCase().includes(s.toLowerCase())||e.meaning.toLowerCase().includes(s.toLowerCase())||e.tiktokMeaning&&e.tiktokMeaning.toLowerCase().includes(s.toLowerCase()),a="all"===p||e.category.toLowerCase()===p.toLowerCase();return t&&a}),N=u.E$.filter(e=>!s||e.emojis.includes(s)||e.meaning.toLowerCase().includes(s.toLowerCase())||e.usage.toLowerCase().includes(s.toLowerCase())),y=v.length+b.length+N.length;return(0,a.jsxs)("div",{className:"container py-8",children:[(0,a.jsxs)(l.default,{href:"/",className:"inline-flex items-center text-muted-foreground hover:text-foreground mb-6",children:[a.jsx(r.Z,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),a.jsx("h1",{className:"text-4xl font-bold mb-8",children:"Search Emojis"}),a.jsx("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"relative max-w-2xl",children:[a.jsx(o.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5"}),a.jsx(c.I,{type:"search",placeholder:"Search emojis, codes, or meanings...",className:"pl-10 text-lg",value:s,onChange:e=>t(e.target.value)})]})}),!s&&"all"!==p&&a.jsx("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx("span",{className:"text-muted-foreground",children:"Current category:"}),a.jsx("span",{className:"font-semibold",children:u.lS.find(e=>e.id===p)?.name||"All"}),a.jsx(d.z,{size:"sm",variant:"ghost",onClick:()=>j("all"),children:"Clear Filter"})]})}),s&&(0,a.jsxs)("p",{className:"text-muted-foreground mb-6",children:["Found ",a.jsx("span",{className:"font-semibold text-foreground",children:y})," results"]}),(0,a.jsxs)(h.mQ,{value:g,onValueChange:f,className:"w-full",children:[(0,a.jsxs)(h.dr,{className:"grid w-full max-w-md grid-cols-4",children:[(0,a.jsxs)(h.SP,{value:"all",children:["All (",y,")"]}),(0,a.jsxs)(h.SP,{value:"hidden",children:["Hidden (",v.length,")"]}),(0,a.jsxs)(h.SP,{value:"emoji",children:["Emojis (",b.length,")"]}),(0,a.jsxs)(h.SP,{value:"combo",children:["Combos (",N.length,")"]})]}),(0,a.jsxs)(h.nU,{value:"all",className:"mt-6 space-y-8",children:[v.length>0&&(0,a.jsxs)("section",{children:[a.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Hidden Emoji Codes"}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:v.slice(0,12).map(e=>a.jsx(x.f,{emoji:e.emoji,code:e.code,name:e.name,usage:e.usage},e.code))}),v.length>12&&a.jsx("div",{className:"mt-4 text-center",children:(0,a.jsxs)(d.z,{variant:"outline",onClick:()=>f("hidden"),children:["View all ",v.length," hidden emojis"]})})]}),b.length>0&&(0,a.jsxs)("section",{children:[a.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Emojis"}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:b.slice(0,8).map(e=>a.jsx(l.default,{href:`/emoji/${encodeURIComponent(e.char)}`,children:a.jsx(m.Zb,{className:"hover:shadow-lg transition-shadow cursor-pointer h-full",children:(0,a.jsxs)(m.aY,{className:"pt-6 text-center",children:[a.jsx("div",{className:"text-4xl mb-2",children:e.char}),a.jsx("h3",{className:"font-semibold",children:e.name}),a.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:e.tiktokMeaning||e.meaning})]})})},e.char))}),b.length>8&&a.jsx("div",{className:"mt-4 text-center",children:(0,a.jsxs)(d.z,{variant:"outline",onClick:()=>f("emoji"),children:["View all ",b.length," emojis"]})})]}),N.length>0&&(0,a.jsxs)("section",{children:[a.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Popular Combinations"}),a.jsx("div",{className:"space-y-3",children:N.slice(0,4).map(e=>a.jsx(m.Zb,{children:a.jsx(m.aY,{className:"pt-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("span",{className:"text-2xl",children:e.emojis}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-semibold",children:e.meaning}),a.jsx("p",{className:"text-sm text-muted-foreground",children:e.usage})]})]}),a.jsx(d.z,{size:"sm",variant:"outline",onClick:()=>navigator.clipboard.writeText(e.emojis),children:"Copy"})]})})},e.id))}),N.length>4&&a.jsx("div",{className:"mt-4 text-center",children:(0,a.jsxs)(d.z,{variant:"outline",onClick:()=>f("combo"),children:["View all ",N.length," combinations"]})})]})]}),a.jsx(h.nU,{value:"hidden",className:"mt-6",children:a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:v.map(e=>a.jsx(x.f,{emoji:e.emoji,code:e.code,name:e.name,usage:e.usage},e.code))})}),a.jsx(h.nU,{value:"emoji",className:"mt-6",children:a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:b.map(e=>a.jsx(l.default,{href:`/emoji/${encodeURIComponent(e.char)}`,children:a.jsx(m.Zb,{className:"hover:shadow-lg transition-shadow cursor-pointer h-full",children:(0,a.jsxs)(m.aY,{className:"pt-6 text-center",children:[a.jsx("div",{className:"text-4xl mb-2",children:e.char}),a.jsx("h3",{className:"font-semibold",children:e.name}),a.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:e.tiktokMeaning||e.meaning})]})})},e.char))})}),a.jsx(h.nU,{value:"combo",className:"mt-6",children:a.jsx("div",{className:"space-y-3",children:N.map(e=>a.jsx(m.Zb,{children:a.jsx(m.aY,{className:"pt-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("span",{className:"text-2xl",children:e.emojis}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-semibold",children:e.meaning}),a.jsx("p",{className:"text-sm text-muted-foreground",children:e.usage})]})]}),a.jsx(d.z,{size:"sm",variant:"outline",onClick:()=>navigator.clipboard.writeText(e.emojis),children:"Copy"})]})})},e.id))})})]}),0===y&&s&&a.jsx(m.Zb,{className:"mt-8",children:(0,a.jsxs)(m.aY,{className:"pt-6 text-center",children:[(0,a.jsxs)("p",{className:"text-muted-foreground mb-4",children:['No results found for "',s,'"']}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-sm",children:"Try these suggestions:"}),(0,a.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[a.jsx("li",{children:"• Check your spelling"}),a.jsx("li",{children:"• Use shorter keywords"}),a.jsx("li",{children:"• Try searching for the emoji itself"})]})]})]})}),!s&&"all"===p&&(0,a.jsxs)("div",{className:"mt-12",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"\uD83D\uDD25 Popular Searches"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:["smile","happy","love","\uD83D\uDE0D","\uD83D\uDD25","POV","help","not me"].map(e=>a.jsx(d.z,{variant:"outline",size:"sm",onClick:()=>t(e),children:e},e))})]})]})}function j(){return a.jsx(i.Suspense,{fallback:a.jsx("div",{className:"container py-8",children:a.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:a.jsx("p",{className:"text-muted-foreground",children:"Loading..."})})}),children:a.jsx(p,{})})}},6245:(e,s,t)=>{"use strict";t.d(s,{f:()=>x});var a=t(326),i=t(2643),n=t(772),l=t(4008),r=t(3810),o=t(7863),d=t(7577),c=t(6226);function m({code:e,emoji:s,imageUrl:t,name:i,size:n="md",showFallback:l=!0}){let[r,o]=(0,d.useState)(!1),m={sm:"w-8 h-8 text-2xl",md:"w-12 h-12 text-4xl",lg:"w-16 h-16 text-6xl"};return t&&!r?a.jsx("div",{className:`${m[n]} relative flex items-center justify-center`,children:a.jsx(c.default,{src:t,alt:`TikTok ${i} emoji`,width:"sm"===n?32:"md"===n?48:64,height:"sm"===n?32:"md"===n?48:64,className:"rounded-full",onError:()=>o(!0),unoptimized:!0})}):l?a.jsx("div",{className:`${m[n]} flex items-center justify-center`,children:a.jsx("span",{className:"select-none",children:s})}):a.jsx("div",{className:`${m[n]} flex items-center justify-center bg-gray-100 rounded-full text-xs font-mono text-gray-600`,children:e})}function x({emoji:e,code:s,name:t,usage:d,className:c,size:x="md",imageUrl:u,description:h}){let{toast:p}=(0,l.pm)();return a.jsx(i.Zb,{className:(0,o.cn)("relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden","before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100",{sm:"p-2 sm:p-3",md:"p-3 sm:p-4",lg:"p-4 sm:p-6"}[x],c),children:(0,a.jsxs)("div",{className:"relative z-10 text-center",children:[a.jsx("div",{className:"mb-3 flex justify-center",children:a.jsx(m,{code:s||"",emoji:e,imageUrl:u,name:t||"",size:x})}),s&&a.jsx("code",{className:"inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono",children:s}),t&&a.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground mb-1 font-medium",children:t}),d&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mb-2 sm:mb-3",children:["Usage: ",d]}),(0,a.jsxs)(n.z,{size:"sm",variant:"outline",className:"w-full text-xs sm:text-sm",onClick:()=>{navigator.clipboard.writeText(s||e),p({title:"Copied to clipboard!",description:`${s||e} copied successfully`})},children:[a.jsx(r.Z,{className:"w-3 h-3 mr-1 sm:mr-2"}),a.jsx("span",{className:"hidden sm:inline",children:"Copy"}),a.jsx("span",{className:"sm:hidden",children:"Copy"})]})]})})}},3427:(e,s,t)=>{"use strict";t.d(s,{mQ:()=>D,nU:()=>_,dr:()=>S,SP:()=>M});var a=t(326),i=t(7577),n=t(2561),l=t(3095),r=t(5594),o=t(9815),d=t(5226),c=t(7124),m=t(2067),x=t(8957),u="Tabs",[h,p]=(0,l.b)(u,[r.Pc]),j=(0,r.Pc)(),[g,f]=h(u),v=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,onValueChange:n,defaultValue:l,orientation:r="horizontal",dir:o,activationMode:h="automatic",...p}=e,j=(0,c.gm)(o),[f,v]=(0,m.T)({prop:i,onChange:n,defaultProp:l??"",caller:u});return(0,a.jsx)(g,{scope:t,baseId:(0,x.M)(),value:f,onValueChange:v,orientation:r,dir:j,activationMode:h,children:(0,a.jsx)(d.WV.div,{dir:j,"data-orientation":r,...p,ref:s})})});v.displayName=u;var b="TabsList",N=i.forwardRef((e,s)=>{let{__scopeTabs:t,loop:i=!0,...n}=e,l=f(b,t),o=j(t);return(0,a.jsx)(r.fC,{asChild:!0,...o,orientation:l.orientation,dir:l.dir,loop:i,children:(0,a.jsx)(d.WV.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:s})})});N.displayName=b;var y="TabsTrigger",w=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,disabled:l=!1,...o}=e,c=f(y,t),m=j(t),x=P(c.baseId,i),u=z(c.baseId,i),h=i===c.value;return(0,a.jsx)(r.ck,{asChild:!0,...m,focusable:!l,active:h,children:(0,a.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":u,"data-state":h?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:x,...o,ref:s,onMouseDown:(0,n.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(i)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(i)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||l||!e||c.onValueChange(i)})})})});w.displayName=y;var k="TabsContent",C=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:n,forceMount:l,children:r,...c}=e,m=f(k,t),x=P(m.baseId,n),u=z(m.baseId,n),h=n===m.value,p=i.useRef(h);return i.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(o.z,{present:l||h,children:({present:t})=>(0,a.jsx)(d.WV.div,{"data-state":h?"active":"inactive","data-orientation":m.orientation,role:"tabpanel","aria-labelledby":x,hidden:!t,id:u,tabIndex:0,...c,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:t&&r})})});function P(e,s){return`${e}-trigger-${s}`}function z(e,s){return`${e}-content-${s}`}C.displayName=k;var L=t(7863);let D=v,S=i.forwardRef(({className:e,...s},t)=>a.jsx(N,{ref:t,className:(0,L.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));S.displayName=N.displayName;let M=i.forwardRef(({className:e,...s},t)=>a.jsx(w,{ref:t,className:(0,L.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));M.displayName=w.displayName;let _=i.forwardRef(({className:e,...s},t)=>a.jsx(C,{ref:t,className:(0,L.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));_.displayName=C.displayName},6333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3243:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`/Users/<USER>/develop/GitHub/tiktokemoji/app/search/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[948,28,108,87,917],()=>t(4412));module.exports=a})();