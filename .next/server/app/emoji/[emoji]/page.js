(()=>{var e={};e.id=531,e.ids=[531],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8607:(e,a,n)=>{"use strict";n.r(a),n.d(a,{GlobalError:()=>r.a,__next_app__:()=>g,originalPathname:()=>d,pages:()=>m,routeModule:()=>u,tree:()=>l}),n(3500),n(6537),n(5866);var t=n(3191),i=n(8716),s=n(7922),r=n.n(s),o=n(5231),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);n.d(a,c);let l=["",{children:["emoji",{children:["[emoji]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,3500)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/emoji/[emoji]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,6537)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,5866,23)),"next/dist/client/components/not-found-error"]}],m=["/Users/<USER>/develop/GitHub/tiktokemoji/app/emoji/[emoji]/page.tsx"],d="/emoji/[emoji]/page",g={require:n,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/emoji/[emoji]/page",pathname:"/emoji/[emoji]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2197:(e,a,n)=>{Promise.resolve().then(n.bind(n,4512))},4512:(e,a,n)=>{"use strict";n.d(a,{default:()=>h});var t=n(326),i=n(434),s=n(6333),r=n(3810),o=n(7069);let c=(0,n(6557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);var l=n(772),m=n(2643),d=n(567),g=n(2056),u=n(4008);function h({emoji:e}){let a=decodeURIComponent(e),n=g.AH.find(e=>e.char===a);return n?t.jsx(p,{emoji:n}):t.jsx(p,{emoji:{char:a,name:"Emoji",meaning:"This is an emoji",tiktokMeaning:"Special meaning on TikTok",category:"Emotions",usage:"⭐⭐⭐"}})}function p({emoji:e}){let{toast:a}=(0,u.pm)(),n=[{combo:`${e.char}${e.char}${e.char}`,meaning:"Emphasis x3"},{combo:`${e.char}🤣`,meaning:"With laughing"},{combo:`help${e.char}`,meaning:"Help + emoji"},{combo:`not me${e.char}`,meaning:"Not me + emoji"}],h=(e,n="Emoji")=>{navigator.clipboard.writeText(e),a({title:"Copied!",description:`${n} copied to clipboard`})};return(0,t.jsxs)("div",{className:"container py-8",children:[(0,t.jsxs)(i.default,{href:"/",className:"inline-flex items-center text-muted-foreground hover:text-foreground mb-6",children:[t.jsx(s.Z,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[t.jsx(m.Zb,{children:t.jsx(m.aY,{className:"pt-8",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-8xl mb-4",children:e.char}),t.jsx("h1",{className:"text-3xl font-bold mb-2",children:e.name}),t.jsx("p",{className:"text-muted-foreground mb-4",children:e.meaning}),(0,t.jsxs)(l.z,{size:"lg",className:"bg-tiktok-pink hover:bg-pink-600",onClick:()=>h(e.char),children:[t.jsx(r.Z,{className:"w-4 h-4 mr-2"}),"Copy Emoji"]})]})})}),e.tiktokMeaning&&(0,t.jsxs)(m.Zb,{children:[t.jsx(m.Ol,{children:(0,t.jsxs)(m.ll,{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-tiktok-pink",children:"TikTok"})," Special Meaning"]})}),t.jsx(m.aY,{children:t.jsx("p",{className:"text-lg",children:e.tiktokMeaning})})]}),(0,t.jsxs)(m.Zb,{children:[t.jsx(m.Ol,{children:t.jsx(m.ll,{children:"Popular Combinations"})}),t.jsx(m.aY,{children:t.jsx("div",{className:"grid gap-3",children:n.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg hover:bg-muted/80 cursor-pointer",onClick:()=>h(e.combo,"Combination"),children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"text-lg font-mono",children:e.combo}),t.jsx("p",{className:"text-sm text-muted-foreground",children:e.meaning})]}),t.jsx(r.Z,{className:"w-4 h-4 text-muted-foreground"})]},a))})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(m.Zb,{children:[t.jsx(m.Ol,{children:(0,t.jsxs)(m.ll,{className:"flex items-center gap-2",children:[t.jsx(o.Z,{className:"w-5 h-5"}),"Usage Statistics"]})}),t.jsx(m.aY,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Daily Usage"}),t.jsx("span",{className:"font-semibold",children:"2.3M"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Popularity Rank"}),t.jsx("span",{className:"font-semibold",children:"#12"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Trend"}),t.jsx("span",{className:"font-semibold text-green-600",children:"+15%"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Emotion"}),t.jsx("span",{className:"font-semibold",children:"85% positive"})]})]})})]}),(0,t.jsxs)(m.Zb,{children:[t.jsx(m.Ol,{children:t.jsx(m.ll,{children:"Category & Tags"})}),t.jsx(m.aY,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Category"}),t.jsx(d.C,{variant:"secondary",children:e.category})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Usage"}),t.jsx("div",{className:"flex gap-1",children:e.usage&&e.usage.split("").map((e,a)=>t.jsx(c,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"},a))})]})]})})]}),(0,t.jsxs)(m.Zb,{children:[t.jsx(m.Ol,{children:t.jsx(m.ll,{children:"Related Emojis"})}),t.jsx(m.aY,{children:t.jsx("div",{className:"grid grid-cols-4 gap-2",children:g.AH.filter(a=>a.category===e.category&&a.char!==e.char).slice(0,8).map(e=>t.jsx(i.default,{href:`/emoji/${encodeURIComponent(e.char)}`,className:"text-2xl hover:scale-110 transition-transform text-center p-2",title:e.name,children:e.char},e.char))})})]})]})]})]})}},567:(e,a,n)=>{"use strict";n.d(a,{C:()=>o});var t=n(326);n(7577);var i=n(9360),s=n(7863);let r=(0,i.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,...n}){return t.jsx("div",{className:(0,s.cn)(r({variant:a}),e),...n})}},6333:(e,a,n)=>{"use strict";n.d(a,{Z:()=>t});let t=(0,n(6557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3810:(e,a,n)=>{"use strict";n.d(a,{Z:()=>t});let t=(0,n(6557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},7069:(e,a,n)=>{"use strict";n.d(a,{Z:()=>t});let t=(0,n(6557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3500:(e,a,n)=>{"use strict";n.r(a),n.d(a,{default:()=>c,generateMetadata:()=>r,generateStaticParams:()=>o});var t=n(9510);let i=(0,n(8570).createProxy)(String.raw`/Users/<USER>/develop/GitHub/tiktokemoji/components/emoji-detail-client.tsx#default`),s=[{char:"❤️",name:"Red Heart",meaning:"Symbol of love and affection",tiktokMeaning:"Universal love, support, or appreciation",category:"Emotions",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83D\uDC80",name:"Skull",meaning:"Skull head",tiktokMeaning:'"I\'m dead" - indicates something is extremely funny',category:"Emotions",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83D\uDE2D",name:"Loudly Crying",meaning:"Can represent actual crying or being moved to tears",tiktokMeaning:"Indicates being very touched or laughing until crying",category:"Emotions",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83E\uDEF6\uD83C\uDFFB",name:"Heart Hands",meaning:"Making a heart shape with hands",tiktokMeaning:"Showing love, appreciation, or gratitude",category:"Gestures",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83D\uDE02",name:"Face with Tears of Joy",meaning:"Laughing so hard it causes tears",tiktokMeaning:"Classic laughing emoji, though Gen Z uses it less",category:"Emotions",usage:"⭐⭐⭐⭐"},{char:"✅",name:"Check Mark Button",meaning:"Green checkmark indicating completion",tiktokMeaning:"Agreement, confirmation, or task completion",category:"Symbols",usage:"⭐⭐⭐⭐⭐"},{char:"✨",name:"Sparkles",meaning:"Shining and sparkling",tiktokMeaning:"Used for emphasis, sarcasm, or adding magic to content",category:"Symbols",usage:"⭐⭐⭐⭐⭐"},{char:"⭐️",name:"Star",meaning:"Five-pointed star",tiktokMeaning:"Shows appreciation, rating, or highlighting something special",category:"Symbols",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83E\uDD7A",name:"Pleading Face",meaning:"Pitiful expression with big eyes",tiktokMeaning:"Shows grievance, pleading, or acting cute",category:"Emotions",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83E\uDEE1",name:"Saluting Face",meaning:"Face giving a military salute",tiktokMeaning:"Shows respect or obedience, often used sarcastically",category:"Emotions",usage:"⭐⭐⭐⭐"},{char:"\uD83E\uDEE0",name:"Melting Face",meaning:"Face that appears to be melting",tiktokMeaning:"Used for embarrassment, extreme heat, or being overwhelmed",category:"Emotions",usage:"⭐⭐⭐⭐⭐"},{char:"❤️‍\uD83E\uDE79",name:"Mending Heart",meaning:"Heart with a bandage",tiktokMeaning:"Represents healing from heartbreak or emotional recovery",category:"Emotions",usage:"⭐⭐⭐⭐"},{char:"\uD83E\uDE77",name:"Pink Heart",meaning:"Light pink colored heart",tiktokMeaning:"Represents friendship, warmth, or soft love",category:"Emotions",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83E\uDEF5",name:"Index Pointing at Viewer",meaning:"Finger pointing directly at the viewer",tiktokMeaning:'Used for direct address, calling someone out, or "you" references',category:"Gestures",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83D\uDDFF",name:"Moai",meaning:"Easter Island stone statue",tiktokMeaning:"Deadpan reaction, unimpressed response, or sarcastic agreement",category:"Objects",usage:"⭐⭐⭐⭐"},{char:"\uD83E\uDDFF",name:"Nazar Amulet",meaning:"Blue eye-shaped amulet",tiktokMeaning:"Protection from evil eye, spiritual vibes, or aesthetic purposes",category:"Symbols",usage:"⭐⭐⭐"},{char:"\uD83E\uDD0C\uD83C\uDFFB",name:"Pinched Fingers",meaning:"Hand gesture with fingers pinched together",tiktokMeaning:'Italian gesture meaning "what" or showing perfection',category:"Gestures",usage:"⭐⭐⭐⭐"},{char:"\uD83D\uDE4F\uD83C\uDFFB",name:"Folded Hands",meaning:"Hands pressed together in prayer",tiktokMeaning:'Prayer, gratitude, pleading, or saying "please"',category:"Gestures",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83D\uDD25",name:"Fire",meaning:"Flame",tiktokMeaning:"Indicates something is hot, awesome, or trending",category:"Symbols",usage:"⭐⭐⭐⭐⭐"},{char:"\uD83D\uDC40",name:"Eyes",meaning:"Pair of eyes looking",tiktokMeaning:"Drawing attention, watching drama, or expressing interest",category:"Body Parts",usage:"⭐⭐⭐⭐⭐"}];async function r({params:e}){let a=decodeURIComponent(e.emoji),n=s.find(e=>e.char===a),t=n?`${n.char} ${n.name} - TikTok Emoji Meaning`:`${a} - TikTok Emoji`,i=n?`Learn about ${n.char} ${n.name} on TikTok: ${n.tiktokMeaning||n.meaning}. Discover popular combinations and usage tips.`:`Explore ${a} emoji on TikTok. Find meanings, combinations, and usage tips.`;return{title:t,description:i,openGraph:{title:t,description:i,type:"article",images:[{url:`/api/og?emoji=${encodeURIComponent(a)}`,width:1200,height:630,alt:t}]},twitter:{card:"summary_large_image",title:t,description:i},alternates:{canonical:`/emoji/${e.emoji}`}}}async function o(){return s.slice(0,20).map(e=>({emoji:encodeURIComponent(e.char)}))}function c({params:e}){return t.jsx(i,{emoji:e.emoji})}}};var a=require("../../../webpack-runtime.js");a.C(e);var n=e=>a(a.s=e),t=a.X(0,[948,28,87,917],()=>n(8607));module.exports=t})();