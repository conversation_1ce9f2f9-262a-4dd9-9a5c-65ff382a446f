<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/bc19b31da0bd50c9.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-1b400f1de436fe28.js"/><script src="/_next/static/chunks/fd9d1056-346a4d4db4c146e1.js" async=""></script><script src="/_next/static/chunks/117-4ced7f814a77fa18.js" async=""></script><script src="/_next/static/chunks/main-app-31faf2160eb37e61.js" async=""></script><script src="/_next/static/chunks/438-3d6b98e82ab59c7b.js" async=""></script><script src="/_next/static/chunks/150-dde69c0d40954ea8.js" async=""></script><script src="/_next/static/chunks/672-2b366b7389252487.js" async=""></script><script src="/_next/static/chunks/app/page-a448ec3e013efd3a.js" async=""></script><script src="/_next/static/chunks/52-e89dc12467f890af.js" async=""></script><script src="/_next/static/chunks/367-70ef8c60d69865f7.js" async=""></script><script src="/_next/static/chunks/781-3944b942f49751e0.js" async=""></script><script src="/_next/static/chunks/435-dc8290f2b1c966aa.js" async=""></script><script src="/_next/static/chunks/2-bcbe5cd3b8eae62a.js" async=""></script><script src="/_next/static/chunks/app/layout-0af87e3f0fffa538.js" async=""></script><title>TikTok Emoji - Hidden Codes &amp; Popular Combinations</title><meta name="description" content="Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings. Copy with one click to make your TikTok comments more engaging!"/><meta name="author" content="TikTok Emoji Team"/><meta name="keywords" content="TikTok emoji,hidden emoji codes,TikTok symbols,emoji combinations,TikTok comments,secret emojis"/><meta name="creator" content="TikTok Emoji"/><meta name="publisher" content="TikTok Emoji"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="google-site-verification" content="google-site-verification-code"/><meta property="og:title" content="TikTok Emoji - Hidden Codes &amp; Popular Combinations"/><meta property="og:description" content="Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings."/><meta property="og:url" content="https://tiktokemoji.com"/><meta property="og:site_name" content="TikTok Emoji"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="https://tiktokemoji.com/og-image.png"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="TikTok Emoji - Hidden Codes &amp; Popular Combinations"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="TikTok Emoji - Hidden Codes &amp; Popular Combinations"/><meta name="twitter:description" content="Discover 46+ TikTok hidden emoji codes and popular combinations"/><meta name="twitter:image" content="https://tiktokemoji.com/og-image.png"/><meta name="next-size-adjust"/><script type="application/ld+json">{"@context":"https://schema.org","@type":"WebSite","name":"TikTok Emoji","alternateName":"TikTok Hidden Emoji Codes","url":"https://tiktokemoji.com","description":"Discover TikTok hidden emoji codes, popular combinations, and special meanings","potentialAction":{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://tiktokemoji.com/search?q={search_term_string}"},"query-input":"required name=search_term_string"},"publisher":{"@type":"Organization","name":"TikTok Emoji","url":"https://tiktokemoji.com","logo":{"@type":"ImageObject","url":"https://tiktokemoji.com/logo.png","width":600,"height":60}},"sameAs":[]}</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"><div class="container flex h-16 items-center justify-between"><a class="flex items-center space-x-2 flex-shrink-0" href="/"><span class="text-2xl">🎵</span><span class="font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">TikTok Emoji</span></a><form class="relative flex-1 max-w-2xl mx-8"><input type="search" class="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-10 w-full" placeholder="Search emojis, codes, or meanings..." value=""/><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 absolute right-0 top-0" type="submit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search h-4 w-4"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg></button></form><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 gap-2 flex-shrink-0" type="button" id="radix-:Rqkq:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-grid3x3 h-4 w-4"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M3 15h18"></path><path d="M9 3v18"></path><path d="M15 3v18"></path></svg>Categories<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3 w-3"><path d="m6 9 6 6 6-6"></path></svg></button></div></header><main class="min-h-screen"><main class="overflow-x-hidden" role="main"><section class="relative overflow-hidden bg-gradient-to-br from-pink-50 via-purple-50/30 to-blue-50 py-24 md:py-32" aria-label="Hero section introducing TikTok emoji tools"><div class="absolute inset-0 overflow-hidden"><div class="absolute top-[10%] left-[15%] w-64 h-64 bg-gradient-to-br from-tiktok-pink/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div><div class="absolute bottom-[10%] right-[15%] w-64 h-64 bg-gradient-to-br from-tiktok-blue/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse animation-delay-2000"></div></div><div class="absolute inset-0 overflow-hidden pointer-events-none"><div class="absolute top-[15%] left-[10%] text-6xl opacity-20 animate-float">🎵</div><div class="absolute top-[20%] right-[15%] text-5xl opacity-25 animate-float animation-delay-1000">✨</div><div class="absolute bottom-[25%] left-[20%] text-5xl opacity-20 animate-float animation-delay-2000">💕</div><div class="absolute bottom-[20%] right-[10%] text-6xl opacity-25 animate-float animation-delay-1500">🔥</div><div class="absolute top-[50%] left-[50%] -translate-x-1/2 -translate-y-1/2 text-5xl opacity-20 animate-float animation-delay-2500">😍</div></div><div class="container relative z-10"><header class="text-center max-w-4xl mx-auto"><h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 bg-gradient-to-r from-tiktok-pink via-purple-500 to-tiktok-blue bg-clip-text text-transparent leading-tight">Unlock TikTok&#x27;s Secret Emojis</h1><p class="text-lg md:text-xl text-muted-foreground mb-16 max-w-2xl mx-auto leading-relaxed">46 exclusive emoji codes • One-click copy • Used by millions daily • Only work in TikTok app</p><nav aria-label="Main navigation to emoji tools" class="flex flex-col sm:flex-row gap-4 justify-center items-center"><a aria-describedby="hidden-emojis-desc" href="/hidden-emojis"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-tiktok-pink to-pink-500 text-white hover:opacity-90 h-11 rounded-md px-8 gap-2 min-w-[200px] shadow-lg hover:shadow-xl transition-all hover:scale-105"><span class="text-xl" role="img" aria-label="Target emoji">🎯</span>Hidden Emojis</button></a><span id="hidden-emojis-desc" class="sr-only">Explore TikTok&#x27;s secret emoji codes</span><a aria-describedby="popular-combos-desc" href="/popular-combos"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:text-accent-foreground h-11 rounded-md px-8 gap-2 min-w-[200px] hover:bg-muted/50 transition-all hover:scale-105"><span class="text-xl" role="img" aria-label="Fire emoji">🔥</span>Popular Combos</button></a><span id="popular-combos-desc" class="sr-only">Browse trending emoji combinations</span><a aria-describedby="title-generator-desc" href="/title-generator"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:text-accent-foreground h-11 rounded-md px-8 gap-2 min-w-[200px] hover:bg-muted/50 transition-all hover:scale-105"><span class="text-xl" role="img" aria-label="Sparkles emoji">✨</span>Title Generator</button></a><span id="title-generator-desc" class="sr-only">Generate creative TikTok titles with emojis</span></nav></header></div></section><article aria-labelledby="hidden-emojis-heading"><section class="py-12 md:py-16 bg-gradient-to-b from-background to-muted/20" aria-labelledby="hidden-emojis-heading"><div class="container"><header class="flex flex-col md:flex-row md:items-end justify-between mb-8 md:mb-12"><div class="max-w-2xl"><h2 id="hidden-emojis-heading" class="text-3xl md:text-4xl font-bold mb-4 flex items-center gap-3"><span class="text-4xl animate-pulse" role="img" aria-label="Target emoji">🎯</span>TikTok Hidden Emoji Codes</h2><p class="text-lg text-muted-foreground leading-relaxed">Click copy and paste in TikTok comments to unlock secret emojis that aren&#x27;t available anywhere else</p></div><a class="hidden md:block" aria-label="View all 46 hidden emoji codes" href="/hidden-emojis"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-10 px-4 py-2 gap-2 hover:bg-muted/50 transition-all">View All 46 Emojis<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4" aria-hidden="true"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></button></a></header><div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4 md:gap-6 mb-8 md:mb-12" role="grid" aria-label="Preview of hidden emoji codes"><div class="animate-fade-in-up" style="animation-delay:0ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Smile emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/smile.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[smile]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Smile</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:50ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Happy emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/happy.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[happy]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Happy</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:100ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Angry emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/angry.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[angry]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Angry</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:150ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Cry emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/cry.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[cry]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Cry</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:200ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Embarrassed emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/embarrassed.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[embarrassed]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Embarrassed</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:250ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Surprised emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/surprised.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[surprised]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Surprised</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:300ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Wronged emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/wronged.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[wronged]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Wronged</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:350ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Shout emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/shout.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[shout]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Shout</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:400ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Flushed emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/flushed.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[flushed]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Flushed</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:450ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Yummy emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/yummy.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[yummy]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Yummy</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:500ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Complacent emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/complacent.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[complacent]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Complacent</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:550ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Drool emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/drool.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[drool]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Drool</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:600ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Scream emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/scream.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[scream]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Scream</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:650ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Weep emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/weep.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[weep]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Weep</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:700ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Speechless emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/speechless.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[speechless]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Speechless</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div><div class="animate-fade-in-up" style="animation-delay:750ms" role="gridcell"><div class="rounded-lg border bg-card text-card-foreground shadow-sm relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-tiktok-pink/10 before:to-tiktok-blue/10 before:opacity-0 before:transition-opacity hover:before:opacity-100 p-2 sm:p-3"><div class="relative z-10 text-center"><div class="mb-3 flex justify-center"><div class="w-8 h-8 text-2xl relative flex items-center justify-center"><img alt="TikTok Funny Face emoji" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://img.alltiktokemojis.com/tiktok-emojis/funnyface.png"/></div></div><code class="inline-block px-1 sm:px-2 py-0.5 sm:py-1 bg-tiktok-pink/10 text-tiktok-pink rounded text-xs sm:text-sm mb-2 font-mono">[funnyface]</code><p class="text-xs sm:text-sm text-muted-foreground mb-1 font-medium">Funny Face</p><button class="inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs sm:text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3 mr-1 sm:mr-2"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg><span class="hidden sm:inline">Copy</span><span class="sm:hidden">Copy</span></button></div></div></div></div><footer class="text-center md:hidden"><a aria-label="View all 46 hidden emoji codes" href="/hidden-emojis"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:text-accent-foreground h-10 px-4 py-2 gap-2 hover:bg-muted/50 transition-all hover:scale-105">View All 46 Hidden Emojis<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4" aria-hidden="true"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></button></a></footer></div></section></article><article aria-labelledby="how-to-use-heading"><section class="py-12 md:py-16 bg-gradient-to-b from-muted/40 via-muted/60 to-muted/40" aria-labelledby="how-to-use-heading"><div class="container"><header class="text-center mb-8 md:mb-12"><h2 id="how-to-use-heading" class="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-tiktok-pink via-purple-500 to-tiktok-blue bg-clip-text text-transparent">How to Use Hidden Emoji Codes</h2><p class="text-lg text-muted-foreground max-w-xl mx-auto leading-relaxed">Transform your TikTok comments in 3 simple steps and stand out from the crowd</p></header><ol class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto" role="list"><li class="relative group"><div class="absolute inset-0 bg-gradient-to-br from-tiktok-pink/20 to-purple-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" aria-hidden="true"></div><div class="relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"><div class="w-20 h-20 bg-gradient-to-br from-tiktok-pink to-purple-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300" aria-label="Step 1">1</div><h3 class="text-xl font-semibold mb-4 text-center">Copy the Code</h3><p class="text-muted-foreground text-center leading-relaxed">Click the copy button next to any emoji code to copy it to your clipboard instantly</p></div></li><li class="relative group"><div class="absolute inset-0 bg-gradient-to-br from-tiktok-blue/20 to-cyan-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" aria-hidden="true"></div><div class="relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"><div class="w-20 h-20 bg-gradient-to-br from-tiktok-blue to-cyan-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300" aria-label="Step 2">2</div><h3 class="text-xl font-semibold mb-4 text-center">Paste in TikTok</h3><p class="text-muted-foreground text-center leading-relaxed">Open TikTok and paste the code in any comment or caption field</p></div></li><li class="relative group"><div class="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500" aria-hidden="true"></div><div class="relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"><div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300" aria-label="Step 3">3</div><h3 class="text-xl font-semibold mb-4 text-center">Watch the Magic</h3><p class="text-muted-foreground text-center leading-relaxed">The code automatically transforms into the colorful TikTok emoji</p></div></li></ol><div class="mt-8 md:mt-12 text-center"><div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200/50 rounded-2xl p-6 max-w-3xl mx-auto"><p class="text-sm text-muted-foreground leading-relaxed">💡 <strong class="text-yellow-700">Pro Tip:</strong> These emojis only work within the TikTok app. Outside TikTok, they&#x27;ll appear as regular text codes.</p></div></div></div></section></article><article aria-labelledby="trending-content-heading"><section class="py-12 md:py-16 bg-gradient-to-b from-muted/20 to-muted/40" aria-labelledby="trending-content-heading"><div class="container"><header class="text-center mb-8 md:mb-12"><div class="flex items-center justify-center gap-2 mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up w-8 h-8 text-tiktok-pink" aria-hidden="true"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg><h2 id="trending-content-heading" class="text-3xl md:text-4xl font-bold bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">What&#x27;s Trending on TikTok</h2></div><p class="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">Discover the hottest emoji combinations and trending symbols that TikTokers are using right now</p></header><div class="mb-8 md:mb-12"><div class="flex items-center justify-between mb-8"><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles w-6 h-6 text-tiktok-pink"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path><path d="M5 3v4"></path><path d="M19 17v4"></path><path d="M3 5h4"></path><path d="M17 19h4"></path></svg><h3 class="text-2xl font-bold">Popular Combinations</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 ml-2">Hot</div></div><a class="hidden md:block" href="/popular-combos"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 gap-2">View All<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></button></a></div><div class="grid md:grid-cols-2 gap-4 mb-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-2"><span class="text-3xl group-hover:scale-110 transition-transform">👁👄👁</span><div class="flex-1"><div class="flex items-center justify-between mb-1"><h3 class="font-semibold tracking-tight text-lg">I&#x27;m watching / Shocked</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">3.2<!-- -->M uses</div></div><p class="text-muted-foreground text-sm line-clamp-2">Used when seeing something strange or shocking, reacting to unexpected content, or expressing disbelief</p></div></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 shrink-0 gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-2"><span class="text-3xl group-hover:scale-110 transition-transform">🥺👉👈</span><div class="flex-1"><div class="flex items-center justify-between mb-1"><h3 class="font-semibold tracking-tight text-lg">Shy / Pleading</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">2.8<!-- -->M uses</div></div><p class="text-muted-foreground text-sm line-clamp-2">Used when being cute or asking for help, showing vulnerability, or making a request</p></div></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 shrink-0 gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-2"><span class="text-3xl group-hover:scale-110 transition-transform">😭😭😭</span><div class="flex-1"><div class="flex items-center justify-between mb-1"><h3 class="font-semibold tracking-tight text-lg">Crying from laughter/emotion</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">2.9<!-- -->M uses</div></div><p class="text-muted-foreground text-sm line-clamp-2">Used when something is extremely funny or emotionally touching, expressing overwhelming feelings</p></div></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 shrink-0 gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-2"><span class="text-3xl group-hover:scale-110 transition-transform">💀💀💀</span><div class="flex-1"><div class="flex items-center justify-between mb-1"><h3 class="font-semibold tracking-tight text-lg">Dead from laughing x3</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">1.9<!-- -->M uses</div></div><p class="text-muted-foreground text-sm line-clamp-2">Extremely funny content, when something is so hilarious it &quot;kills&quot; you</p></div></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 shrink-0 gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-2"><span class="text-3xl group-hover:scale-110 transition-transform">🫠🫠🫠</span><div class="flex-1"><div class="flex items-center justify-between mb-1"><h3 class="font-semibold tracking-tight text-lg">Melting from embarrassment</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">1.6<!-- -->M uses</div></div><p class="text-muted-foreground text-sm line-clamp-2">Used when feeling extremely embarrassed, awkward, or overwhelmed by heat/stress</p></div></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 shrink-0 gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-2"><span class="text-3xl group-hover:scale-110 transition-transform">🙃💔</span><div class="flex-1"><div class="flex items-center justify-between mb-1"><h3 class="font-semibold tracking-tight text-lg">Fake smile hiding pain</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">1.4<!-- -->M uses</div></div><p class="text-muted-foreground text-sm line-clamp-2">Used when pretending to be okay while actually hurt, masking true emotions</p></div></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 shrink-0 gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-2"><span class="text-3xl group-hover:scale-110 transition-transform">😮‍💨😤</span><div class="flex-1"><div class="flex items-center justify-between mb-1"><h3 class="font-semibold tracking-tight text-lg">Speechless/Frustrated</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">1.2<!-- -->M uses</div></div><p class="text-muted-foreground text-sm line-clamp-2">Used when feeling exasperated, frustrated, or at a loss for words</p></div></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 shrink-0 gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 border-l-4 border-l-tiktok-pink/50"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="flex items-start justify-between gap-4"><div class="flex-1"><div class="flex items-center gap-3 mb-2"><span class="text-3xl group-hover:scale-110 transition-transform">🤡🎪</span><div class="flex-1"><div class="flex items-center justify-between mb-1"><h3 class="font-semibold tracking-tight text-lg">Circus clown</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs">1.5<!-- -->M uses</div></div><p class="text-muted-foreground text-sm line-clamp-2">Self-deprecation or mocking absurd things, calling out foolish behavior</p></div></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 shrink-0 gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg>Copy</button></div></div></div></div></div><div class="mb-8 md:mb-12"><div class="flex items-center justify-between mb-8"><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up w-6 h-6 text-tiktok-blue"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg><h3 class="text-2xl font-bold">Trending Individual Emojis</h3><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground ml-2">Live</div></div></div><div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"><a href="/emoji/%E2%9D%A4%EF%B8%8F"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">❤️</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Red Heart</h4><p class="text-xs text-muted-foreground line-clamp-2">Universal love, support, or appreciation</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%F0%9F%92%80"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">💀</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Skull</h4><p class="text-xs text-muted-foreground line-clamp-2">&quot;I&#x27;m dead&quot; - indicates something is extremely funny</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%F0%9F%98%AD"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">😭</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Loudly Crying</h4><p class="text-xs text-muted-foreground line-clamp-2">Indicates being very touched or laughing until crying</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%F0%9F%AB%B6%F0%9F%8F%BB"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">🫶🏻</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Heart Hands</h4><p class="text-xs text-muted-foreground line-clamp-2">Showing love, appreciation, or gratitude</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%F0%9F%98%82"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">😂</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Face with Tears of Joy</h4><p class="text-xs text-muted-foreground line-clamp-2">Classic laughing emoji, though Gen Z uses it less</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%E2%9C%85"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">✅</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Check Mark Button</h4><p class="text-xs text-muted-foreground line-clamp-2">Agreement, confirmation, or task completion</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%E2%9C%A8"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">✨</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Sparkles</h4><p class="text-xs text-muted-foreground line-clamp-2">Used for emphasis, sarcasm, or adding magic to content</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%E2%AD%90%EF%B8%8F"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">⭐️</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Star</h4><p class="text-xs text-muted-foreground line-clamp-2">Shows appreciation, rating, or highlighting something special</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%F0%9F%A5%BA"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">🥺</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Pleading Face</h4><p class="text-xs text-muted-foreground line-clamp-2">Shows grievance, pleading, or acting cute</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%F0%9F%AB%A1"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">🫡</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Saluting Face</h4><p class="text-xs text-muted-foreground line-clamp-2">Shows respect or obedience, often used sarcastically</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%F0%9F%AB%A0"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">🫠</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Melting Face</h4><p class="text-xs text-muted-foreground line-clamp-2">Used for embarrassment, extreme heat, or being overwhelmed</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a><a href="/emoji/%E2%9D%A4%EF%B8%8F%E2%80%8D%F0%9F%A9%B9"><div class="rounded-lg border bg-card text-card-foreground shadow-sm group hover:shadow-lg transition-all hover:-translate-y-1 cursor-pointer h-full text-center"><div class="p-6 pt-6 pb-4"><div class="text-4xl mb-3 group-hover:scale-110 transition-transform">❤️‍🩹</div><h4 class="font-semibold text-sm mb-1 line-clamp-1">Mending Heart</h4><p class="text-xs text-muted-foreground line-clamp-2">Represents healing from heartbreak or emotional recovery</p><div class="mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-3 h-3 mx-auto text-muted-foreground group-hover:text-tiktok-blue transition-colors"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></div></div></div></a></div></div><div class="text-center"><div class="flex flex-col sm:flex-row gap-4 justify-center"><a href="/popular-combos"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-11 rounded-md px-8 gap-2 min-w-[200px]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles w-4 h-4"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path><path d="M5 3v4"></path><path d="M19 17v4"></path><path d="M3 5h4"></path><path d="M17 19h4"></path></svg>Explore All Combinations</button></a><a href="/search"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 rounded-md px-8 gap-2 min-w-[200px]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up w-4 h-4"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg>Search Trending Emojis</button></a></div></div></div></section></article><article aria-labelledby="categories-heading"><section class="py-12 md:py-16 bg-gradient-to-b from-background to-muted/30" aria-labelledby="categories-heading"><div class="container"><header class="text-center mb-8 md:mb-12"><h2 id="categories-heading" class="text-3xl md:text-4xl font-bold mb-6 flex items-center justify-center gap-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-grid3x3 w-8 h-8 text-tiktok-pink" aria-hidden="true"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M3 15h18"></path><path d="M9 3v18"></path><path d="M15 3v18"></path></svg>Browse by Category</h2><p class="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">Explore over 1,300 Unicode emojis organized by category. Find the perfect emoji for every mood and occasion.</p></header><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8" role="grid" aria-label="Emoji categories"><div><div class="animate-pulse"><div class="bg-muted rounded-2xl p-8 space-y-4"><div class="flex items-start justify-between"><div class="w-16 h-16 bg-muted-foreground/20 rounded-full"></div><div class="w-8 h-6 bg-muted-foreground/20 rounded"></div></div><div class="space-y-2"><div class="h-4 bg-muted-foreground/20 rounded w-3/4"></div><div class="h-3 bg-muted-foreground/20 rounded w-full"></div><div class="h-3 bg-muted-foreground/20 rounded w-2/3"></div></div></div></div></div><div><div class="animate-pulse"><div class="bg-muted rounded-2xl p-8 space-y-4"><div class="flex items-start justify-between"><div class="w-16 h-16 bg-muted-foreground/20 rounded-full"></div><div class="w-8 h-6 bg-muted-foreground/20 rounded"></div></div><div class="space-y-2"><div class="h-4 bg-muted-foreground/20 rounded w-3/4"></div><div class="h-3 bg-muted-foreground/20 rounded w-full"></div><div class="h-3 bg-muted-foreground/20 rounded w-2/3"></div></div></div></div></div><div><div class="animate-pulse"><div class="bg-muted rounded-2xl p-8 space-y-4"><div class="flex items-start justify-between"><div class="w-16 h-16 bg-muted-foreground/20 rounded-full"></div><div class="w-8 h-6 bg-muted-foreground/20 rounded"></div></div><div class="space-y-2"><div class="h-4 bg-muted-foreground/20 rounded w-3/4"></div><div class="h-3 bg-muted-foreground/20 rounded w-full"></div><div class="h-3 bg-muted-foreground/20 rounded w-2/3"></div></div></div></div></div><div><div class="animate-pulse"><div class="bg-muted rounded-2xl p-8 space-y-4"><div class="flex items-start justify-between"><div class="w-16 h-16 bg-muted-foreground/20 rounded-full"></div><div class="w-8 h-6 bg-muted-foreground/20 rounded"></div></div><div class="space-y-2"><div class="h-4 bg-muted-foreground/20 rounded w-3/4"></div><div class="h-3 bg-muted-foreground/20 rounded w-full"></div><div class="h-3 bg-muted-foreground/20 rounded w-2/3"></div></div></div></div></div><div><div class="animate-pulse"><div class="bg-muted rounded-2xl p-8 space-y-4"><div class="flex items-start justify-between"><div class="w-16 h-16 bg-muted-foreground/20 rounded-full"></div><div class="w-8 h-6 bg-muted-foreground/20 rounded"></div></div><div class="space-y-2"><div class="h-4 bg-muted-foreground/20 rounded w-3/4"></div><div class="h-3 bg-muted-foreground/20 rounded w-full"></div><div class="h-3 bg-muted-foreground/20 rounded w-2/3"></div></div></div></div></div><div><div class="animate-pulse"><div class="bg-muted rounded-2xl p-8 space-y-4"><div class="flex items-start justify-between"><div class="w-16 h-16 bg-muted-foreground/20 rounded-full"></div><div class="w-8 h-6 bg-muted-foreground/20 rounded"></div></div><div class="space-y-2"><div class="h-4 bg-muted-foreground/20 rounded w-3/4"></div><div class="h-3 bg-muted-foreground/20 rounded w-full"></div><div class="h-3 bg-muted-foreground/20 rounded w-2/3"></div></div></div></div></div><div><div class="animate-pulse"><div class="bg-muted rounded-2xl p-8 space-y-4"><div class="flex items-start justify-between"><div class="w-16 h-16 bg-muted-foreground/20 rounded-full"></div><div class="w-8 h-6 bg-muted-foreground/20 rounded"></div></div><div class="space-y-2"><div class="h-4 bg-muted-foreground/20 rounded w-3/4"></div><div class="h-3 bg-muted-foreground/20 rounded w-full"></div><div class="h-3 bg-muted-foreground/20 rounded w-2/3"></div></div></div></div></div><div><div class="animate-pulse"><div class="bg-muted rounded-2xl p-8 space-y-4"><div class="flex items-start justify-between"><div class="w-16 h-16 bg-muted-foreground/20 rounded-full"></div><div class="w-8 h-6 bg-muted-foreground/20 rounded"></div></div><div class="space-y-2"><div class="h-4 bg-muted-foreground/20 rounded w-3/4"></div><div class="h-3 bg-muted-foreground/20 rounded w-full"></div><div class="h-3 bg-muted-foreground/20 rounded w-2/3"></div></div></div></div></div></div></div></section></article></main></main><footer class="mt-20 border-t bg-muted/30"><div class="container py-12"><div class="grid gap-8 md:grid-cols-4"><div class="md:col-span-2"><div class="flex items-center gap-2 mb-4"><span class="text-2xl">🎵</span><span class="font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent">TikTok Emoji</span></div><p class="text-sm text-muted-foreground mb-4">Your ultimate resource for TikTok hidden emojis, creative combinations, and emoji trends. Make your TikTok comments more expressive and engaging!</p></div><div><h3 class="font-semibold mb-4">Quick Links</h3><ul class="space-y-2 text-sm"><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/hidden-emojis">Hidden Emojis</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/popular-combos">Popular Combos</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/title-generator">Title Generator</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/search">Search Emojis</a></li></ul></div><div><h3 class="font-semibold mb-4">Categories</h3><ul class="space-y-2 text-sm"><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/smileys-emotion">Smileys &amp; Emotion</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/people-body">People &amp; Body</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/animals-nature">Animals &amp; Nature</a></li><li><a class="text-muted-foreground hover:text-foreground transition-colors" href="/category/food-drink">Food &amp; Drink</a></li></ul></div></div><div class="mt-8 pt-8 border-t text-center text-sm text-muted-foreground"><p>© 2024 TikTok Emoji. Not affiliated with TikTok or ByteDance.</p><p class="mt-2">Made with 💕 for the TikTok community</p></div></div></footer><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div><script src="/_next/static/chunks/webpack-1b400f1de436fe28.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/bc19b31da0bd50c9.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"3:I[2846,[],\"\"]\n5:I[2972,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"150\",\"static/chunks/150-dde69c0d40954ea8.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"931\",\"static/chunks/app/page-a448ec3e013efd3a.js\"],\"\"]\n6:I[4876,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"150\",\"static/chunks/150-dde69c0d40954ea8.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"931\",\"static/chunks/app/page-a448ec3e013efd3a.js\"],\"HiddenEmojisPreview\"]\n7:I[1003,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"150\",\"static/chunks/150-dde69c0d40954ea8.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"931\",\"static/chunks/app/page-a448ec3e013efd3a.js\"],\"TrendingContent\"]\n8:I[5596,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"150\",\"static/chunks/150-dde69c0d40954ea8.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"931\",\"static/chunks/app/page-a448ec3e013efd3a.js\"],\"LazyLoad\"]\n9:I[5596,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"150\",\"static/chunks/150-dde69c0d40954ea8.js\",\"672\",\"static/chunks/672-2b366b7389252487.js\",\"931\",\"static/chunks/app/page-a448ec3e013efd3a.js\"],\"CategoryCardSkeleton\"]\na:I[7105,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"ExtensionErrorHandler\"]\nb:I[8783,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"Navigation\"]\nc:I[4707,[],\"\"]\nd:I[6423,[],\"\"]\ne:I[9556,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunk"])</script><script>self.__next_f.push([1,"s/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"Toaster\"]\nf:I[8271,[\"438\",\"static/chunks/438-3d6b98e82ab59c7b.js\",\"52\",\"static/chunks/52-e89dc12467f890af.js\",\"367\",\"static/chunks/367-70ef8c60d69865f7.js\",\"781\",\"static/chunks/781-3944b942f49751e0.js\",\"435\",\"static/chunks/435-dc8290f2b1c966aa.js\",\"2\",\"static/chunks/2-bcbe5cd3b8eae62a.js\",\"185\",\"static/chunks/app/layout-0af87e3f0fffa538.js\"],\"PerformanceMonitor\"]\n11:I[1060,[],\"\"]\n12:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$L3\",null,{\"buildId\":\"w8mdcKjlUfOtXTnBxYEHi\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"\"],\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"__PAGE__\",{},[[\"$L4\",[\"$\",\"main\",null,{\"className\":\"overflow-x-hidden\",\"role\":\"main\",\"children\":[[\"$\",\"section\",null,{\"className\":\"relative overflow-hidden bg-gradient-to-br from-pink-50 via-purple-50/30 to-blue-50 py-24 md:py-32\",\"aria-label\":\"Hero section introducing TikTok emoji tools\",\"children\":[[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 overflow-hidden\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute top-[10%] left-[15%] w-64 h-64 bg-gradient-to-br from-tiktok-pink/20 to-purple-400/20 rounded-full blur-3xl animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"absolute bottom-[10%] right-[15%] w-64 h-64 bg-gradient-to-br from-tiktok-blue/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse animation-delay-2000\"}]]}],[\"$\",\"div\",null,{\"className\":\"absolute inset-0 overflow-hidden pointer-events-none\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute top-[15%] left-[10%] text-6xl opacity-20 animate-float\",\"children\":\"🎵\"}],[\"$\",\"div\",null,{\"className\":\"absolute top-[20%] right-[15%] text-5xl opacity-25 animate-float animation-delay-1000\",\"children\":\"✨\"}],[\"$\",\"div\",null,{\"className\":\"absolute bottom-[25%] left-[20%] text-5xl opacity-20 animate-float animation-delay-2000\",\"children\":\"💕\"}],[\"$\",\"div\",null,{\"className\":\"absolute bottom-[20%] right-[10%] text-6xl opacity-25 animate-float animation-delay-1500\",\"children\":\"🔥\"}],[\"$\",\"div\",null,{\"className\":\"absolute top-[50%] left-[50%] -translate-x-1/2 -translate-y-1/2 text-5xl opacity-20 animate-float animation-delay-2500\",\"children\":\"😍\"}]]}]],[\"$\",\"div\",null,{\"className\":\"container relative z-10\",\"children\":[\"$\",\"header\",null,{\"className\":\"text-center max-w-4xl mx-auto\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-4xl md:text-6xl lg:text-7xl font-bold mb-8 bg-gradient-to-r from-tiktok-pink via-purple-500 to-tiktok-blue bg-clip-text text-transparent leading-tight\",\"children\":\"Unlock TikTok's Secret Emojis\"}],[\"$\",\"p\",null,{\"className\":\"text-lg md:text-xl text-muted-foreground mb-16 max-w-2xl mx-auto leading-relaxed\",\"children\":\"46 exclusive emoji codes • One-click copy • Used by millions daily • Only work in TikTok app\"}],[\"$\",\"nav\",null,{\"aria-label\":\"Main navigation to emoji tools\",\"className\":\"flex flex-col sm:flex-row gap-4 justify-center items-center\",\"children\":[[\"$\",\"$L5\",null,{\"href\":\"/hidden-emojis\",\"aria-describedby\":\"hidden-emojis-desc\",\"children\":[\"$\",\"button\",null,{\"className\":\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-tiktok-pink to-pink-500 text-white hover:opacity-90 h-11 rounded-md px-8 gap-2 min-w-[200px] shadow-lg hover:shadow-xl transition-all hover:scale-105\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-xl\",\"role\":\"img\",\"aria-label\":\"Target emoji\",\"children\":\"🎯\"}],\"Hidden Emojis\"]}]}],[\"$\",\"span\",null,{\"id\":\"hidden-emojis-desc\",\"className\":\"sr-only\",\"children\":\"Explore TikTok's secret emoji codes\"}],[\"$\",\"$L5\",null,{\"href\":\"/popular-combos\",\"aria-describedby\":\"popular-combos-desc\",\"children\":[\"$\",\"button\",null,{\"className\":\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:text-accent-foreground h-11 rounded-md px-8 gap-2 min-w-[200px] hover:bg-muted/50 transition-all hover:scale-105\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-xl\",\"role\":\"img\",\"aria-label\":\"Fire emoji\",\"children\":\"🔥\"}],\"Popular Combos\"]}]}],[\"$\",\"span\",null,{\"id\":\"popular-combos-desc\",\"className\":\"sr-only\",\"children\":\"Browse trending emoji combinations\"}],[\"$\",\"$L5\",null,{\"href\":\"/title-generator\",\"aria-describedby\":\"title-generator-desc\",\"children\":[\"$\",\"button\",null,{\"className\":\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:text-accent-foreground h-11 rounded-md px-8 gap-2 min-w-[200px] hover:bg-muted/50 transition-all hover:scale-105\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-xl\",\"role\":\"img\",\"aria-label\":\"Sparkles emoji\",\"children\":\"✨\"}],\"Title Generator\"]}]}],[\"$\",\"span\",null,{\"id\":\"title-generator-desc\",\"className\":\"sr-only\",\"children\":\"Generate creative TikTok titles with emojis\"}]]}]]}]}]]}],[\"$\",\"article\",null,{\"aria-labelledby\":\"hidden-emojis-heading\",\"children\":[\"$\",\"$L6\",null,{}]}],[\"$\",\"article\",null,{\"aria-labelledby\":\"how-to-use-heading\",\"children\":[\"$\",\"section\",null,{\"className\":\"py-12 md:py-16 bg-gradient-to-b from-muted/40 via-muted/60 to-muted/40\",\"aria-labelledby\":\"how-to-use-heading\",\"children\":[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[[\"$\",\"header\",null,{\"className\":\"text-center mb-8 md:mb-12\",\"children\":[[\"$\",\"h2\",null,{\"id\":\"how-to-use-heading\",\"className\":\"text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-tiktok-pink via-purple-500 to-tiktok-blue bg-clip-text text-transparent\",\"children\":\"How to Use Hidden Emoji Codes\"}],[\"$\",\"p\",null,{\"className\":\"text-lg text-muted-foreground max-w-xl mx-auto leading-relaxed\",\"children\":\"Transform your TikTok comments in 3 simple steps and stand out from the crowd\"}]]}],[\"$\",\"ol\",null,{\"className\":\"grid md:grid-cols-3 gap-8 max-w-5xl mx-auto\",\"role\":\"list\",\"children\":[[\"$\",\"li\",null,{\"className\":\"relative group\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 bg-gradient-to-br from-tiktok-pink/20 to-purple-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500\",\"aria-hidden\":\"true\"}],[\"$\",\"div\",null,{\"className\":\"relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-20 h-20 bg-gradient-to-br from-tiktok-pink to-purple-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300\",\"aria-label\":\"Step 1\",\"children\":\"1\"}],[\"$\",\"h3\",null,{\"className\":\"text-xl font-semibold mb-4 text-center\",\"children\":\"Copy the Code\"}],[\"$\",\"p\",null,{\"className\":\"text-muted-foreground text-center leading-relaxed\",\"children\":\"Click the copy button next to any emoji code to copy it to your clipboard instantly\"}]]}]]}],[\"$\",\"li\",null,{\"className\":\"relative group\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 bg-gradient-to-br from-tiktok-blue/20 to-cyan-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500\",\"aria-hidden\":\"true\"}],[\"$\",\"div\",null,{\"className\":\"relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-20 h-20 bg-gradient-to-br from-tiktok-blue to-cyan-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300\",\"aria-label\":\"Step 2\",\"children\":\"2\"}],[\"$\",\"h3\",null,{\"className\":\"text-xl font-semibold mb-4 text-center\",\"children\":\"Paste in TikTok\"}],[\"$\",\"p\",null,{\"className\":\"text-muted-foreground text-center leading-relaxed\",\"children\":\"Open TikTok and paste the code in any comment or caption field\"}]]}]]}],[\"$\",\"li\",null,{\"className\":\"relative group\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500\",\"aria-hidden\":\"true\"}],[\"$\",\"div\",null,{\"className\":\"relative bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-500 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300\",\"aria-label\":\"Step 3\",\"children\":\"3\"}],[\"$\",\"h3\",null,{\"className\":\"text-xl font-semibold mb-4 text-center\",\"children\":\"Watch the Magic\"}],[\"$\",\"p\",null,{\"className\":\"text-muted-foreground text-center leading-relaxed\",\"children\":\"The code automatically transforms into the colorful TikTok emoji\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 md:mt-12 text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200/50 rounded-2xl p-6 max-w-3xl mx-auto\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground leading-relaxed\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"className\":\"text-yellow-700\",\"children\":\"Pro Tip:\"}],\" These emojis only work within the TikTok app. Outside TikTok, they'll appear as regular text codes.\"]}]}]}]]}]}]}],[\"$\",\"article\",null,{\"aria-labelledby\":\"trending-content-heading\",\"children\":[\"$\",\"$L7\",null,{}]}],[\"$\",\"article\",null,{\"aria-labelledby\":\"categories-heading\",\"children\":[\"$\",\"section\",null,{\"className\":\"py-12 md:py-16 bg-gradient-to-b from-background to-muted/30\",\"aria-labelledby\":\"categories-heading\",\"children\":[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[[\"$\",\"header\",null,{\"className\":\"text-center mb-8 md:mb-12\",\"children\":[[\"$\",\"h2\",null,{\"id\":\"categories-heading\",\"className\":\"text-3xl md:text-4xl font-bold mb-6 flex items-center justify-center gap-3\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-grid3x3 w-8 h-8 text-tiktok-pink\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"rect\",\"afitv7\",{\"width\":\"18\",\"height\":\"18\",\"x\":\"3\",\"y\":\"3\",\"rx\":\"2\"}],[\"$\",\"path\",\"1pudct\",{\"d\":\"M3 9h18\"}],[\"$\",\"path\",\"5xshup\",{\"d\":\"M3 15h18\"}],[\"$\",\"path\",\"fh3hqa\",{\"d\":\"M9 3v18\"}],[\"$\",\"path\",\"14nvp0\",{\"d\":\"M15 3v18\"}],\"$undefined\"]}],\"Browse by Category\"]}],[\"$\",\"p\",null,{\"className\":\"text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed\",\"children\":\"Explore over 1,300 Unicode emojis organized by category. Find the perfect emoji for every mood and occasion.\"}]]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\"role\":\"grid\",\"aria-label\":\"Emoji categories\",\"children\":[[\"$\",\"$L8\",\"smileys-emotion\",{\"fallback\":[\"$\",\"$L9\",null,{}],\"rootMargin\":\"100px\",\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/smileys-emotion\",\"aria-label\":\"Browse Smileys \u0026 Emotion emojis - 144 available\",\"children\":[\"$\",\"div\",null,{\"className\":\"rounded-lg bg-card text-card-foreground shadow-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up\",\"style\":{\"animationDelay\":\"0ms\"},\"role\":\"gridcell\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-6 pt-8 pb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12\",\"role\":\"img\",\"aria-label\":\"Smileys \u0026 Emotion category icon\",\"children\":\"😊\"}],[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors\",\"aria-label\":\"144 emojis in this category\",\"children\":144}]]}],[\"$\",\"h3\",null,{\"className\":\"font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors\",\"children\":\"Smileys \u0026 Emotion\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\",\"children\":\"Express your emotions with faces, hearts, and feelings\"}]]}]}]}]}],[\"$\",\"$L8\",\"people-body\",{\"fallback\":[\"$\",\"$L9\",null,{}],\"rootMargin\":\"100px\",\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/people-body\",\"aria-label\":\"Browse People \u0026 Body emojis - 44 available\",\"children\":[\"$\",\"div\",null,{\"className\":\"rounded-lg bg-card text-card-foreground shadow-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up\",\"style\":{\"animationDelay\":\"100ms\"},\"role\":\"gridcell\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-6 pt-8 pb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12\",\"role\":\"img\",\"aria-label\":\"People \u0026 Body category icon\",\"children\":\"👋\"}],[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors\",\"aria-label\":\"44 emojis in this category\",\"children\":44}]]}],[\"$\",\"h3\",null,{\"className\":\"font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors\",\"children\":\"People \u0026 Body\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\",\"children\":\"Hand gestures, body parts, and people\"}]]}]}]}]}],[\"$\",\"$L8\",\"animals-nature\",{\"fallback\":[\"$\",\"$L9\",null,{}],\"rootMargin\":\"100px\",\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/animals-nature\",\"aria-label\":\"Browse Animals \u0026 Nature emojis - 127 available\",\"children\":[\"$\",\"div\",null,{\"className\":\"rounded-lg bg-card text-card-foreground shadow-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up\",\"style\":{\"animationDelay\":\"200ms\"},\"role\":\"gridcell\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-6 pt-8 pb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12\",\"role\":\"img\",\"aria-label\":\"Animals \u0026 Nature category icon\",\"children\":\"🐱\"}],[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors\",\"aria-label\":\"127 emojis in this category\",\"children\":127}]]}],[\"$\",\"h3\",null,{\"className\":\"font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors\",\"children\":\"Animals \u0026 Nature\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\",\"children\":\"Animals, plants, and nature\"}]]}]}]}]}],[\"$\",\"$L8\",\"food-drink\",{\"fallback\":[\"$\",\"$L9\",null,{}],\"rootMargin\":\"100px\",\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/food-drink\",\"aria-label\":\"Browse Food \u0026 Drink emojis - 95 available\",\"children\":[\"$\",\"div\",null,{\"className\":\"rounded-lg bg-card text-card-foreground shadow-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up\",\"style\":{\"animationDelay\":\"300ms\"},\"role\":\"gridcell\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-6 pt-8 pb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12\",\"role\":\"img\",\"aria-label\":\"Food \u0026 Drink category icon\",\"children\":\"🍔\"}],[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors\",\"aria-label\":\"95 emojis in this category\",\"children\":95}]]}],[\"$\",\"h3\",null,{\"className\":\"font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors\",\"children\":\"Food \u0026 Drink\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\",\"children\":\"Delicious food and refreshing drinks\"}]]}]}]}]}],[\"$\",\"$L8\",\"travel-places\",{\"fallback\":[\"$\",\"$L9\",null,{}],\"rootMargin\":\"100px\",\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/travel-places\",\"aria-label\":\"Browse Travel \u0026 Places emojis - 106 available\",\"children\":[\"$\",\"div\",null,{\"className\":\"rounded-lg bg-card text-card-foreground shadow-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up\",\"style\":{\"animationDelay\":\"400ms\"},\"role\":\"gridcell\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-6 pt-8 pb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12\",\"role\":\"img\",\"aria-label\":\"Travel \u0026 Places category icon\",\"children\":\"✈️\"}],[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors\",\"aria-label\":\"106 emojis in this category\",\"children\":106}]]}],[\"$\",\"h3\",null,{\"className\":\"font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors\",\"children\":\"Travel \u0026 Places\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\",\"children\":\"Transportation, landmarks, and places\"}]]}]}]}]}],[\"$\",\"$L8\",\"activities\",{\"fallback\":[\"$\",\"$L9\",null,{}],\"rootMargin\":\"100px\",\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/activities\",\"aria-label\":\"Browse Activities emojis - 50 available\",\"children\":[\"$\",\"div\",null,{\"className\":\"rounded-lg bg-card text-card-foreground shadow-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up\",\"style\":{\"animationDelay\":\"500ms\"},\"role\":\"gridcell\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-6 pt-8 pb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12\",\"role\":\"img\",\"aria-label\":\"Activities category icon\",\"children\":\"⚽\"}],[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors\",\"aria-label\":\"50 emojis in this category\",\"children\":50}]]}],[\"$\",\"h3\",null,{\"className\":\"font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors\",\"children\":\"Activities\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\",\"children\":\"Sports, games, and activities\"}]]}]}]}]}],[\"$\",\"$L8\",\"objects\",{\"fallback\":[\"$\",\"$L9\",null,{}],\"rootMargin\":\"100px\",\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/objects\",\"aria-label\":\"Browse Objects emojis - 145 available\",\"children\":[\"$\",\"div\",null,{\"className\":\"rounded-lg bg-card text-card-foreground shadow-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up\",\"style\":{\"animationDelay\":\"600ms\"},\"role\":\"gridcell\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-6 pt-8 pb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12\",\"role\":\"img\",\"aria-label\":\"Objects category icon\",\"children\":\"💎\"}],[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors\",\"aria-label\":\"145 emojis in this category\",\"children\":145}]]}],[\"$\",\"h3\",null,{\"className\":\"font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors\",\"children\":\"Objects\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\",\"children\":\"Everyday objects and tools\"}]]}]}]}]}],[\"$\",\"$L8\",\"symbols\",{\"fallback\":[\"$\",\"$L9\",null,{}],\"rootMargin\":\"100px\",\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/symbols\",\"aria-label\":\"Browse Symbols emojis - 672 available\",\"children\":[\"$\",\"div\",null,{\"className\":\"rounded-lg bg-card text-card-foreground shadow-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group h-full border-0 bg-gradient-to-br from-background to-muted/20 backdrop-blur-sm animate-fade-in-up\",\"style\":{\"animationDelay\":\"700ms\"},\"role\":\"gridcell\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-6 pt-8 pb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl group-hover:scale-125 transition-all duration-300 group-hover:rotate-12\",\"role\":\"img\",\"aria-label\":\"Symbols category icon\",\"children\":\"❤️\"}],[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold text-tiktok-pink/70 group-hover:text-tiktok-pink transition-colors\",\"aria-label\":\"672 emojis in this category\",\"children\":672}]]}],[\"$\",\"h3\",null,{\"className\":\"font-semibold text-lg mb-3 group-hover:text-tiktok-blue transition-colors\",\"children\":\"Symbols\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\",\"children\":\"Hearts, arrows, and various symbols\"}]]}]}]}]}]]}]]}]}]}]]}],null],null],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/bc19b31da0bd50c9.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"WebSite\\\",\\\"name\\\":\\\"TikTok Emoji\\\",\\\"alternateName\\\":\\\"TikTok Hidden Emoji Codes\\\",\\\"url\\\":\\\"https://tiktokemoji.com\\\",\\\"description\\\":\\\"Discover TikTok hidden emoji codes, popular combinations, and special meanings\\\",\\\"potentialAction\\\":{\\\"@type\\\":\\\"SearchAction\\\",\\\"target\\\":{\\\"@type\\\":\\\"EntryPoint\\\",\\\"urlTemplate\\\":\\\"https://tiktokemoji.com/search?q={search_term_string}\\\"},\\\"query-input\\\":\\\"required name=search_term_string\\\"},\\\"publisher\\\":{\\\"@type\\\":\\\"Organization\\\",\\\"name\\\":\\\"TikTok Emoji\\\",\\\"url\\\":\\\"https://tiktokemoji.com\\\",\\\"logo\\\":{\\\"@type\\\":\\\"ImageObject\\\",\\\"url\\\":\\\"https://tiktokemoji.com/logo.png\\\",\\\"width\\\":600,\\\"height\\\":60}},\\\"sameAs\\\":[]}\"}}]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[[\"$\",\"$La\",null,{}],[\"$\",\"$Lb\",null,{}],[\"$\",\"main\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}]}],[\"$\",\"footer\",null,{\"className\":\"mt-20 border-t bg-muted/30\",\"children\":[\"$\",\"div\",null,{\"className\":\"container py-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grid gap-8 md:grid-cols-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"md:col-span-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2 mb-4\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-2xl\",\"children\":\"🎵\"}],[\"$\",\"span\",null,{\"className\":\"font-bold text-xl bg-gradient-to-r from-tiktok-pink to-tiktok-blue bg-clip-text text-transparent\",\"children\":\"TikTok Emoji\"}]]}],[\"$\",\"p\",null,{\"className\":\"text-sm text-muted-foreground mb-4\",\"children\":\"Your ultimate resource for TikTok hidden emojis, creative combinations, and emoji trends. Make your TikTok comments more expressive and engaging!\"}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"font-semibold mb-4\",\"children\":\"Quick Links\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-sm\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$L5\",null,{\"href\":\"/hidden-emojis\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Hidden Emojis\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L5\",null,{\"href\":\"/popular-combos\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Popular Combos\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L5\",null,{\"href\":\"/title-generator\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Title Generator\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L5\",null,{\"href\":\"/search\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Search Emojis\"}]}]]}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"font-semibold mb-4\",\"children\":\"Categories\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-sm\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/smileys-emotion\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Smileys \u0026 Emotion\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/people-body\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"People \u0026 Body\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/animals-nature\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Animals \u0026 Nature\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L5\",null,{\"href\":\"/category/food-drink\",\"className\":\"text-muted-foreground hover:text-foreground transition-colors\",\"children\":\"Food \u0026 Drink\"}]}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 pt-8 border-t text-center text-sm text-muted-foreground\",\"children\":[[\"$\",\"p\",null,{\"children\":\"© 2024 TikTok Emoji. Not affiliated with TikTok or ByteDance.\"}],[\"$\",\"p\",null,{\"className\":\"mt-2\",\"children\":\"Made with 💕 for the TikTok community\"}]]}]]}]}],[\"$\",\"$Le\",null,{}],[\"$\",\"$Lf\",null,{}]]}]]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$L10\"],\"globalErrorComponent\":\"$11\",\"missingSlots\":\"$W12\"}]\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"TikTok Emoji - Hidden Codes \u0026 Popular Combinations\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings. Copy with one click to make your TikTok comments more engaging!\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"TikTok Emoji Team\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"TikTok emoji,hidden emoji codes,TikTok symbols,emoji combinations,TikTok comments,secret emojis\"}],[\"$\",\"meta\",\"6\",{\"name\":\"creator\",\"content\":\"TikTok Emoji\"}],[\"$\",\"meta\",\"7\",{\"name\":\"publisher\",\"content\":\"TikTok Emoji\"}],[\"$\",\"meta\",\"8\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"9\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"10\",{\"name\":\"google-site-verification\",\"content\":\"google-site-verification-code\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:title\",\"content\":\"TikTok Emoji - Hidden Codes \u0026 Popular Combinations\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:description\",\"content\":\"Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings.\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:url\",\"content\":\"https://tiktokemoji.com\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:site_name\",\"content\":\"TikTok Emoji\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image\",\"content\":\"https://tiktokemoji.com/og-image.png\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:image:alt\",\"content\":\"TikTok Emoji - Hidden Codes \u0026 Popular Combinations\"}],[\"$\",\"meta\",\"20\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:title\",\"content\":\"TikTok Emoji - Hidden Codes \u0026 Popular Combinations\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:description\",\"content\":\"Discover 46+ TikTok hidden emoji codes and popular combinations\"}],[\"$\",\"meta\",\"24\",{\"name\":\"twitter:image\",\"content\":\"https://tiktokemoji.com/og-image.png\"}],[\"$\",\"meta\",\"25\",{\"name\":\"next-size-adjust\"}]]\n"])</script><script>self.__next_f.push([1,"4:null\n"])</script></body></html>