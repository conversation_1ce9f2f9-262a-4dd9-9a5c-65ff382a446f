"use strict";(()=>{var e={};e.id=703,e.ids=[703],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7196:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>x,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>c});var r={};o.r(r),o.d(r,{GET:()=>l});var a=o(9303),s=o(8716),n=o(3131),i=o(5661),u=o(707);async function l(){let e=await function(){let e="https://tiktokemoji.com";return{rules:[{userAgent:"*",allow:"/",disallow:["/api/","/scripts/","/*.json$","/ui/"]},{userAgent:"Googlebot",allow:"/",crawlDelay:0},{userAgent:"Bingbot",allow:"/",crawlDelay:1}],sitemap:`${e}/sitemap.xml`,host:e}}(),t=(0,u.resolveRouteData)(e,"robots");return new i.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let p=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Frobots.txt%2Froute&filePath=%2FUsers%2Fjiang%2Fdevelop%2FGitHub%2Ftiktokemoji%2Fapp%2Frobots.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:c,serverHooks:m}=p,x="/robots.txt/route";function g(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:c})}}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[948,346],()=>o(7196));module.exports=r})();