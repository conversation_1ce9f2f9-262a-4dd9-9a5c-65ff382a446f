(()=>{var e={};e.id=27,e.ids=[27],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3368:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(8686),t(6537),t(5866);var n=t(3191),a=t(8716),r=t(7922),i=t.n(r),l=t(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["popular-combos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8686)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/popular-combos/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6537)),"/Users/<USER>/develop/GitHub/tiktokemoji/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/develop/GitHub/tiktokemoji/app/popular-combos/page.tsx"],u="/popular-combos/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/popular-combos/page",pathname:"/popular-combos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8356:(e,s,t)=>{Promise.resolve().then(t.bind(t,9164))},9164:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var n=t(326),a=t(7577),r=t(434),i=t(6333),l=t(7069),o=t(3810),d=t(772),c=t(2643),u=t(3427),m=t(567),x=t(4008),p=t(2056);let h={all:"All",funny:"Funny",emotional:"Emotional",reaction:"Reaction",trending:"Trending"};function j(){let[e,s]=(0,a.useState)("all"),{toast:t}=(0,x.pm)(),j=p.E$.filter(s=>{if("all"===e)return!0;let t=`${s.meaning} ${s.usage}`.toLowerCase();switch(e){case"funny":return t.includes("laugh")||t.includes("funny")||t.includes("dead");case"emotional":return t.includes("love")||t.includes("moved")||t.includes("cry");case"reaction":return t.includes("shock")||t.includes("watch")||t.includes("surprise");case"trending":return t.includes("trending")||t.includes("popular")||t.includes("viral");default:return!0}}),g=e=>{navigator.clipboard.writeText(e),t({title:"Copied!",description:"Emoji combination copied to clipboard"})},f=p.E$.slice(0,6);return(0,n.jsxs)("div",{className:"container py-8",children:[(0,n.jsxs)(r.default,{href:"/",className:"inline-flex items-center text-muted-foreground hover:text-foreground mb-6",children:[n.jsx(i.Z,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,n.jsxs)("div",{className:"mb-8",children:[n.jsx("h1",{className:"text-4xl font-bold mb-4 flex items-center gap-3",children:"\uD83D\uDD25 Popular Emoji Combinations"}),n.jsx("p",{className:"text-muted-foreground text-lg",children:"Explore the most popular emoji combinations on TikTok to make your comments more engaging!"})]}),(0,n.jsxs)(c.Zb,{className:"mb-8 border-tiktok-pink/20 bg-gradient-to-br from-pink-50/50 to-blue-50/50",children:[(0,n.jsxs)(c.Ol,{children:[(0,n.jsxs)(c.ll,{className:"flex items-center gap-2",children:[n.jsx(l.Z,{className:"w-5 h-5 text-tiktok-pink"}),"Today's Viral Combos"]}),n.jsx(c.SZ,{children:"Combinations with surging usage in the last 24 hours"})]}),n.jsx(c.aY,{children:n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:f.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:e.emojis}),(0,n.jsxs)("div",{children:[n.jsx("p",{className:"font-medium text-sm",children:e.meaning}),n.jsx("p",{className:"text-xs text-muted-foreground",children:e.usage})]})]}),n.jsx(d.z,{size:"sm",variant:"ghost",onClick:()=>g(e.emojis),children:n.jsx(o.Z,{className:"w-3 h-3"})})]},e.id))})})]}),(0,n.jsxs)(u.mQ,{value:e,onValueChange:e=>s(e),children:[n.jsx(u.dr,{className:"grid w-full max-w-md grid-cols-5",children:Object.entries(h).map(([e,s])=>n.jsx(u.SP,{value:e,children:s},e))}),n.jsx(u.nU,{value:e,className:"mt-6",children:n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:j.map(e=>n.jsx(c.Zb,{className:"hover:shadow-lg transition-shadow",children:n.jsx(c.aY,{className:"pt-6",children:(0,n.jsxs)("div",{className:"flex items-start justify-between gap-4",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[n.jsx("span",{className:"text-3xl",children:e.emojis}),(0,n.jsxs)(m.C,{variant:"secondary",className:"text-xs",children:[e.emojis.length," emojis"]})]}),n.jsx("h3",{className:"font-semibold text-lg mb-1",children:e.meaning}),n.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:e.usage}),(0,n.jsxs)("div",{className:"bg-muted rounded-md p-3 text-sm",children:[n.jsx("p",{className:"text-muted-foreground mb-1",children:"Example:"}),n.jsx("p",{className:"italic",children:e.usage.includes("POV")?`POV: You see this video ${e.emojis}`:e.usage.includes("comment")?`This is so funny ${e.emojis}`:`${e.emojis} This is amazing!`})]})]}),(0,n.jsxs)(d.z,{size:"sm",variant:"outline",onClick:()=>g(e.emojis),children:[n.jsx(o.Z,{className:"w-3 h-3 mr-2"}),"Copy"]})]})})},e.id))})})]}),(0,n.jsxs)("div",{className:"mt-12 space-y-6",children:[n.jsx("h2",{className:"text-2xl font-bold",children:"\uD83D\uDCA1 Creative Combination Tips"}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)(c.Zb,{children:[n.jsx(c.Ol,{children:n.jsx(c.ll,{className:"text-lg",children:"Repetition for Emphasis"})}),(0,n.jsxs)(c.aY,{children:[n.jsx("p",{className:"text-muted-foreground mb-3",children:"Repeat the same emoji to strengthen emotional expression"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDE2D\uD83D\uDE2D\uD83D\uDE2D"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Super moved"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDD25\uD83D\uDD25\uD83D\uDD25"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"So hot"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDC80\uD83D\uDC80\uD83D\uDC80"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"I'm dead"})]})]})]})]}),(0,n.jsxs)(c.Zb,{children:[n.jsx(c.Ol,{children:n.jsx(c.ll,{className:"text-lg",children:"Progressive Expression"})}),(0,n.jsxs)(c.aY,{children:[n.jsx("p",{className:"text-muted-foreground mb-3",children:"Use emojis of different intensities to show emotional progression"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDE42\uD83D\uDE0A\uD83D\uDE04"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Getting happier"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDE22\uD83D\uDE2D\uD83D\uDC94"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Gradually heartbroken"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDE10\uD83D\uDE11\uD83D\uDE34"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Getting sleepier"})]})]})]})]}),(0,n.jsxs)(c.Zb,{children:[n.jsx(c.Ol,{children:n.jsx(c.ll,{className:"text-lg",children:"Storytelling Method"})}),(0,n.jsxs)(c.aY,{children:[n.jsx("p",{className:"text-muted-foreground mb-3",children:"Use emoji combinations to tell a short story"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDC40➡️\uD83D\uDE31"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Shocked after seeing"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83C\uDFC3‍♂️\uD83D\uDCA8\uD83C\uDFE0"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Rush home"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDCF1\uD83D\uDCAC❤️"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Send love message"})]})]})]})]}),(0,n.jsxs)(c.Zb,{children:[n.jsx(c.Ol,{children:n.jsx(c.ll,{className:"text-lg",children:"Contrast Method"})}),(0,n.jsxs)(c.aY,{children:[n.jsx("p",{className:"text-muted-foreground mb-3",children:"Use contrasting emojis to create humorous effects"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDE07\uD83D\uDE08"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Surface vs Inner"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83E\uDD13➡️\uD83E\uDD73"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Work vs Off work"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[n.jsx("span",{className:"text-xl",children:"\uD83D\uDCAA\uD83D\uDE2D"}),n.jsx("span",{className:"text-sm text-muted-foreground",children:"Strong yet fragile"})]})]})]})]})]})]}),(0,n.jsxs)(c.Zb,{className:"mt-8 bg-muted",children:[n.jsx(c.Ol,{children:n.jsx(c.ll,{children:"\uD83C\uDFAF Usage Suggestions"})}),n.jsx(c.aY,{children:(0,n.jsxs)("ul",{className:"space-y-2 text-muted-foreground",children:[n.jsx("li",{children:"• Choose appropriate emoji combinations based on video content to enhance expression"}),n.jsx("li",{children:"• Avoid overuse - combinations of 2-5 emojis work best"}),n.jsx("li",{children:"• Combine with text to make comments more vivid and interesting"}),n.jsx("li",{children:"• Follow the latest trends and update your emoji library regularly"}),n.jsx("li",{children:"• Create your own unique combinations to develop a personal style"})]})})]})]})}},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>l});var n=t(326);t(7577);var a=t(9360),r=t(7863);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...t}){return n.jsx("div",{className:(0,r.cn)(i({variant:s}),e),...t})}},3427:(e,s,t)=>{"use strict";t.d(s,{mQ:()=>Z,nU:()=>_,dr:()=>A,SP:()=>T});var n=t(326),a=t(7577),r=t(2561),i=t(3095),l=t(5594),o=t(9815),d=t(5226),c=t(7124),u=t(2067),m=t(8957),x="Tabs",[p,h]=(0,i.b)(x,[l.Pc]),j=(0,l.Pc)(),[g,f]=p(x),b=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:o,activationMode:p="automatic",...h}=e,j=(0,c.gm)(o),[f,b]=(0,u.T)({prop:a,onChange:r,defaultProp:i??"",caller:x});return(0,n.jsx)(g,{scope:t,baseId:(0,m.M)(),value:f,onValueChange:b,orientation:l,dir:j,activationMode:p,children:(0,n.jsx)(d.WV.div,{dir:j,"data-orientation":l,...h,ref:s})})});b.displayName=x;var v="TabsList",D=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...r}=e,i=f(v,t),o=j(t);return(0,n.jsx)(l.fC,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:a,children:(0,n.jsx)(d.WV.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:s})})});D.displayName=v;var N="TabsTrigger",y=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:i=!1,...o}=e,c=f(N,t),u=j(t),m=k(c.baseId,a),x=E(c.baseId,a),p=a===c.value;return(0,n.jsx)(l.ck,{asChild:!0,...u,focusable:!i,active:p,children:(0,n.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...o,ref:s,onMouseDown:(0,r.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||i||!e||c.onValueChange(a)})})})});y.displayName=N;var w="TabsContent",C=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,forceMount:i,children:l,...c}=e,u=f(w,t),m=k(u.baseId,r),x=E(u.baseId,r),p=r===u.value,h=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,n.jsx)(o.z,{present:i||p,children:({present:t})=>(0,n.jsx)(d.WV.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:x,tabIndex:0,...c,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:t&&l})})});function k(e,s){return`${e}-trigger-${s}`}function E(e,s){return`${e}-content-${s}`}C.displayName=w;var P=t(7863);let Z=b,A=a.forwardRef(({className:e,...s},t)=>n.jsx(D,{ref:t,className:(0,P.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));A.displayName=D.displayName;let T=a.forwardRef(({className:e,...s},t)=>n.jsx(y,{ref:t,className:(0,P.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));T.displayName=y.displayName;let _=a.forwardRef(({className:e,...s},t)=>n.jsx(C,{ref:t,className:(0,P.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));_.displayName=C.displayName},6333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});let n=(0,t(6557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});let n=(0,t(6557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},7069:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});let n=(0,t(6557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},8686:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});let n=(0,t(8570).createProxy)(String.raw`/Users/<USER>/develop/GitHub/tiktokemoji/app/popular-combos/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),n=s.X(0,[948,28,87,917],()=>t(3368));module.exports=n})();