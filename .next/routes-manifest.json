{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/category/[category]", "regex": "^/category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcategory": "nxtPcategory"}, "namedRegex": "^/category/(?<nxtPcategory>[^/]+?)(?:/)?$"}, {"page": "/emoji/[emoji]", "regex": "^/emoji/([^/]+?)(?:/)?$", "routeKeys": {"nxtPemoji": "nxtPemoji"}, "namedRegex": "^/emoji/(?<nxtPemoji>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/hidden-emojis", "regex": "^/hidden\\-emojis(?:/)?$", "routeKeys": {}, "namedRegex": "^/hidden\\-emojis(?:/)?$"}, {"page": "/popular-combos", "regex": "^/popular\\-combos(?:/)?$", "routeKeys": {}, "namedRegex": "^/popular\\-combos(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/title-generator", "regex": "^/title\\-generator(?:/)?$", "routeKeys": {}, "namedRegex": "^/title\\-generator(?:/)?$"}, {"page": "/unicode-emoji-library", "regex": "^/unicode\\-emoji\\-library(?:/)?$", "routeKeys": {}, "namedRegex": "^/unicode\\-emoji\\-library(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}