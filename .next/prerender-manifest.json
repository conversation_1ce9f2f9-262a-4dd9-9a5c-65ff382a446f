{"version": 4, "routes": {"/robots.txt": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "text/plain", "x-next-cache-tags": "_N_T_/layout,_N_T_/robots.txt/layout,_N_T_/robots.txt/route,_N_T_/robots.txt"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/robots.txt", "dataRoute": null}, "/sitemap.xml": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "application/xml", "x-next-cache-tags": "_N_T_/layout,_N_T_/sitemap.xml/layout,_N_T_/sitemap.xml/route,_N_T_/sitemap.xml"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/sitemap.xml", "dataRoute": null}, "/search": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/search", "dataRoute": "/search.rsc"}, "/emoji/%E2%9C%85": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%E2%9C%85.rsc"}, "/emoji/%E2%9C%A8": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%E2%9C%A8.rsc"}, "/emoji/%E2%9D%A4%EF%B8%8F": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%E2%9D%A4%EF%B8%8F.rsc"}, "/emoji/%E2%9D%A4%EF%B8%8F%E2%80%8D%F0%9F%A9%B9": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%E2%9D%A4%EF%B8%8F%E2%80%8D%F0%9F%A9%B9.rsc"}, "/emoji/%E2%AD%90%EF%B8%8F": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%E2%AD%90%EF%B8%8F.rsc"}, "/emoji/%F0%9F%91%80": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%91%80.rsc"}, "/emoji/%F0%9F%92%80": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%92%80.rsc"}, "/emoji/%F0%9F%94%A5": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%94%A5.rsc"}, "/emoji/%F0%9F%97%BF": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%97%BF.rsc"}, "/emoji/%F0%9F%98%82": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%98%82.rsc"}, "/emoji/%F0%9F%98%AD": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%98%AD.rsc"}, "/emoji/%F0%9F%99%8F%F0%9F%8F%BB": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%99%8F%F0%9F%8F%BB.rsc"}, "/emoji/%F0%9F%A4%8C%F0%9F%8F%BB": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%A4%8C%F0%9F%8F%BB.rsc"}, "/emoji/%F0%9F%A5%BA": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%A5%BA.rsc"}, "/emoji/%F0%9F%A7%BF": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%A7%BF.rsc"}, "/emoji/%F0%9F%A9%B7": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%A9%B7.rsc"}, "/emoji/%F0%9F%AB%A0": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%AB%A0.rsc"}, "/emoji/%F0%9F%AB%A1": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%AB%A1.rsc"}, "/emoji/%F0%9F%AB%B5": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%AB%B5.rsc"}, "/emoji/%F0%9F%AB%B6%F0%9F%8F%BB": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emoji/[emoji]", "dataRoute": "/emoji/%F0%9F%AB%B6%F0%9F%8F%BB.rsc"}, "/hidden-emojis": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/hidden-emojis", "dataRoute": "/hidden-emojis.rsc"}, "/title-generator": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/title-generator", "dataRoute": "/title-generator.rsc"}, "/popular-combos": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/popular-combos", "dataRoute": "/popular-combos.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {"/emoji/[emoji]": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/emoji/([^/]+?)(?:/)?$", "dataRoute": "/emoji/[emoji].rsc", "fallback": null, "dataRouteRegex": "^/emoji/([^/]+?)\\.rsc$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "5e3f7e44728e0210d748fca0f9c1bfe1", "previewModeSigningKey": "53e149a180349552a7ea2ed653ea2861997b6d523034fc15f097d7c26891f595", "previewModeEncryptionKey": "2b6932816397ef656ae5748a721ded524bee99f17fe514bb960b69b57a54757d"}}