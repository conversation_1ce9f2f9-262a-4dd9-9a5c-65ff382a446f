'use client'

import { useParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Search, Filter } from 'lucide-react'
import { UnicodeEmojiCard } from '@/components/unicode-emoji-card'
import { getCategoryByIdOrName, getEmojisByCategory } from '@/lib/unicode-data'
import { useToast } from '@/components/ui/use-toast'
import { useState, useMemo } from 'react'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

export default function CategoryPage() {
  const params = useParams()
  const categoryId = params?.category as string || ''
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null)
  
  const categoryInfo = getCategoryByIdOrName(categoryId)
  const emojis = categoryInfo ? getEmojisByCategory(categoryInfo.name) : []
  
  // Get unique subcategories
  const subcategories = useMemo(() => {
    const uniqueSubcategories = new Set(emojis.map(emoji => emoji.subcategory))
    return Array.from(uniqueSubcategories).sort()
  }, [emojis])
  
  // Filter emojis based on search and subcategory
  const filteredEmojis = useMemo(() => {
    let filtered = emojis
    
    // Filter by subcategory
    if (selectedSubcategory) {
      filtered = filtered.filter(emoji => emoji.subcategory === selectedSubcategory)
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(emoji => 
        emoji.name.toLowerCase().includes(query) ||
        emoji.keywords.some(keyword => keyword.toLowerCase().includes(query)) ||
        emoji.emoji === searchQuery
      )
    }
    
    return filtered
  }, [emojis, searchQuery, selectedSubcategory])
  
  const copyEmoji = (emoji: string) => {
    navigator.clipboard.writeText(emoji)
    toast({
      title: "Copied!",
      description: `${emoji} copied to clipboard`,
      duration: 2000,
    })
  }
  
  if (!categoryInfo) {
    return (
      <div className="container py-20 text-center">
        <h1 className="text-3xl font-bold mb-4">Category not found</h1>
        <Link href="/">
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </Link>
      </div>
    )
  }
  
  return (
    <div className="container py-8">
      {/* Header */}
      <div className="mb-8">
        <Link href="/">
          <Button variant="ghost" size="sm" className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </Link>
        
        <div className="flex items-center gap-4 mb-6">
          <div className="text-6xl">{categoryInfo.icon}</div>
          <div>
            <h1 className="text-4xl font-bold">{categoryInfo.name}</h1>
            <p className="text-xl text-muted-foreground">{categoryInfo.zhName}</p>
            <p className="text-sm text-muted-foreground mt-1">{categoryInfo.description}</p>
          </div>
        </div>
        
        {/* Controls */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <p className="text-lg mb-2">
                <span className="font-semibold">{filteredEmojis.length}</span> of{' '}
                <span className="font-semibold">{emojis.length}</span> emojis
                {selectedSubcategory && (
                  <span className="text-muted-foreground"> in {selectedSubcategory}</span>
                )}
              </p>
            </div>
            
            {/* Search */}
            <div className="relative w-full sm:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                type="text"
                placeholder="Search emojis..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          {/* Subcategory filters */}
          {subcategories.length > 1 && (
            <div className="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant={selectedSubcategory === null ? "default" : "outline"}
                onClick={() => setSelectedSubcategory(null)}
                className="gap-1"
              >
                <Filter className="w-3 h-3" />
                All
              </Button>
              {subcategories.map((subcategory) => {
                const count = emojis.filter(e => e.subcategory === subcategory).length
                return (
                  <Badge
                    key={subcategory}
                    variant={selectedSubcategory === subcategory ? "default" : "outline"}
                    className={cn(
                      "cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors",
                      selectedSubcategory === subcategory && "bg-primary text-primary-foreground"
                    )}
                    onClick={() => setSelectedSubcategory(
                      selectedSubcategory === subcategory ? null : subcategory
                    )}
                  >
                    {subcategory} ({count})
                  </Badge>
                )
              })}
            </div>
          )}
        </div>
      </div>
      
      {/* Emoji Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        {filteredEmojis.map((emoji) => (
          <UnicodeEmojiCard
            key={emoji.code}
            emoji={emoji.emoji}
            name={emoji.name}
            code={emoji.code}
            keywords={emoji.keywords}
            onCopy={copyEmoji}
          />
        ))}
      </div>
      
      {filteredEmojis.length === 0 && (
        <div className="text-center py-20">
          <p className="text-xl text-muted-foreground">
            No emojis found
            {searchQuery && ` matching "${searchQuery}"`}
            {selectedSubcategory && ` in ${selectedSubcategory}`}
          </p>
          <div className="flex gap-2 justify-center mt-4">
            {searchQuery && (
              <Button 
                variant="outline"
                onClick={() => setSearchQuery('')}
              >
                Clear search
              </Button>
            )}
            {selectedSubcategory && (
              <Button 
                variant="outline"
                onClick={() => setSelectedSubcategory(null)}
              >
                Clear filter
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  )
}