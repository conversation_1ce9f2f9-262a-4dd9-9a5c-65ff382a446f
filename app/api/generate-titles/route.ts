import { NextRequest, NextResponse } from 'next/server'

// Prompt templates for different styles
const promptTemplates = {
  funny: (topic: string, emojis: string) => `You are a TikTok content creator expert specializing in viral, humorous content. Generate 5 funny and engaging TikTok video titles based on the topic: "${topic}".

Requirements:
- Use popular TikTok formats like "POV:", "Nobody: Me:", "Tell me you're... without telling me", "When you...", "That moment when..."
- Include 2-3 emojis from this list naturally in each title: ${emojis}
- Make it humorous
- Keep titles under 100 characters for optimal display
- Use internet slang and trending phrases when appropriate
- Each title should evoke laughter or a smile

Output format: Return ONLY the 5 titles, one per line, no numbering or extra formatting.`,

  trending: (topic: string, emojis: string) => `You are a TikTok growth expert. Generate 5 viral-worthy TikTok titles for the topic: "${topic}".

Requirements:
- Use attention-grabbing hooks like "Wait for it...", "You won't believe...", "Watch till the end", "Part 1/2/3"
- Include trending hashtags: #fyp #viral #foryou #foryoupage
- Add urgency with phrases like "MUST WATCH", "Before it's deleted", "Going viral"
- Use 2-3 relevant emojis from: ${emojis}
- Make viewers feel they'll miss out if they don't watch

Output format: Return ONLY the 5 titles, one per line, no numbering or extra formatting.`,

  emotional: (topic: string, emojis: string) => `You are a TikTok storyteller specializing in emotional content. Create 5 emotional and deeply relatable TikTok titles about: "${topic}".

Requirements:
- Use storytelling formats: "Story time:", "This hit different", "Real talk:", "The moment I realized..."
- Make it personal, vulnerable, and authentic
- Include emotional emojis from: ${emojis}
- Add hashtags like #relatable #storytime #emotional #real
- Connect with viewers' emotions and experiences
- Create titles that make people want to share their own stories

Output format: Return ONLY the 5 titles, one per line, no numbering or extra formatting.`,

  tutorial: (topic: string, emojis: string) => `You are a TikTok educator creating helpful content. Generate 5 educational TikTok titles teaching about: "${topic}".

Requirements:
- Use instructional formats: "How to...", "3 ways to...", "The secret to...", "Nobody teaches you this..."
- Include helpful emojis from: ${emojis}
- Add hashtags like #LearnOnTikTok #tutorial #howto #tips
- Promise easy, quick results ("in 30 seconds", "instant results")
- Use numbers for listicle format when applicable
- Make complex things sound simple and achievable

Output format: Return ONLY the 5 titles, one per line, no numbering or extra formatting.`
}

export async function POST(request: NextRequest) {
  try {
    const { topic, style, customEmojis } = await request.json()

    // Validate inputs
    if (!topic || !style) {
      return NextResponse.json(
        { error: 'Topic and style are required' },
        { status: 400 }
      )
    }

    if (!promptTemplates[style as keyof typeof promptTemplates]) {
      return NextResponse.json(
        { error: 'Invalid style selected' },
        { status: 400 }
      )
    }

    // Use custom emojis or default trending ones
    const emojis = customEmojis || '❤️ 💀 😭 🫶 ✨ ⭐️ 🥺 🫡 🫠 🔥 👀 💯'

    // Get the appropriate prompt
    const prompt = promptTemplates[style as keyof typeof promptTemplates](topic, emojis)

    // Call OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': process.env.OPENROUTER_SITE_URL || 'https://tiktokemoji.com',
        'X-Title': process.env.OPENROUTER_SITE_NAME || 'TikTok Emoji Generator',
      },
      body: JSON.stringify({
        model: process.env.OPENROUTER_MODEL || 'google/gemini-flash-2.5',
        messages: [
          {
            role: 'system',
            content: 'You are a TikTok content expert. Generate titles exactly as requested, with no extra formatting or numbering.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.9, // Higher temperature for more creative outputs
        max_tokens: 500,
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      console.error('OpenRouter API error:', error)
      console.error('Response status:', response.status)
      console.error('Response headers:', response.headers)
      return NextResponse.json(
        { error: 'Failed to generate titles. Please try again.', details: error },
        { status: 500 }
      )
    }

    const data = await response.json()
    const generatedText = data.choices[0]?.message?.content || ''
    
    // Split the generated text into individual titles
    const titles = generatedText
      .split('\n')
      .filter((title: string) => title.trim().length > 0)
      .slice(0, 5) // Ensure we only return 5 titles

    // If we didn't get enough titles, return an error
    if (titles.length === 0) {
      return NextResponse.json(
        { error: 'Failed to generate titles. Please try again.' },
        { status: 500 }
      )
    }

    return NextResponse.json({ titles })

  } catch (error) {
    console.error('Error generating titles:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred. Please try again.' },
      { status: 500 }
    )
  }
}

// Rate limiting middleware (simple in-memory implementation)
const requestCounts = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const limit = requestCounts.get(ip)

  if (!limit || now > limit.resetTime) {
    // Reset every hour
    requestCounts.set(ip, {
      count: 1,
      resetTime: now + 3600000 // 1 hour
    })
    return true
  }

  if (limit.count >= 20) { // 20 requests per hour
    return false
  }

  limit.count++
  return true
}

