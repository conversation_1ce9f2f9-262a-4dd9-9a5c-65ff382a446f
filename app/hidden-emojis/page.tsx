import { Metadata } from 'next'
import HiddenEmojisClient from '@/components/hidden-emojis-client'

export const metadata: Metadata = {
  title: '46 TikTok Hidden Emoji Codes - Complete List 2024',
  description: 'Complete list of 46 TikTok hidden emoji codes with meanings and usage. Discover secret emojis like [smile], [happy], [angry] and more. Copy with one click!',
  keywords: ['TikTok hidden emojis', 'secret emoji codes', 'TikTok symbols', '[smile] emoji', '[happy] emoji', 'TikTok comment tricks'],
  openGraph: {
    title: '46 TikTok Hidden Emoji Codes - Complete List',
    description: 'All TikTok secret emoji codes in one place. Learn how to use hidden emojis in comments.',
    type: 'website',
    images: [
      {
        url: '/og-hidden-emojis.png', // 需要创建此图片
        width: 1200,
        height: 630,
        alt: 'TikTok Hidden Emoji Codes Complete List',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '46 TikTok Hidden Emoji Codes',
    description: 'All TikTok secret emoji codes in one place',
    images: ['/og-hidden-emojis.png'],
  },
  alternates: {
    canonical: '/hidden-emojis',
  },
}

export default function HiddenEmojisPage() {
  return <HiddenEmojisClient />
}