"use client"

import { useState } from 'react'
import Link from 'next/link'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>resh<PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/components/ui/use-toast'
import { trendingEmojis, popularCombos } from '@/lib/data'

// Legacy preset title templates (kept for reference, now using AI)
const titleTemplates = {
  funny: [
    "POV: {content} {emoji}",
    "Nobody: Me: {content} {emoji}",
    "Tell me you're {content} without telling me {emoji}",
    "When you {content} {emoji}{emoji}{emoji}",
    "This is why I {content} {emoji}",
  ],
  trending: [
    "{emoji} {content} | Part {number}",
    "Wait for it... {content} {emoji}",
    "{content} {emoji} #fyp #viral",
    "You won't believe {content} {emoji}",
    "{emoji} {content} (MUST WATCH)",
  ],
  emotional: [
    "{content} {emoji} #relatable",
    "Story time: {content} {emoji}",
    "This hit different {emoji} {content}",
    "{content} and I felt that {emoji}",
    "Real talk: {content} {emoji}",
  ],
  tutorial: [
    "How to {content} {emoji} (EASY)",
    "{content} Tutorial {emoji} #LearnOnTikTok",
    "3 ways to {content} {emoji}",
    "{emoji} {content} hack you need",
    "Did you know? {content} {emoji}",
  ],
}

export default function TitleGeneratorPage() {
  const [topic, setTopic] = useState('')
  const [category, setCategory] = useState<keyof typeof titleTemplates>('funny')
  const [generatedTitles, setGeneratedTitles] = useState<string[]>([])
  const [customEmojis, setCustomEmojis] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const { toast } = useToast()

  const generateTitles = async () => {
    if (!topic.trim()) {
      toast({
        title: "Please enter a topic",
        description: "A topic is required to generate titles",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)
    setGeneratedTitles([])

    try {
      const response = await fetch('/api/generate-titles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: topic.trim(),
          style: category,
          customEmojis: customEmojis || undefined,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to generate titles')
      }

      const data = await response.json()
      setGeneratedTitles(data.titles)
      
      toast({
        title: "✨ AI-Generated titles ready!",
        description: `Created ${data.titles.length} unique titles for your TikTok video`,
      })
    } catch (error) {
      console.error('Error generating titles:', error)
      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : "Please try again later",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const copyTitle = (title: string) => {
    navigator.clipboard.writeText(title)
    toast({
      title: "Copied!",
      description: "Title copied to clipboard",
    })
  }

  const refreshTitles = async () => {
    if (topic.trim() && !isGenerating) {
      await generateTitles()
    }
  }

  return (
    <div className="container py-8 max-w-4xl">
      <Link href="/" className="inline-flex items-center text-muted-foreground hover:text-foreground mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Home
      </Link>
      
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
          <Sparkles className="w-10 h-10 text-tiktok-pink" />
          TikTok Title Generator
        </h1>
        <p className="text-muted-foreground text-lg">
          Powered by AI! Enter any topic and get instant viral titles with trending emojis 🚀
        </p>
      </div>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Generation Settings</CardTitle>
          <CardDescription>Customize your title style and emoji preferences</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="topic">Video Topic *</Label>
            <Input
              id="topic"
              placeholder="For example: my cat, today's outfit, cooking..."
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category">Title Style</Label>
            <Select value={category} onValueChange={(value) => setCategory(value as keyof typeof titleTemplates)}>
              <SelectTrigger id="category">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="funny">🤣 Funny Style</SelectItem>
                <SelectItem value="trending">🔥 Trending Style</SelectItem>
                <SelectItem value="emotional">💕 Emotional Style</SelectItem>
                <SelectItem value="tutorial">📚 Tutorial Style</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="emojis">Custom Emojis (Optional)</Label>
            <Textarea
              id="emojis"
              placeholder="Enter emojis you want to use, leave empty to use trending emojis"
              value={customEmojis}
              onChange={(e) => setCustomEmojis(e.target.value)}
              rows={2}
            />
            <p className="text-sm text-muted-foreground">
              Popular choices: {trendingEmojis.slice(0, 10).map(e => e.char).join(' ')}
            </p>
          </div>
          
          <Button 
            onClick={generateTitles} 
            size="lg" 
            variant="tiktok" 
            className="w-full"
            disabled={isGenerating || !topic.trim()}
          >
            {isGenerating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                AI is creating titles...
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4 mr-2" />
                Generate AI Titles
              </>
            )}
          </Button>
        </CardContent>
      </Card>
      

      
      {generatedTitles.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Generated Results</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshTitles}
              disabled={isGenerating}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
              Regenerate
            </Button>
          </div>
          
          <div className="space-y-3">
            {generatedTitles.map((title, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between gap-4">
                    <p className="text-lg flex-1">{title}</p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyTitle(title)}
                    >
                      <Copy className="w-3 h-3 mr-2" />
                      Copy
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
      
      <Card className="mt-12 bg-muted">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            💡 Viral Title Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-muted-foreground">
            <li>• Use trending hooks like POV, Story time to grab attention quickly</li>
            <li>• Add 3-5 relevant emojis to make titles more engaging</li>
            <li>• Keep titles short and powerful, avoid lengthy descriptions</li>
            <li>• Use questions or suspense to spark viewer curiosity</li>
            <li>• Include trending hashtags like #fyp #viral to increase exposure</li>
            <li>• Numbered titles (like "3 tips") tend to perform better</li>
          </ul>
        </CardContent>
      </Card>
      
      <div className="mt-8">
        <h3 className="text-xl font-bold mb-4">🔥 Popular Emoji Combinations</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {popularCombos.slice(0, 6).map((combo) => (
            <Card 
              key={combo.id}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => {
                setCustomEmojis(combo.emojis)
                toast({
                  title: "Added to custom emojis",
                  description: combo.meaning,
                })
              }}
            >
              <CardContent className="pt-4 text-center">
                <div className="text-2xl mb-2">{combo.emojis}</div>
                <p className="text-sm text-muted-foreground">{combo.meaning}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}