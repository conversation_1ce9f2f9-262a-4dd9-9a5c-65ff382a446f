"use client"

import { useState } from 'react'
import Link from 'next/link'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TrendingUp, Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import { popularCombos } from '@/lib/data'

// Combo categories
const comboCategories = {
  all: 'All',
  funny: 'Funny',
  emotional: 'Emotional',
  reaction: 'Reaction',
  trending: 'Trending',
}

export default function PopularCombosPage() {
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof comboCategories>('all')
  const { toast } = useToast()

  const filteredCombos = popularCombos.filter(combo => {
    if (selectedCategory === 'all') return true
    // Simple categorization based on meaning and usage
    const comboText = `${combo.meaning} ${combo.usage}`.toLowerCase()
    
    switch (selectedCategory) {
      case 'funny':
        return comboText.includes('laugh') || comboText.includes('funny') || comboText.includes('dead')
      case 'emotional':
        return comboText.includes('love') || comboText.includes('moved') || comboText.includes('cry')
      case 'reaction':
        return comboText.includes('shock') || comboText.includes('watch') || comboText.includes('surprise')
      case 'trending':
        return comboText.includes('trending') || comboText.includes('popular') || comboText.includes('viral')
      default:
        return true
    }
  })

  const copyCombo = (combo: string) => {
    navigator.clipboard.writeText(combo)
    toast({
      title: "Copied!",
      description: "Emoji combination copied to clipboard",
    })
  }

  // Trending combos (top 6)
  const trendingCombos = popularCombos.slice(0, 6)

  return (
    <div className="container py-8">
      <Link href="/" className="inline-flex items-center text-muted-foreground hover:text-foreground mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Home
      </Link>
      
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4 flex items-center gap-3">
          🔥 Popular Emoji Combinations
        </h1>
        <p className="text-muted-foreground text-lg">
          Explore the most popular emoji combinations on TikTok to make your comments more engaging!
        </p>
      </div>
      
      {/* Today's trending */}
      <Card className="mb-8 border-tiktok-pink/20 bg-gradient-to-br from-pink-50/50 to-blue-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-tiktok-pink" />
            Today's Viral Combos
          </CardTitle>
          <CardDescription>Combinations with surging usage in the last 24 hours</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {trendingCombos.map((combo) => (
              <div 
                key={combo.id}
                className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm"
              >
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{combo.emojis}</span>
                  <div>
                    <p className="font-medium text-sm">{combo.meaning}</p>
                    <p className="text-xs text-muted-foreground">{combo.usage}</p>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => copyCombo(combo.emojis)}
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Browse by category */}
      <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as keyof typeof comboCategories)}>
        <TabsList className="grid w-full max-w-md grid-cols-5">
          {Object.entries(comboCategories).map(([key, label]) => (
            <TabsTrigger key={key} value={key}>
              {label}
            </TabsTrigger>
          ))}
        </TabsList>
        
        <TabsContent value={selectedCategory} className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredCombos.map((combo) => (
              <Card key={combo.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <span className="text-3xl">{combo.emojis}</span>
                        <Badge variant="secondary" className="text-xs">
                          {combo.emojis.length} emojis
                        </Badge>
                      </div>
                      <h3 className="font-semibold text-lg mb-1">{combo.meaning}</h3>
                      <p className="text-sm text-muted-foreground mb-3">{combo.usage}</p>
                      
                      {/* Usage example */}
                      <div className="bg-muted rounded-md p-3 text-sm">
                        <p className="text-muted-foreground mb-1">Example:</p>
                        <p className="italic">
                          {combo.usage.includes('POV') 
                            ? `POV: You see this video ${combo.emojis}`
                            : combo.usage.includes('comment')
                            ? `This is so funny ${combo.emojis}`
                            : `${combo.emojis} This is amazing!`
                          }
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyCombo(combo.emojis)}
                    >
                      <Copy className="w-3 h-3 mr-2" />
                      Copy
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Creative combination tips */}
      <div className="mt-12 space-y-6">
        <h2 className="text-2xl font-bold">💡 Creative Combination Tips</h2>
        
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Repetition for Emphasis</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-3">
                Repeat the same emoji to strengthen emotional expression
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">😭😭😭</span>
                  <span className="text-sm text-muted-foreground">Super moved</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">🔥🔥🔥</span>
                  <span className="text-sm text-muted-foreground">So hot</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">💀💀💀</span>
                  <span className="text-sm text-muted-foreground">I'm dead</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Progressive Expression</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-3">
                Use emojis of different intensities to show emotional progression
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">🙂😊😄</span>
                  <span className="text-sm text-muted-foreground">Getting happier</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">😢😭💔</span>
                  <span className="text-sm text-muted-foreground">Gradually heartbroken</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">😐😑😴</span>
                  <span className="text-sm text-muted-foreground">Getting sleepier</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Storytelling Method</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-3">
                Use emoji combinations to tell a short story
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">👀➡️😱</span>
                  <span className="text-sm text-muted-foreground">Shocked after seeing</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">🏃‍♂️💨🏠</span>
                  <span className="text-sm text-muted-foreground">Rush home</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">📱💬❤️</span>
                  <span className="text-sm text-muted-foreground">Send love message</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Contrast Method</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-3">
                Use contrasting emojis to create humorous effects
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">😇😈</span>
                  <span className="text-sm text-muted-foreground">Surface vs Inner</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">🤓➡️🥳</span>
                  <span className="text-sm text-muted-foreground">Work vs Off work</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-xl">💪😭</span>
                  <span className="text-sm text-muted-foreground">Strong yet fragile</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Usage Suggestions */}
      <Card className="mt-8 bg-muted">
        <CardHeader>
          <CardTitle>🎯 Usage Suggestions</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-muted-foreground">
            <li>• Choose appropriate emoji combinations based on video content to enhance expression</li>
            <li>• Avoid overuse - combinations of 2-5 emojis work best</li>
            <li>• Combine with text to make comments more vivid and interesting</li>
            <li>• Follow the latest trends and update your emoji library regularly</li>
            <li>• Create your own unique combinations to develop a personal style</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}