import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Navigation } from '@/components/navigation'
import { Footer } from '@/components/footer'
import { Toaster } from '@/components/ui/toaster'
import { PerformanceMonitor } from '@/components/performance-monitor'
import { ExtensionErrorHandler } from '@/components/extension-error-handler'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  metadataBase: new URL('https://tiktokemoji.wiki'),
  title: {
    default: 'TikTok Emoji - Hidden Codes & Popular Combinations',
    template: '%s | TikTok Emoji'
  },
  description: 'Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings. Copy with one click to make your TikTok comments more engaging!',
  keywords: ['TikTok emoji', 'hidden emoji codes', 'TikTok symbols', 'emoji combinations', 'TikTok comments', 'secret emojis'],
  authors: [{ name: 'TikTok Emoji Team' }],
  creator: 'TikTok Emoji',
  publisher: 'TikTok Emoji',
  icons: {
    icon: [
      { url: '/icon.png', sizes: '32x32', type: 'image/png' },
      { url: '/icon.svg', type: 'image/svg+xml' },
    ],
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://tiktokemoji.wiki',
    siteName: 'TikTok Emoji',
    title: 'TikTok Emoji - Hidden Codes & Popular Combinations',
    description: 'Discover 46+ TikTok hidden emoji codes, popular emoji combinations, and special meanings.',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'TikTok Emoji - Hidden Codes & Popular Combinations',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TikTok Emoji - Hidden Codes & Popular Combinations',
    description: 'Discover 46+ TikTok hidden emoji codes and popular combinations',
    images: ['/og-image.png'], // 需要创建此图片
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code', // 需要替换为实际的验证码
    // yandex: 'yandex-verification-code',
    // bing: 'bing-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // 基础结构化数据 - 网站信息
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'TikTok Emoji',
    alternateName: 'TikTok Hidden Emoji Codes',
    url: 'https://tiktokemoji.wiki',
    description: 'Discover TikTok hidden emoji codes, popular combinations, and special meanings',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://tiktokemoji.wiki/search?q={search_term_string}'
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'TikTok Emoji',
      url: 'https://tiktokemoji.wiki',
      logo: {
        '@type': 'ImageObject',
        url: 'https://tiktokemoji.wiki/logo.png',
        width: 600,
        height: 60
      }
    },
    sameAs: [
      // 'https://twitter.com/tiktokemoji', // 添加社交媒体链接
      // 'https://facebook.com/tiktokemoji',
    ]
  }

  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </head>
      <body className={inter.className}>
        <ExtensionErrorHandler />
        <Navigation />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
        <Toaster />
        <PerformanceMonitor />
      </body>
    </html>
  )
}