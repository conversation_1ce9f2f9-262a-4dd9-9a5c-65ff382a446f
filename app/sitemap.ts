import { MetadataRoute } from 'next'
import { hiddenEmojis } from '@/lib/data'
import { unicodeCategories } from '@/lib/unicode-data'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://tiktokemoji.com' // 请根据实际域名修改
  
  // 静态页面
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/hidden-emojis`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/popular-combos`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/title-generator`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/search`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
  ]
  
  // 所有emoji详情页
  const emojiPages = hiddenEmojis.map((emoji) => ({
    url: `${baseUrl}/emoji/${encodeURIComponent(emoji.emoji)}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.6,
  }))
  
  // 所有分类页面
  const categoryPages = unicodeCategories.map((category) => ({
    url: `${baseUrl}/category/${encodeURIComponent(category.id)}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }))
  
  return [...staticPages, ...emojiPages, ...categoryPages]
}