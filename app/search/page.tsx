"use client"

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { EmojiCard } from '@/components/emoji-card'
import { hiddenEmojis, trendingEmojis, emojiCategories, popularCombos } from '@/lib/data'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

function SearchContent() {
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(searchParams?.get('q') || '')
  const [selectedCategory, setSelectedCategory] = useState(searchParams?.get('category') || 'all')
  const [activeTab, setActiveTab] = useState('all')

  // Update search parameters
  useEffect(() => {
    const query = searchParams?.get('q')
    const category = searchParams?.get('category')
    if (query) setSearchQuery(query)
    if (category) {
      setSelectedCategory(category)
      setActiveTab('all')
    }
  }, [searchParams])

  // Search hidden emojis
  const searchHiddenEmojis = hiddenEmojis.filter(emoji => {
    const matchesQuery = !searchQuery || 
      emoji.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      emoji.name.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCategory = selectedCategory === 'all' || 
      (selectedCategory === 'hidden')
    
    return matchesQuery && matchesCategory
  })

  // Search regular emojis
  const searchTrendingEmojis = trendingEmojis.filter(emoji => {
    const matchesQuery = !searchQuery || 
      emoji.char.includes(searchQuery) ||
      emoji.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      emoji.meaning.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (emoji.tiktokMeaning && emoji.tiktokMeaning.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || 
      emoji.category.toLowerCase() === selectedCategory.toLowerCase()
    
    return matchesQuery && matchesCategory
  })

  // Search emoji combinations
  const searchCombos = popularCombos.filter(combo => {
    const matchesQuery = !searchQuery || 
      combo.emojis.includes(searchQuery) ||
      combo.meaning.toLowerCase().includes(searchQuery.toLowerCase()) ||
      combo.usage.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesQuery
  })

  const totalResults = searchHiddenEmojis.length + searchTrendingEmojis.length + searchCombos.length

  return (
    <div className="container py-8">
      <Link href="/" className="inline-flex items-center text-muted-foreground hover:text-foreground mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Home
      </Link>
      
      <h1 className="text-4xl font-bold mb-8">Search Emojis</h1>
      
      {/* Search Box */}
      <div className="mb-8">
        <div className="relative max-w-2xl">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
          <Input
            type="search"
            placeholder="Search emojis, codes, or meanings..."
            className="pl-10 text-lg"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      {/* Category Filter */}
      {!searchQuery && selectedCategory !== 'all' && (
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-4">
            <span className="text-muted-foreground">Current category:</span>
            <span className="font-semibold">
              {emojiCategories.find(c => c.id === selectedCategory)?.name || 'All'}
            </span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setSelectedCategory('all')}
            >
              Clear Filter
            </Button>
          </div>
        </div>
      )}
      
      {/* Search Results Count */}
      {searchQuery && (
        <p className="text-muted-foreground mb-6">
          Found <span className="font-semibold text-foreground">{totalResults}</span> results
        </p>
      )}
      
      {/* Search Results */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-4">
          <TabsTrigger value="all">
            All ({totalResults})
          </TabsTrigger>
          <TabsTrigger value="hidden">
            Hidden ({searchHiddenEmojis.length})
          </TabsTrigger>
          <TabsTrigger value="emoji">
            Emojis ({searchTrendingEmojis.length})
          </TabsTrigger>
          <TabsTrigger value="combo">
            Combos ({searchCombos.length})
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="mt-6 space-y-8">
          {/* Hidden emoji results */}
          {searchHiddenEmojis.length > 0 && (
            <section>
              <h2 className="text-2xl font-bold mb-4">Hidden Emoji Codes</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {searchHiddenEmojis.slice(0, 12).map((emoji) => (
                  <EmojiCard
                    key={emoji.code}
                    emoji={emoji.emoji}
                    code={emoji.code}
                    name={emoji.name}
                    usage={emoji.usage}
                  />
                ))}
              </div>
              {searchHiddenEmojis.length > 12 && (
                <div className="mt-4 text-center">
                  <Button variant="outline" onClick={() => setActiveTab('hidden')}>
                    View all {searchHiddenEmojis.length} hidden emojis
                  </Button>
                </div>
              )}
            </section>
          )}
          
          {/* Regular emoji results */}
          {searchTrendingEmojis.length > 0 && (
            <section>
              <h2 className="text-2xl font-bold mb-4">Emojis</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {searchTrendingEmojis.slice(0, 8).map((emoji) => (
                  <Link key={emoji.char} href={`/emoji/${encodeURIComponent(emoji.char)}`}>
                    <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                      <CardContent className="pt-6 text-center">
                        <div className="text-4xl mb-2">{emoji.char}</div>
                        <h3 className="font-semibold">{emoji.name}</h3>
                        <p className="text-sm text-muted-foreground mt-2">{emoji.tiktokMeaning || emoji.meaning}</p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
              {searchTrendingEmojis.length > 8 && (
                <div className="mt-4 text-center">
                  <Button variant="outline" onClick={() => setActiveTab('emoji')}>
                    View all {searchTrendingEmojis.length} emojis
                  </Button>
                </div>
              )}
            </section>
          )}
          
          {/* Emoji combination results */}
          {searchCombos.length > 0 && (
            <section>
              <h2 className="text-2xl font-bold mb-4">Popular Combinations</h2>
              <div className="space-y-3">
                {searchCombos.slice(0, 4).map((combo) => (
                  <Card key={combo.id}>
                    <CardContent className="pt-4">
                      <div className="flex items-center justify-between gap-4">
                        <div className="flex items-center gap-4">
                          <span className="text-2xl">{combo.emojis}</span>
                          <div>
                            <p className="font-semibold">{combo.meaning}</p>
                            <p className="text-sm text-muted-foreground">{combo.usage}</p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => navigator.clipboard.writeText(combo.emojis)}
                        >
                          Copy
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              {searchCombos.length > 4 && (
                <div className="mt-4 text-center">
                  <Button variant="outline" onClick={() => setActiveTab('combo')}>
                    View all {searchCombos.length} combinations
                  </Button>
                </div>
              )}
            </section>
          )}
        </TabsContent>
        
        <TabsContent value="hidden" className="mt-6">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {searchHiddenEmojis.map((emoji) => (
              <EmojiCard
                key={emoji.code}
                emoji={emoji.emoji}
                code={emoji.code}
                name={emoji.name}
                usage={emoji.usage}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="emoji" className="mt-6">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {searchTrendingEmojis.map((emoji) => (
              <Link key={emoji.char} href={`/emoji/${encodeURIComponent(emoji.char)}`}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                  <CardContent className="pt-6 text-center">
                    <div className="text-4xl mb-2">{emoji.char}</div>
                    <h3 className="font-semibold">{emoji.name}</h3>
                    <p className="text-sm text-muted-foreground mt-2">{emoji.tiktokMeaning || emoji.meaning}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="combo" className="mt-6">
          <div className="space-y-3">
            {searchCombos.map((combo) => (
              <Card key={combo.id}>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-4">
                      <span className="text-2xl">{combo.emojis}</span>
                      <div>
                        <p className="font-semibold">{combo.meaning}</p>
                        <p className="text-sm text-muted-foreground">{combo.usage}</p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => navigator.clipboard.writeText(combo.emojis)}
                    >
                      Copy
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
      
      {/* No results message */}
      {totalResults === 0 && searchQuery && (
        <Card className="mt-8">
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">
              No results found for "{searchQuery}"
            </p>
            <div className="space-y-2">
              <p className="text-sm">Try these suggestions:</p>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Check your spelling</li>
                <li>• Use shorter keywords</li>
                <li>• Try searching for the emoji itself</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Popular searches */}
      {!searchQuery && selectedCategory === 'all' && (
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">🔥 Popular Searches</h2>
          <div className="flex flex-wrap gap-2">
            {['smile', 'happy', 'love', '😍', '🔥', 'POV', 'help', 'not me'].map((term) => (
              <Button
                key={term}
                variant="outline"
                size="sm"
                onClick={() => setSearchQuery(term)}
              >
                {term}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="container py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  )
}