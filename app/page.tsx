import { HeroSection } from '@/components/sections/hero-section'
import { HiddenEmojisPreview } from '@/components/sections/hidden-emojis-preview'
import { HowToUse } from '@/components/sections/how-to-use'
import { TrendingContent } from '@/components/sections/trending-content'
import { Categories } from '@/components/sections/categories'

export default function Home() {
  return (
    <main className="overflow-x-hidden" role="main">
      {/* Hero Section - Primary value proposition */}
      <HeroSection />

      {/* Core Features Section */}
      <article aria-labelledby="hidden-emojis-heading">
        <HiddenEmojisPreview />
      </article>

      {/* Tutorial Section */}
      <article aria-labelledby="how-to-use-heading">
        <HowToUse />
      </article>

      {/* Trending Content Section - Combined Popular Combos and Trending Emojis */}
      <article aria-labelledby="trending-content-heading">
        <TrendingContent />
      </article>

      {/* Categories Section */}
      <article aria-labelledby="categories-heading">
        <Categories />
      </article>
    </main>
  )
}