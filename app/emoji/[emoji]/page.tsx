import { Metadata } from 'next'
import EmojiDetailClient from '@/components/emoji-detail-client'
import { trendingEmojis } from '@/lib/data'

interface PageProps {
  params: {
    emoji: string
  }
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const decodedEmoji = decodeURIComponent(params.emoji)
  const emojiData = trendingEmojis.find(e => e.char === decodedEmoji)
  
  const title = emojiData 
    ? `${emojiData.char} ${emojiData.name} - TikTok Emoji Meaning`
    : `${decodedEmoji} - TikTok Emoji`
    
  const description = emojiData
    ? `Learn about ${emojiData.char} ${emojiData.name} on TikTok: ${emojiData.tiktokMeaning || emojiData.meaning}. Discover popular combinations and usage tips.`
    : `Explore ${decodedEmoji} emoji on TikTok. Find meanings, combinations, and usage tips.`

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
      images: [
        {
          url: `/api/og?emoji=${encodeURIComponent(decodedEmoji)}`, // 动态OG图片
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: `/emoji/${params.emoji}`,
    },
  }
}

// Generate static params for common emojis
export async function generateStaticParams() {
  return trendingEmojis.slice(0, 20).map((emoji) => ({
    emoji: encodeURIComponent(emoji.char),
  }))
}

export default function EmojiDetailPage({ params }: PageProps) {
  return <EmojiDetailClient emoji={params.emoji} />
}