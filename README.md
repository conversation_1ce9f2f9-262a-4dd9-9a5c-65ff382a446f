# TikTok Emoji Website

A comprehensive website focused on TikTok emojis, providing hidden emoji code lookup, popular combination recommendations, title generation, and more.

## 🚀 Main Features

### 1. Hidden Emoji Code Collection
- Collection of 46 TikTok platform-exclusive hidden emoji codes (based on Emojipedia.org official data)
- **Real TikTok emoji display**: Shows TikTok platform-exclusive colorful emoji images
- **Smart fallback mechanism**: Automatically displays standard emojis when TikTok images fail to load
- Quick search and one-click copy support
- Detailed usage instructions and examples

### 2. Emoji Detail View
- Display TikTok's special meanings for emojis
- Usage statistics (daily average usage, popularity ranking, etc.)
- Popular combination recommendations
- Usage scenarios and cultural background introduction

### 3. Smart Title Generator
- Automatically generate eye-catching titles based on video themes
- Multiple style options (funny, trending, emotional, tutorial)
- Automatic matching of suitable emojis
- Support for custom emoji preferences

### 4. Site-wide Search Function
- Support for searching hidden emoji codes, emoticons, and popular combinations
- Category filtering and tag filtering
- Categorized display of search results

### 5. Popular Emoji Combinations
- Curated selection of the most popular emoji combinations on TikTok
- Browse by category (funny, emotional, reaction, trending)
- Creative combination technique guidance
- Usage examples and scenario descriptions

## 📁 Page Structure

```
/                          # Homepage
├── /hidden-emojis        # Hidden Emoji Codes Page
├── /emoji/[emoji]        # Emoji Detail Page (dynamic route)
├── /title-generator      # Title Generator
├── /search              # Search Page
└── /popular-combos      # Popular Combinations Page
```

### Page Details

#### Homepage (`/`)
- Hero section: Website introduction and quick access
- Hidden Emoji preview (showing top 16)
- Today's popular emojis
- Usage tutorial (bilingual: Chinese and English)
- Latest trends display
- Category browsing entrance

#### Hidden Emojis Page (`/hidden-emojis`)
- Complete list of 46 hidden emoji codes (source: Emojipedia.org official data)
- Real-time search filtering
- Usage instructions
- Professional tips and precautions

#### Emoji Detail Page (`/emoji/[emoji]`)
- Large-size emoji display
- Unicode information
- TikTok special meanings
- Usage statistics (daily average usage, ranking, emotion percentage)
- Usage scenario tags
- Popular combination recommendations
- Real usage examples
- Cultural background introduction

#### Title Generator (`/title-generator`)
- Theme input box
- Style selection (funny, trending, emotional, tutorial)
- Custom emoji input
- Batch generation results
- One-click copy function
- Viral title techniques
- Popular emoji combination recommendations

#### Search Page (`/search`)
- Global search box
- Categorized results display (all, hidden, emojis, combinations)
- Category filtering
- Popular search recommendations
- No results prompt and suggestions

#### Popular Combinations Page (`/popular-combos`)
- Today's viral combinations display
- Browse by category (all, funny, emotional, reaction, trending)
- Detailed combination descriptions and usage examples
- Creative combination techniques (repetition emphasis, progressive expression, storytelling, contrast)
- Usage suggestions

## 🛠 Technology Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Component Library**: shadcn/ui
- **Font**: Inter
- **Icons**: Lucide React

## 📦 Project Structure

```
tiktokemoji/
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── globals.css        # Global styles
│   ├── hidden-emojis/     # Hidden emojis page
│   ├── emoji/[emoji]/     # Emoji detail page
│   ├── title-generator/   # Title generator page
│   ├── search/            # Search page
│   └── popular-combos/    # Popular combinations page
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   ├── navigation.tsx    # Navigation bar component
│   └── emoji-card.tsx    # Emoji card component
├── lib/                  # Utility functions and data
│   ├── utils.ts         # Utility functions
│   ├── types.ts         # TypeScript type definitions
│   └── data.ts          # Static data (emoji lists, etc.)
├── public/              # Static assets
├── package.json         # Project dependencies
├── tsconfig.json        # TypeScript configuration
├── tailwind.config.ts   # Tailwind CSS configuration
├── next.config.js       # Next.js configuration
└── README.md           # Project documentation
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18.0 or higher
- npm or yarn package manager

### Installation Steps

1. **Clone the project**
```bash
git clone [project-url]
cd tiktokemoji
```

2. **Install dependencies**
```bash
npm install
# or
yarn install
```

3. **Start the development server**
```bash
npm run dev
# or
yarn dev
```

4. **Access the website**
Open your browser and navigate to `http://localhost:3000`

### Production Deployment

1. **Build the project**
```bash
npm run build
# or
yarn build
```

2. **Start the production server**
```bash
npm start
# or
yarn start
```

### Other Commands

- `npm run lint` - Run code linting
- `npm run type-check` - Run TypeScript type checking

## 🎨 Design Features

- **Modern UI**: Features glassmorphism effects and gradient colors
- **Responsive Design**: Perfect adaptation for mobile, tablet, and desktop devices
- **Animations**: Smooth transition animations and hover effects
- **Dark Mode**: Support for system theme switching (configuration reserved)
- **Brand Colors**: Uses TikTok brand colors (pink #fe2c55 and blue #25f4ee)

## 📝 Data Description

- **Hidden Emojis**: 46 official TikTok hidden emoji codes (data source: Emojipedia.org)
- **Popular Emojis**: Curated selection of trending emoticons and their special meanings
- **Emoji Combinations**: 20+ carefully curated popular combinations
- **Category Tags**: 9 main categories (faces, animals, food, etc.)

## 🔧 Configuration Instructions

### Environment Variables
The project currently does not require environment variable configuration. All data is stored in `lib/data.ts`.

### Custom Configuration
- **Color Theme**: Modify color configuration in `tailwind.config.ts`
- **Animation Effects**: Modify animation configuration in `tailwind.config.ts`
- **Data Content**: Modify static data in `lib/data.ts`

## 🤝 Contribution Guidelines

Welcome to submit Issues and Pull Requests to improve this project!

## 📄 License

[Add license information]