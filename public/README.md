# 网站Icon文件放置指南

## 📁 文件结构

请将你的网站icon文件按以下结构放置在此目录中：

```
public/
├── favicon.ico          # 传统favicon (16x16, 32x32, 48x48)
├── icon.png            # 现代浏览器icon (32x32)
├── icon.svg            # 矢量图标 (推荐，可缩放)
├── apple-touch-icon.png # iOS设备图标 (180x180)
├── logo.png            # 网站logo (600x60 或其他尺寸)
└── og-image.png        # 社交媒体分享图 (1200x630)
```

## 🎯 文件规格要求

### favicon.ico
- **尺寸**: 16x16, 32x32, 48x48 (多尺寸ICO文件)
- **格式**: ICO
- **用途**: 浏览器标签页图标

### icon.png
- **尺寸**: 32x32
- **格式**: PNG
- **用途**: 现代浏览器图标

### icon.svg
- **格式**: SVG
- **用途**: 矢量图标，支持深色/浅色模式
- **推荐**: 最佳选择，可完美缩放

### apple-touch-icon.png
- **尺寸**: 180x180
- **格式**: PNG
- **用途**: iOS设备主屏幕图标

### logo.png
- **尺寸**: 600x60 (或其他合适尺寸)
- **格式**: PNG
- **用途**: 结构化数据中的logo

### og-image.png
- **尺寸**: 1200x630
- **格式**: PNG/JPG
- **用途**: 社交媒体分享预览图

## 🔧 配置说明

这些文件已经在 `app/layout.tsx` 中配置好了：

```typescript
icons: {
  icon: [
    { url: '/icon.png', sizes: '32x32', type: 'image/png' },
    { url: '/icon.svg', type: 'image/svg+xml' },
  ],
  shortcut: '/favicon.ico',
  apple: '/apple-touch-icon.png',
},
```

## ✅ 放置完成后

1. 将你的icon文件复制到此目录
2. 确保文件名与上述规格一致
3. 重启开发服务器以查看效果
4. 检查浏览器标签页是否显示新图标

## 💡 提示

- SVG格式的icon.svg是最推荐的，因为它可以完美适应不同尺寸
- 如果你只有一个图标文件，建议至少创建favicon.ico和apple-touch-icon.png
- 可以使用在线工具将你的图标转换为不同格式和尺寸
